
param(
    [string] $targetFilePath = "$PSScriptRoot\..\functions\Register-PsmWithFabricAuthorization.ps1"
)


Describe 'Register-PsmWithFabricAuthorization' {
    BeforeAll {
        . $targetFilePath

        function Write-PsmMessage { }
        Mock Write-PsmMessage { }

    }

    It 'should filter down into a unique list of permission names' {
        # Arrange
        $mockRolePermissionMapping = @(
            @{
                roleName = "Dummydocumenter";
                permissions = "cannotdocument", "canViewTriggers";
                addToGroupNames = $groupName;
            },
            @{
                roleName = "TriggerViewer";
                permissions = "canViewTriggers", "candocument";
                addToGroupNames = $groupName;
            }
        )


        # Act
        $permissions = Get-UniquePermissions -RolePermissionMapping $mockRolePermissionMapping
        # $isObjectMatched = Compare-PsmObject -ReferenceObject $psmWebApiRegistrationBody -DifferenceObject $fakeApiRegistrationResponse

        # Assert
        $permissions | Should -Not -BeNull
        $permissions.Count | Should -Be 3
        $permissions.ContainsKey("cannotdocument") | Should -Be $true
        $permissions.ContainsKey("canViewTriggers") | Should -Be $true
        $permissions.ContainsKey("candocument") | Should -Be $true
        $permissions.ContainsKey("no-item") | Should -Be $false
        # $isObjectMatched | Should -Be $true
    }


}
