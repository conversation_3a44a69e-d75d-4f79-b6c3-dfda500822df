function Assert-PsmLocalComputer {
  # helper function to determine if computer name matches local machine where code is running
  param (
    [Parameter(Mandatory=$true)][String] $ComputerName
  )

  # trim off FQDN if present
  $splitComputerName = $ComputerName.Split('.')
  $trimmedComputerName = $splitComputerName[0]
  # no need to use psremoting if computername is localhsot
  if ($Env:COMPUTERNAME -ne $trimmedComputerName) {
    Write-PsmMessage -Level "Debug" -Message "Script executing on computer [$($Env:COMPUTERNAME)], not [$trimmedComputerName]"
    return $false
  }
  else {
    Write-PsmMessage -Level "Debug" -Message "Verified code is running locally on computer [$trimmedComputerName]"
    return $true
  }
}