param(
    [CmdletBinding()]
    [String]$ReleaseName,
    [String]$ServiceLogonPw
)

$BaseLocation = $PSScriptRoot
Import-Module -Name "$BaseLocation\PsmInstallUtilities" -Force

# validate path to PS modules if provided
if (!$ReleaseName){
  Write-PsmMessage -Level "Warning" -Message "ReleaseName not provided; please provide for better logging"
  $ReleaseName = ""
}

Write-PsmMessage "========== starting REMOVAL of PSM Notifier process ==========="

$config = Get-PsmInstallConfig 
$config.ReleaseName = $ReleaseName

# immediately convert service acct pw to secure string
# this is required due to Azure DevOps lack of support for passing secure strings via pipelines
if ($ServiceLogonPw) {
  Write-PsmMessage "Converting service account password to secure string..."
  [SecureString]$SecureServiceLogonPw = ConvertTo-SecureString $ServiceLogonPw -AsPlainText -Force
  $config.SetNotifierServiceAccountCreds($SecureServiceLogonPw)
}
else {
  Write-PsmMessage -Level "Debug" -Message "No service account password provided to PSM Notifier installer..."
}

Remove-PsmNotifier -PsmInstallConfig $config

Write-PsmMessage "========== Finished REMOVAL of PSM Notifier process ==========="
