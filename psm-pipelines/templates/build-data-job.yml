# ASP.NET Core (.NET Framework)
# Build and test ASP.NET Core projects targeting the full .NET Framework.
# Add steps that publish symbols, save build artifacts, and more:
# https://docs.microsoft.com/azure/devops/pipelines/languages/dotnet-core

jobs:
- job: 'Build_Data'
  variables:
    projFolder: 'safety-surveillance-webapi'
    projIncludePattern: '**/*.csproj'
    projExcludePattern: '!**/*Notifier*.csproj'
    projUnitTestPattern: '**/*UnitTests.csproj'
    projNugetConfigPath: 'safety-surveillance-webapi/SafetySurveillanceWebAPI/Nuget.Config' 
  
  pool:
    vmImage: 'windows-latest'

  steps:
  - task: VSBuild@1
    displayName: 'Build 2012 SQL DACPAC'
    inputs:
      solution: 'safety-surveillance-webapi/SafetySurveillance.Data/SafetySurveillance.Data.sqlproj'
      msbuildArgs: '/p:DSP=Microsoft.Data.Tools.Schema.Sql.Sql110DatabaseSchemaProvider /p:OutputPath=.\bin\Sql2012'
      vsVersion: 'latest'

  - task: VSBuild@1
    displayName: 'Build 2016 SQL DACPAC'
    inputs:
      solution: 'safety-surveillance-webapi/SafetySurveillance.Data/SafetySurveillance.Data.sqlproj'
      msbuildArgs: '/p:DSP=Microsoft.Data.Tools.Schema.Sql.Sql130DatabaseSchemaProvider /p:OutputPath=.\bin\Sql2016'
      vsVersion: 'latest'

  - task: CopyFiles@2
    displayName: 'Copy 2012 SQL DACPAC to publish directory'
    inputs:
      SourceFolder: 'safety-surveillance-webapi\SafetySurveillance.Data\bin\Sql2012'
      Contents: '**\*.dacpac'
      TargetFolder: '$(Build.ArtifactStagingDirectory)\Sql2012'
      CleanTargetFolder: true

  - task: CopyFiles@2
    displayName: 'Copy 2016 SQL DACPAC to publish directory'
    inputs:
      SourceFolder: 'safety-surveillance-webapi\SafetySurveillance.Data\bin\Sql2016'
      Contents: '**\*.dacpac'
      TargetFolder: '$(Build.ArtifactStagingDirectory)\Sql2016'
      CleanTargetFolder: true

  - task: PublishBuildArtifacts@1
    displayName: 'Publish Build Artifacts'
    condition: succeeded()
    inputs:
      PathtoPublish: '$(Build.ArtifactStagingDirectory)'
      publishLocation: 'Container'
      ArtifactName: 'PSM.Data'
