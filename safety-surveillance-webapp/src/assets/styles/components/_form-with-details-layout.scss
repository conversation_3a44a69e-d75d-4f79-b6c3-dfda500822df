$form-padding-left: 42px;

.form-layout {
    .form-header-text {
        display: -webkit-box;
        display: -ms-flexbox;
        display:flex;
        align-items: center;
        color:$hc-label-text;
        font-size: 14px;
        font-weight: 200;
    }

    .form-header-text-readonly {
        color: white;
        font-weight: 600;
    }

    .back-subheader {
        margin-left: 55px;
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        height: 50px;
        align-items: center;
        font-weight: 600;
        font-size: 16px;
        width: 700px;
        padding-top: 5px;

        .button-container {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            align-items: center;
            width: 25px;
            font-size: 28px;
            font-weight: 700;
            height: 80%;
            border-right: 2px solid $gray-400;
            margin-right: 10px;
            a {
                cursor: pointer;
                color: $gray-400;
            }
        }
    }

    .action-bar {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        height: 56px;
        align-items: center;
        background-color: $action-bar-back-ground;
        border-bottom: 2px solid $subheader-border-color;
        padding: 10px $form-padding-left 10px $form-padding-left;
    }

    .page-content {
        display: -webkit-box;
        display: -ms-flexbox;
        display: flex;
        width: 100%;
        font-size: 15px;

        .form-section {
            width: 85%;
            background-color: white;

            fieldset {
                padding-left: $form-padding-left +10;
                padding-top: 10px;
                padding-right: 15px;
            }

            .form-header {
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                width: 100%;
                border-bottom: 1px solid $subheader-border-color;
                background-color: $e-gray;
                height: 62px;
                padding-left: $form-padding-left;
                padding-right: 2%;
                padding-top: 5px;
            }

            .form-header-readonly {
                background: $button-blue;
            }
        }

        .right-section {
            display: -webkit-box;
            display: -ms-flexbox;
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-start;
            width: 40%;
            border-left: 2px solid $subheader-border-color;
            min-height: 119vh;
            background-color: $details-back-ground;

            .content {
                width: 95%;
                height: 100%;
                padding-left: 2%;
            }

            .nav {
                display: -webkit-box;
                display: -ms-flexbox;
                display: flex;
                width: 100%;
                border-bottom: 1px solid $subheader-border-color;
                background-color: $e-gray;
                height: 62px;
                padding-left: $form-padding-left;
                padding-right: 2%;
                padding-top: 5px;

                .tabs > li, .tabs a, .tabs button.btn {
                    color: $hc-label-text;
                    transition: background-color 0.25s;
                    font-size: 15px;
                    font-weight: 600;
                    &.active{
                        color: black !important;
                    }
                }
            }
        }
    }
}
.disabled {
    cursor: not-allowed;
    pointer-events: none;
}
