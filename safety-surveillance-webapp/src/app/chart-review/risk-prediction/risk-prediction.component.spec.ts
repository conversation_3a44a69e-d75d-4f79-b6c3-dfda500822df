import { mock, instance } from 'ts-mockito';
import { RiskPredictionComponent } from './risk-prediction.component';
import { ActivatedRoute } from '@angular/router';
import { CautiService } from 'app/shared/services/cauti.service';
import { SimpleModalService } from 'ngx-simple-modal';

describe('RiskPredictionComponent', () => {
  const activatedRoute = mock(ActivatedRoute);
  const cautiService = mock(CautiService);
  const simpleModalService = mock(SimpleModalService);

  let component: RiskPredictionComponent;

  beforeEach(() => {
    component = new RiskPredictionComponent(instance(activatedRoute), instance(cautiService), instance(simpleModalService));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  
});
