import { CashmereModule } from 'app/shared/ui-modules/cashmere-modules';
import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { QuillModule } from 'ngx-quill'

import { SharedModule } from 'app/shared/shared.module';
import { NgxPopperModule } from 'ngx-popper';
import { RelatedTriggersTemplateModule } from 'app/ui-templates/related-triggers/related-triggers-template.module';
import { Daterangepicker } from 'ng2-daterangepicker';
import { CollapseModule } from 'ngx-bootstrap/collapse';

import { ChartReviewRoutingModule } from './chart-review-routing.module';
import { ChartReviewTriggersComponent } from 'app/chart-review/chart-review-triggers/chart-review-triggers.component';
import { TimelineSummaryComponent } from 'app/chart-review/timeline-summary/timeline-summary.component';
import { TriggerTimelineComponent } from 'app/chart-review/timeline-summary/trigger-timeline/trigger-timeline.component';
import { TriggerTableComponent } from 'app/chart-review/timeline-summary/trigger-table/trigger-table.component';
import { TriggerGraphsModule } from 'app/chart-review/timeline-summary/trigger-graphs-template/trigger-graphs.module';
import { DetailsComponent } from 'app/chart-review/documentation-detail/details/details.component';
import { HistoryComponent } from 'app/chart-review/documentation-detail/history/history.component';
import { TriggerEventAssignmentComponent } from 'app/chart-review/documentation-detail/trigger-event-assignment/trigger-event-assignment.component';
import { ChartReviewComponent } from 'app/chart-review/chart-review.component';
import { DocumentationDetailComponent } from 'app/chart-review/documentation-detail/documentation-detail.component';
import { DeleteAdverseEventComponent } from 'app/chart-review/documentation-detail/delete-adverse-event/delete-adverse-event.component';
import { ErrorDialogComponent } from 'app/chart-review/documentation-detail/error-dialog/error-dialog.component';
import { TriggerEventCommentComponent } from 'app/chart-review/documentation-detail/trigger-event-comment/trigger-event-comment.component';
import { SupportingClinicalDataComponent } from 'app/chart-review/supporting-clinical-data/supporting-clinical-data.component';
import { AllergiesComponent } from 'app/chart-review/supporting-clinical-data/allergies/allergies.component';
import { ActiveMedicationOrdersComponent } from 'app/chart-review/supporting-clinical-data/active-medication-orders/active-medication-orders.component';
import { SurgeryProceduresComponent } from 'app/chart-review/supporting-clinical-data/surgery-procedures/surgery-procedures.component';
import { LabsComponent } from 'app/chart-review/supporting-clinical-data/labs/labs.component';
import { RadiologyComponent } from 'app/chart-review/supporting-clinical-data/radiology/radiology.component';
import { TimelineTemplatesModule } from 'app/timeline-templates/timeline-templates.module';
import { SurveyEventModule } from 'app/survey-event/survey-event.module';
import { RoundModule } from 'app/round/round.module';
import { ClinicalEventTimelineComponent } from './clinical-event-timeline/clinical-event-timeline.component';
import { ClinicalEventDetailTriggerComponent } from './clinical-event-detail/clinical-event-detail-trigger/clinical-event-detail-trigger.component';
import { EventDocumentedComponent } from './clinical-event-detail/event-documented/event-documented.component';
import { UnitTransferComponent } from './clinical-event-detail/unit-transfer/unit-transfer.component';
import { InsertionCatheterCentralLineComponent } from './clinical-event-detail/insertion-catheter-central-line/insertion-catheter-central-line.component';
import { BundleComplianceComponent } from './clinical-event-detail/bundle-compliance/bundle-compliance.component';
import { ConfirmedCautiClabsiComponent } from './clinical-event-detail/confirmed-cauti-clabsi/confirmed-cauti-clabsi.component';
import { RemovalCatheterCentralLineComponent } from './clinical-event-detail/removal-catheter-central-line/removal-catheter-central-line.component';
import { AdmissionComponent } from './clinical-event-detail/admission/admission.component';
import { DischargedComponent } from './clinical-event-detail/discharged/discharged.component';
import { SurgicalProcedureComponent } from './clinical-event-detail/surgical-procedure/surgical-procedure.component';
import { ClinicalEventDetailComponent } from './clinical-event-detail/clinical-event-detail.component';
import { CarouselComponent, CarouselSlideDirective } from './carousel/carousel.component';
import { ClinicalEventDetailHostComponent } from './clinical-event-detail-host/clinical-event-detail-host.component';
import { ClinicalEventDetailModalComponent } from './clinical-event-detail-modal/clinical-event-detail-modal.component';
import { PipesModule } from 'app/shared/pipes/pipes.module';
import { PositiveCultureComponent } from './clinical-event-detail/positive-culture/positive-culture.component';
import { AncComponent } from './clinical-event-detail/anc/anc.component';
import { QualifyingTempComponent } from './clinical-event-detail/qualifying-temp/qualifying-temp.component';
import { AntibioticAdministeredComponent } from './clinical-event-detail/antibiotic-administered/antibiotic-administered.component';
import { BloodCultureDrawnComponent } from './clinical-event-detail/blood-culture-drawn/blood-culture-drawn.component';
import { ConfirmedPressureInjuryComponent } from './clinical-event-detail/confirmed-pressure-injury/confirmed-pressure-injury.component';
import { WoundCareConsultOrderComponent } from './clinical-event-detail/wound-care-consult-order/wound-care-consult-order.component';
import { DocumentedBradenScoreComponent } from './clinical-event-detail/documented-braden-score/documented-braden-score.component';
import { ClinicalEventTimelineContainerComponent } from './clinical-event-timeline/clinical-event-timeline.container';
import { ShiftCompletionPipe } from './pipes/shift-completion.pipe';
import { DisplaySquarePipe } from './pipes/display-square.pipe';
import { IsSplitPipe } from './pipes/is-split.pipe';
import { IsPresentPipe } from './pipes/is-present.pipe';
import { PublicHealthDocumentationDetailComponent } from './public-health-documentation-detail/public-health-documentation-detail.component';
import { PublicHealthDetailsComponent } from './public-health-documentation-detail/public-health-details/public-health-details.component';
import { PublicHealthHistoryComponent } from './public-health-documentation-detail/public-health-history/public-health-history.component';
import { PublicHealthCommentComponent } from './public-health-documentation-detail/public-health-comment/public-health-comment.component';
import { RiskPredictionComponent } from './risk-prediction/risk-prediction.component';
import { DocumentInterventionsModalComponent } from 'app/adverse-events/adverse-events-detail/adverse-events-cauti/cauti-patients-risk/document-interventions-modal/document-interventions-modal.component';
import { RiskPredictionCautiClabsiComponent } from './clinical-event-detail/risk-prediction-cauti-clabsi/risk-prediction-cauti-clabsi.component';
import { ClinicalEventTimelineFlowContainerComponent } from './clinical-event-timeline-flow/clinical-event-timeline-flow-container.component';
import { ClinicalEventTimelineFlowComponent } from './clinical-event-timeline-flow/clinical-event-timeline-flow/clinical-event-timeline-flow.component';
import { RootCauseAnalysisOptionsComponent } from './root-cause-analysis-options/root-cause-analysis-options.component';
import { RootCauseAnalysisOptionsViewComponent } from './root-cause-analysis-options/root-cause-analysis-options-view/root-cause-analysis-options-view.component';
import { RootCauseAnalysisDescriptionComponent } from './root-cause-analysis-options/root-cause-analysis-description/root-cause-analysis-description.component';
import { IncidentTimelineComponent } from './incident-timeline/incident-timeline.component';
import { IncidentTimelineCardComponent } from './incident-timeline/incident-timeline-card/incident-timeline-card.component';
import { DocumentationDetailRedirectComponent } from './documentation-detail-redirect/documentation-detail-redirect.component';
import { FacilitySelectModule } from 'app/shared/facility-select/facility-select.module';
import { TriggerAssigneeRedirectionComponent } from './trigger-assignee-redirection/trigger-assignee-redirection.component';
import { CommentsModule } from 'app/shared/comments/comments.module';
import { ExportPdfConfirmationModalComponent } from './documentation-detail/export-pdf-confirmation-modal/export-pdf-confirmation-modal.component';
import { FileUploadModule } from "../shared/ui-modules/file-upload/file-upload.module";
import { ReviewAttachmentListComponent } from './documentation-detail/review-attachment-list/review-attachment-list.component';
import { IncidentAttachmentListComponent } from './documentation-detail/incident-attachment-list/incident-attachment-list.component';
import { AttachmentHistoryComponent } from './documentation-detail/history/attachment-history/attachment-history.component';
import { AssigneeHistoryComponent } from './documentation-detail/history/assignee-history/assignee-history.component';
import { DocumentationFieldsHistoryComponent } from './documentation-detail/history/documentation-fields-history/documentation-fields-history.component';
import { StatusHistoryComponent } from './documentation-detail/history/status-history/status-history.component';
import { historyReducer } from './documentation-detail/history/store/history.reducer';
import { HistoryEffects } from './documentation-detail/history/store/history.effects';
import { StoreModule } from '@ngrx/store';
import { EffectsModule } from '@ngrx/effects';
import { BannerModule, CheckboxModule, DatepickerModule, PicklistModule, ProgressIndicatorsModule, RadioButtonModule } from '@healthcatalyst/cashmere';
import { DocumentationProgressBarComponent } from './documentation-detail/documentation-progress-bar/documentation-progress-bar.component';
import { triggerReviewReducer } from './state/trigger-review.reducer';
import { TriggerReviewEffects } from './state/trigger-review.effects';
import { TriggerReviewService } from 'app/shared/services/trigger-review.service';
import { AuthService } from 'app/shared/services/auth.service';
import { ConfigService } from 'app/shared/services/app-config.service';
import { ActionPlanningComponent } from './documentation-detail/action-planning/action-planning.component';
import { ActionPlanningIndividualsModalComponent } from './documentation-detail/action-planning/action-planning-individuals-modal/action-planning-individuals-modal.component';
import { ActionPlanningDepartmentsModalComponent } from './documentation-detail/action-planning/action-planning-departments-modal/action-planning-departments-modal.component';
import { ActionPlanningHistoryComponent } from './documentation-detail/history/action-planning-history/action-planning-history.component';
import { ActionPlanningSendDetailsModalComponent } from './documentation-detail/action-planning/action-planning-send-details-modal/action-planning-send-details-modal.component';
import { LinkingHistoryComponent } from './documentation-detail/history/linking-history/linking-history.component';
import { CdkOverlayOrigin, OverlayModule } from '@angular/cdk/overlay';
import {DynamicFormComponent} from "../formly/dynamic-form/dynamic-form.component";
import {HideIfProdDirective} from "../shared/directives/hide-if-prod.directive";

@NgModule({
    imports: [
        CommonModule,
        SharedModule,
        OverlayModule,
        Daterangepicker,
        ChartReviewRoutingModule,
        TriggerGraphsModule,
        FormsModule,
        NgxPopperModule,
        CollapseModule,
        RelatedTriggersTemplateModule,
        TimelineTemplatesModule,
        SurveyEventModule,
        RoundModule,
        PipesModule,
        QuillModule.forRoot(),
        CashmereModule,
        FacilitySelectModule,
        CommentsModule,
        FileUploadModule,
        StoreModule.forFeature('history', historyReducer),
        StoreModule.forFeature('triggerReview', triggerReviewReducer),
        // EffectsModule.forFeature([TriggerReviewEffects]),
        ReactiveFormsModule,
        ProgressIndicatorsModule,
        CheckboxModule,
        RadioButtonModule,
        DatepickerModule,
        PicklistModule,
        BannerModule,
        DynamicFormComponent,
        HideIfProdDirective
    ],
  declarations: [
    ChartReviewTriggersComponent,
    DocumentationDetailComponent,
    DeleteAdverseEventComponent,
    ErrorDialogComponent,
    TriggerEventCommentComponent,
    SupportingClinicalDataComponent,
    AllergiesComponent,
    ActiveMedicationOrdersComponent,
    SurgeryProceduresComponent,
    LabsComponent,
    TimelineSummaryComponent,
    ChartReviewComponent,
    RadiologyComponent,
    TriggerTimelineComponent,
    TriggerTableComponent,
    DetailsComponent,
    HistoryComponent,
    TriggerEventAssignmentComponent,
    ClinicalEventTimelineComponent,
    ClinicalEventTimelineContainerComponent,
    ClinicalEventTimelineFlowContainerComponent,
    ClinicalEventTimelineFlowComponent,
    ClinicalEventDetailComponent,
    AdmissionComponent,
    DischargedComponent,
    ClinicalEventDetailTriggerComponent,
    EventDocumentedComponent,
    UnitTransferComponent,
    InsertionCatheterCentralLineComponent,
    BundleComplianceComponent,
    ConfirmedCautiClabsiComponent,
    RemovalCatheterCentralLineComponent,
    SurgicalProcedureComponent,
    CarouselComponent,
    CarouselSlideDirective,
    ClinicalEventDetailHostComponent,
    ClinicalEventDetailModalComponent,
    PositiveCultureComponent,
    AncComponent,
    QualifyingTempComponent,
    AntibioticAdministeredComponent,
    BloodCultureDrawnComponent,
    ConfirmedPressureInjuryComponent,
    WoundCareConsultOrderComponent,
    DocumentedBradenScoreComponent,
    RiskPredictionComponent,
    IncidentTimelineComponent,
    IncidentTimelineCardComponent,
    IsPresentPipe,
    IsSplitPipe,
    DisplaySquarePipe,
    ShiftCompletionPipe,
    PublicHealthDocumentationDetailComponent,
    PublicHealthDetailsComponent,
    PublicHealthHistoryComponent,
    PublicHealthCommentComponent,
    RiskPredictionCautiClabsiComponent,
    RootCauseAnalysisOptionsComponent,
    RootCauseAnalysisOptionsViewComponent,
    RootCauseAnalysisDescriptionComponent,
    DocumentationDetailRedirectComponent,
    TriggerAssigneeRedirectionComponent,
    ExportPdfConfirmationModalComponent,
    AttachmentHistoryComponent,
    AssigneeHistoryComponent,
    DocumentationFieldsHistoryComponent,
    StatusHistoryComponent,
    DocumentationProgressBarComponent,
    ActionPlanningComponent,
    ActionPlanningIndividualsModalComponent,
    ActionPlanningDepartmentsModalComponent,
    ActionPlanningHistoryComponent,
    ActionPlanningSendDetailsModalComponent,
    LinkingHistoryComponent
  ],
  exports: [
    RootCauseAnalysisOptionsComponent,
    RootCauseAnalysisOptionsViewComponent
  ],
  providers: [TimelineSummaryComponent, TriggerEventAssignmentComponent],
})
export class ChartReviewModule { }
