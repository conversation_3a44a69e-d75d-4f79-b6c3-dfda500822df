import { <PERSON>mpo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>t, ViewChild } from '@angular/core';
import { Router } from '@angular/router';
import {
  HcModal,
  HcPopComponent,
  ModalOptions,
  ModalService,
} from '@healthcatalyst/cashmere';
import { IncidentLinkState } from '@models/incidents/incident-link-state';
import { LinkState } from '@models/incidents/link-state';
import { AccessPermission } from '@models/security/access-permission';
import { LinkIncidentsModalComponent } from 'app/incident/link-incidents-modal/link-incidents-modal.component';
import { LinkIncidentsModalDto } from 'app/incident/link-incidents-modal/types/link-incidents-modal-dto';
import { LinkedIncident } from 'app/incident/link-incidents-modal/types/linked-incident';
import { PatientWatch } from 'app/shared/models/patient-watch';
import { AuthService } from 'app/shared/services/auth.service';
import { LinkIncidentsService } from 'app/shared/services/incidents/link-incidents.service';
import { PermissionsService } from 'app/shared/services/security/permissions.service';
import { BehaviorSubject, Observable } from 'rxjs';
import { mergeMap, shareReplay, take } from 'rxjs/operators';
import { SubSink } from 'subsink';
import { PositiveTrigger } from './../../../shared/models/positive-trigger';
import { SelectedTriggerService } from './../../../shared/services/selected-trigger-service';

@Component({
  selector: 'app-details',
  templateUrl: './details.component.html',
  styleUrls: ['./details.component.scss'],
})
export class DetailsComponent implements OnInit, OnDestroy {
  selectedTrigger: PositiveTrigger;
  showAddressAndPhoneFields = false;
  public readonly canLinkIncidents$ =
    this.permissionService.hasOneOrMorePermissions([
      AccessPermission.canLinkIncidents,
    ]);

  private subs = new SubSink();

  @ViewChild('linkedIncidentsPop') attachedPopover: HcPopComponent;

  public popoverLinkedIncidents$ = new BehaviorSubject<{
    linkedIncidents: IncidentLinkState[];
    currentIncidentId: number;
  }>(null);
  routerLinkArr: string[]=['/patientdetail/triggers'];

  constructor(
    private selectedTriggerService: SelectedTriggerService,
    private router: Router,
    private authService: AuthService,
    private permissionService: PermissionsService,
    private linkedIncidentsService: LinkIncidentsService,
    private readonly _modalService: ModalService
  ) {}

  ngOnDestroy(): void {
    this.subs.unsubscribe();
  }

  ngOnInit() {
    this.selectedTriggerService.newTriggerSelectedSubject.subscribe(c => {
      if (this.selectedTriggerService.selectedPositiveTrigger === undefined) {
        this.router.navigate(['']);
      }

      this.selectedTrigger =
        this.selectedTriggerService.selectedPositiveTrigger;

      this.showAddressAndPhoneFields =
        this.selectedTrigger.encounter.isManual &&
        this.selectedTrigger.encounter.personTypeId == 2;

      // Incidents only
      if (this.selectedTrigger.triggerId === '9999') {
        this.getIncidentLinks();
        this.routerLinkArr=['/patientdetail/incidents'];
      }
    });
  }

  showWatchPatientFlag() {
    if (this.selectedTrigger.triggerId === '9999') {
      return false;
    }

    return true;
  }

  public openIncidentLinking(closePopup: boolean): void {
    let primaryIncidendId;
    let linkIncidentId;

    this.popoverLinkedIncidents$
      .pipe(
        mergeMap(links => {
          const linkedIncidents = [...links.linkedIncidents] || [];
          primaryIncidendId =
            linkedIncidents?.find(i => i.linkState === LinkState.Primary)
              ?.incidentId || this.selectedTrigger.incidentId;

          if (!linkedIncidents || linkedIncidents.length === 0) {
            linkedIncidents.push(
              new IncidentLinkState(primaryIncidendId, LinkState.Primary)
            );
          } else {
            linkIncidentId = linkedIncidents[0].incidentLinkId;
          }

          return this.linkedIncidentsService.getIncidentsForLinking(
            linkedIncidents
          );
        }),
        take(1)
      )
      .subscribe(incidents => {
        const options: ModalOptions = {
          data: new LinkIncidentsModalDto(
            incidents.map(i =>
              LinkedIncident.fromReviewListDto(
                i,
                i.incidentId === this.selectedTrigger.incidentId,
                i.incidentId === primaryIncidendId
              )
            ),
            linkIncidentId
          ),
          ignoreEscapeKey: true,
          ignoreOverlayClick: true,
          size: 'lg',
        };

        if (closePopup) {
          this.attachedPopover.close();
        }

        const subModal: HcModal<LinkIncidentsModalComponent> =
          this._modalService.open(LinkIncidentsModalComponent, options);
        subModal.result.subscribe((response: boolean) =>
          this.getIncidentLinks()
        );
      });
  }

  public getIncidentLinks(): void {
    this.linkedIncidentsService
      .getLinkedIncidents(this.selectedTrigger.incidentId)
      .pipe(take(1))
      .subscribe(incidentLinkStates => {
        this.popoverLinkedIncidents$.next({
          linkedIncidents: incidentLinkStates,
          currentIncidentId: this.selectedTrigger.incidentId,
        });
      });
  }
}
