import { Component, EventEmitter, Input, OnDestroy, OnInit, Output } from '@angular/core';
import { HcModal, ModalOptions, ModalService } from '@healthcatalyst/cashmere';
import { PositiveTrigger } from '@models/positive-trigger';
import { SafetyUser } from '@models/safety-user';
import { AccessPermission } from '@models/security/access-permission';
import { TriggerStatusReference } from '@models/trigger-status-reference';
import { Store } from '@ngrx/store';
import { DestroyableComponent } from '@utils/destroyable.component';
import { getActionPlanningErrorFields, getReviewValidity, getStatusValue, selectActionPlanningVariables, selectCreatedTriggerReview, selectedHasReviewBeenCreated } from 'app/chart-review/state';
import { TriggerReviewActions } from 'app/chart-review/state/actions';
import { getIncidentReviewCreated } from 'app/incident/state';
import { IncidentApiActions } from 'app/incident/state/actions';
import { ActionPlanningService } from 'app/shared/services/action-planning.service';
import { PermissionsService } from 'app/shared/services/security/permissions.service';
import { TriggerReviewService } from 'app/shared/services/trigger-review.service';
import { fadeInOut } from 'assets/animations/fade-in-out';
import { FormTypeBuilder, NgTypeFormGroup } from 'reactive-forms-typed';
import { BehaviorSubject, combineLatest, Observable } from 'rxjs';
import { debounceTime, distinctUntilChanged, filter, map, skip, take } from 'rxjs/operators';
import { ActionPlanningDepartmentsModalComponent } from './action-planning-departments-modal/action-planning-departments-modal.component';
import { ActionPlanningIndividualsModalComponent } from './action-planning-individuals-modal/action-planning-individuals-modal.component';
import { ActionPlanning } from './types/action-planning';
import { ActionPlanningErrorFields } from './types/action-planning-error-fields';
import { ActionPlanningForm } from './types/action-planning-form';
import { ActionPlanningIncidentCategorization } from './types/action-planning-incident-categorization';
import { ActionPlanningIntervention } from './types/action-planning-intervention';
import { ActionPlanningInterventionStrength } from './types/action-planning-intervention-strength';
import { GetActionPlanningDepartmentsModal } from './types/get-action-planning-departments';
import { GetActionPlanningIndividualsModal } from './types/get-action-planning-individuals';

@Component({
  selector: 'action-planning',
  templateUrl: './action-planning.component.html',
  styleUrls: ['./action-planning.component.scss'],
  animations: [fadeInOut]
})
export class ActionPlanningComponent extends DestroyableComponent implements OnInit, OnDestroy {

  @Input() public selectedTrigger: PositiveTrigger;
  @Input() public reviewId: number;

  public form: NgTypeFormGroup<ActionPlanningForm>;

  public interventions$: BehaviorSubject<ActionPlanningIntervention[]> = new BehaviorSubject([]);
  public categorizations$: BehaviorSubject<ActionPlanningIncidentCategorization[]> = new BehaviorSubject([]);
  public actionPlanning$: BehaviorSubject<ActionPlanning> = new BehaviorSubject(null);

  public formInformation$ = combineLatest([this.interventions$, this.categorizations$, this.actionPlanning$]).pipe(
    filter(([interventions, categorizations, actionPlanning]) => {
      if (!this.reviewId) {
        return interventions?.length > 0 && categorizations?.length > 0;
      } else {
        return actionPlanning !== null && interventions?.length > 0 && categorizations?.length > 0;
      }
    })
  );

  public readonly canEditTriggerActionPlan$ = this._permissionsService.hasOneOrMorePermissions([AccessPermission.canEditTriggerActionPlan]);
  public readonly canEditIncidentActionPlan$ = this._permissionsService.hasOneOrMorePermissions([AccessPermission.canEditIncidentActionPlan]);

  public readonly canViewTriggerActionPlan$ = this._permissionsService.hasOneOrMorePermissions([AccessPermission.canViewTriggerActionPlan]);
  public readonly canViewIncidentActionPlan$ = this._permissionsService.hasOneOrMorePermissions([AccessPermission.canViewIncidentActionPlan]);

  private stateVariables$ = this._store.select(selectActionPlanningVariables);
  private statusValue$ = this._store.select(getStatusValue);
  private actionPlanningErrorFields$ = this._store.select(getActionPlanningErrorFields);
  private reviewValid$ = this._store.select(getReviewValidity);

  public errorState$ = combineLatest([this.actionPlanningErrorFields$, this.reviewValid$]).pipe(
    map(([errorFields, isValid]) => ({
      description: !isValid && errorFields.description,
      interventions: !isValid && errorFields.interventions,
      interventionStrength: !isValid && errorFields.interventionStrength,
      completionDate: !isValid && errorFields.completionDate,
      eventCategorization: !isValid && errorFields.eventCategorization,
    } as ActionPlanningErrorFields))
  );

  public state$ = combineLatest([this.canEditTriggerActionPlan$, this.canEditIncidentActionPlan$, this.stateVariables$, this.statusValue$, this.errorState$]).pipe(
    map(([canEditTriggerActionPlan, canEditIncidentActionPlan, stateVariables, statusValue, errorState]) => {
      if (this.selectedTrigger.triggerId === '9999') {
        return { canEdit: canEditIncidentActionPlan && statusValue?.statusId !== 5, actionPlanningRequired: stateVariables.actionPlanningRequired, errors: errorState };
      }
      return { canEdit: canEditTriggerActionPlan && statusValue?.statusId !== 5, actionPlanningRequired: stateVariables.actionPlanningRequired, errors: errorState };
    })
  );

  public hasReviewBeenCreated$ = this._store.select(selectedHasReviewBeenCreated);

  private _updateMode = false;

  constructor(
    private readonly _actionPlanningService: ActionPlanningService,
    private readonly _formTypeBuilder: FormTypeBuilder,
    private readonly _modalService: ModalService,
    private readonly _permissionsService: PermissionsService,
    private readonly _store: Store,
    private readonly _triggerReviewService: TriggerReviewService
  ) {
    super();
  }

  public ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  public ngOnInit(): void {
    this._store.dispatch(TriggerReviewActions.setActionPlanningExists({ exists: false }));
    this._store.dispatch(TriggerReviewActions.clearActionPlanningErrorFields());

    this._actionPlanningService.getActionPlanningInterventions().pipe(take(1))
      .subscribe((interventions) => this.interventions$.next(interventions));

    this._actionPlanningService.getActionPlanningIncidentCategorizations().pipe(take(1))
      .subscribe((categorizations) => this.categorizations$.next(categorizations));

    if (this.reviewId) {
      this._actionPlanningService.getActionPlanning(this.reviewId).pipe(take(1))
        .subscribe((actionPlanning) => {
          this.actionPlanning$.next(actionPlanning);
          if (actionPlanning?.reviewId !== null && actionPlanning?.reviewId > 0) {
            this._updateMode = true;
            this._store.dispatch(TriggerReviewActions.setActionPlanningExists({ exists: true }));
          }
        });
    }

    this.formInformation$.pipe(take(1))
      .subscribe(([interventions, categorizations, actionPlanning]) => {
        this.form = this._formTypeBuilder.group<ActionPlanningForm>({
          description: this._formTypeBuilder.control<string>(actionPlanning?.description),
          interventions: this._formTypeBuilder.array<NgTypeFormGroup<ActionPlanningIntervention>>(interventions.map(x => this._formTypeBuilder.group<ActionPlanningIntervention>({
            interventionId: this._formTypeBuilder.control<string>(x.interventionId),
            interventionName: this._formTypeBuilder.control<string>(x.interventionName),
            selected: this._formTypeBuilder.control<boolean>(actionPlanning?.associatedInterventions?.findIndex(z => z.interventionId === x.interventionId) > -1)
          }))),
          hasOtherIntervention: this._formTypeBuilder.control<boolean>(actionPlanning !== null && actionPlanning?.otherIntervention !== null),
          otherIntervention: this._formTypeBuilder.control<string>(actionPlanning?.otherIntervention),
          interventionStrength: this._formTypeBuilder.control<ActionPlanningInterventionStrength>(actionPlanning?.interventionStrength),
          responsibleIndividuals: this._formTypeBuilder.control<SafetyUser[]>(actionPlanning?.responsibleIndividuals?.map(x => x.safetyUser)),
          hasOtherResponsibleIndividual: this._formTypeBuilder.control<boolean>(actionPlanning !== null && actionPlanning?.otherResponsibleIndividual !== null),
          otherResponsibleIndividual: this._formTypeBuilder.control<string>(actionPlanning?.otherResponsibleIndividual),
          completionDate: this._formTypeBuilder.control<Date>(actionPlanning?.completionDate),
          responsibleDepartments: this._formTypeBuilder.control<string[]>(actionPlanning?.responsibleDepartments?.map(x => x.locationName)),
          incidentCategorizations: this._formTypeBuilder.array<NgTypeFormGroup<ActionPlanningIncidentCategorization>>(categorizations.map(x => this._formTypeBuilder.group<ActionPlanningIncidentCategorization>({
            incidentCategorizationId: this._formTypeBuilder.control<string>(x.incidentCategorizationId),
            incidentCategorizationName: this._formTypeBuilder.control<string>(x.incidentCategorizationName),
            selected: this._formTypeBuilder.control<boolean>(actionPlanning?.associatedIncidentCategorizations?.findIndex(z => z.categorizationId === x.incidentCategorizationId) > -1)
          }))),
        });

        this._handleOtherIntervention();
        this._handleOtherResponsibleIndividual();
        this._determineRequiredFields(this.form.value);

        this.form.valueChanges.pipe(
          debounceTime(1000),
          distinctUntilChanged(),
          this.takeUntilDestroyed())
          .subscribe((formChanges) => {

            this._determineRequiredFields(formChanges);

            if (!this.reviewId) {
              this._store.dispatch(TriggerReviewActions.invokeCreateTriggerReview());

              this.hasReviewBeenCreated$.pipe(filter(x => x !== undefined && x !== null), take(1))
                .subscribe((review) => {
                  this.reviewId = review;
                  this._store.dispatch(TriggerReviewActions.reviewHasBeenCreatedCleared());
                  if (review) {
                    const actionPlanningDto = this._mapToActionPlanning(formChanges, review);
                    this._actionPlanningService.createActionPlanning(actionPlanningDto).pipe(take(1))
                      .subscribe(() => {
                        this._updateMode = true;
                        this._store.dispatch(TriggerReviewActions.setActionPlanningExists({ exists: true }));
                        this._updateTimestamp();
                      });
                  }
                });
            } else {
              const actionPlanningDto = this._mapToActionPlanning(formChanges, this.reviewId);
              if (this._updateMode) {
                this._actionPlanningService.updateActionPlanning(actionPlanningDto).pipe(take(1))
                  .subscribe(() => {
                    this._updateTimestamp();
                  });
              } else {
                this._actionPlanningService.createActionPlanning(actionPlanningDto).pipe(take(1))
                  .subscribe(() => {
                    this._updateMode = true;
                    this._store.dispatch(TriggerReviewActions.setActionPlanningExists({ exists: true }));
                    this._updateTimestamp();
                  });
              }
            }
          });
      });
  }

  private _updateTimestamp(): void {
    this._triggerReviewService.updateReviewLastSavedDate(this.reviewId)
      .pipe(take(1))
      .subscribe((date) => this._store.dispatch(TriggerReviewActions.setLastUpdateTimestamp({ timestamp: date })));
  }

  private _handleOtherIntervention(): void {
    const hasOtherIntervention = this.form.get('hasOtherIntervention');
    const otherInterventionControl = this.form.get('otherIntervention');

    if (!hasOtherIntervention.value) {
      otherInterventionControl.disable();
    }

    hasOtherIntervention.valueChanges.subscribe((value) => {
      if (value) {
        otherInterventionControl.enable();
      } else {
        otherInterventionControl.disable();
      }
    });
  }

  private _handleOtherResponsibleIndividual(): void {
    const hasOtherResponsibleIndividual = this.form.get('hasOtherResponsibleIndividual');
    const otherResponsibleIndividual = this.form.get('otherResponsibleIndividual');

    if (!hasOtherResponsibleIndividual.value) {
      otherResponsibleIndividual.disable();
    }

    hasOtherResponsibleIndividual.valueChanges.subscribe((value) => {
      if (value) {
        otherResponsibleIndividual.enable();
      } else {
        otherResponsibleIndividual.disable();
      }
    });
  }

  public openResponsibleIndividualsModal(): void {
    const responsibleIndividualsControl = this.form.get('responsibleIndividuals');
    const options: ModalOptions = {
      data: new GetActionPlanningIndividualsModal(this.selectedTrigger.triggerId, responsibleIndividualsControl.value),
      ignoreEscapeKey: true,
      ignoreOverlayClick: true,
      size: 'lg'
    };
    const subModal: HcModal<ActionPlanningIndividualsModalComponent> = this._modalService.open(ActionPlanningIndividualsModalComponent, options);
    subModal.result.subscribe((users: SafetyUser[]) => {
      if (users) {
        responsibleIndividualsControl.setValue(users);
      }
    });
  }

  public openResponsibleDepartmentsModal(): void {
    const responsibleDepartmentsControl = this.form.get('responsibleDepartments');
    const options: ModalOptions = {
      data: new GetActionPlanningDepartmentsModal(this.selectedTrigger.facility, responsibleDepartmentsControl.value),
      ignoreEscapeKey: true,
      ignoreOverlayClick: true,
      size: 'lg'
    };
    const subModal: HcModal<ActionPlanningDepartmentsModalComponent> = this._modalService.open(ActionPlanningDepartmentsModalComponent, options);
    subModal.result.subscribe((departments: string[]) => {
      if (departments) {
        responsibleDepartmentsControl.setValue(departments);
      }
    });
  }

  public clearInterventionStrength(): void {
    this.form.get('interventionStrength').setValue(null);
  }

  private _mapToActionPlanning(form: ActionPlanningForm, reviewId: number): ActionPlanning {
    return {
      reviewId: reviewId,
      description: form.description,
      otherIntervention: form.hasOtherIntervention ? form.otherIntervention : null,
      interventionStrength: form.interventionStrength,
      otherResponsibleIndividual: form.hasOtherResponsibleIndividual ? form.otherResponsibleIndividual : null,
      completionDate: form.completionDate,
      associatedInterventions: form.interventions?.filter(x => x.selected).map(x => ({
        reviewId: reviewId,
        interventionId: x.interventionId,
        intervention: x
      })),
      associatedIncidentCategorizations: form.incidentCategorizations?.filter(x => x.selected).map(x => ({
        reviewId: reviewId,
        categorizationId: x.incidentCategorizationId,
        incidentCategorization: x
      })),
      responsibleIndividuals: form.responsibleIndividuals?.map(x => ({
        reviewId: reviewId,
        userName: x.userName,
        safetyUser: x
      }) || []),
      responsibleDepartments: form.responsibleDepartments?.map(x => ({
        reviewId: reviewId,
        locationName: x
      }) || []),
    } as ActionPlanning;
  }

  private _determineRequiredFields(form: ActionPlanningForm): void {
    const errorFields = {
      description: false,
      interventions: false,
      interventionStrength: false,
      completionDate: false,
      eventCategorization: false,
    } as ActionPlanningErrorFields;

    const requiredFields = 5;
    let completedFields = 5;

    if (!form.description) {
      completedFields -= 1;
      errorFields.description = true;
    }
    if (!form.completionDate) {
      completedFields -= 1;
      errorFields.completionDate = true;
    }
    if (form.interventionStrength === null) {
      completedFields -= 1;
      errorFields.interventionStrength = true;
    }
    if (!form.interventions.filter(x => x.selected)?.length && !form.otherIntervention) {
      completedFields -= 1;
      errorFields.interventions = true;
    }
    if (!form.incidentCategorizations.filter(x => x.selected)?.length) {
      completedFields -= 1;
      errorFields.eventCategorization = true;
    }

    this._store.dispatch(TriggerReviewActions.setActionPlanningRequiredFields({ actionPlanningRequiredFields: requiredFields, actionPlanningCompletedFields: completedFields }));
    this._store.dispatch(TriggerReviewActions.setActionPlanningErrorFields({ fields: errorFields }));
  }

}
