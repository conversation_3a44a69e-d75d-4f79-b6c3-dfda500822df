@import "../../../assets/styles/_colors";
@import "../../../assets/styles/_severity-colors";
@import "../../../assets/styles/_layout";
@import "../../../assets/styles/components/_buttons";
@import "../../../assets/styles/components/_select-list";
@import "../../../assets/styles/components/_tables";

$inputWidth:400px;
$buttonwidth: 120px;
$form-padding-left: 42px;
$body-padding:$form-padding-left+15;

.page-content-container{
    display: -webkit-box;
    display: -ms-flexbox;
    display:flex;
    width: 100%;
    font-size: 15px;
}

.public-health-content{
    width: 60%;
    background-color: white;

    fieldset{
        padding-left: $form-padding-left +10;
        padding-top:10px;
        padding-right: 15px;
    }

}


.view-container{

  padding-left:$form-padding-left
}


.dropdown-content {
  min-height: unset;
  padding-bottom: 0px 10px 0px 50px;
}

.actionBar{
    display: -webkit-box;
    display: -ms-flexbox;
    display:flex;
    align-items: center;
    background-color: $action-bar-back-ground;
    border-bottom: 2px solid $subheader-border-color;
    padding: 10px $form-padding-left 10px $form-padding-left;
}

.actionBarItem{
    width:475px;
    margin-left: 20px;
    display: -webkit-box;
    display: -ms-flexbox;
    display:flex;
}

.statusLabel{
    display: -webkit-box;
    display: -ms-flexbox;
    display:flex;
    align-items: center;
    color:$hc-label-text;
    margin-right: 5px;
    font-size: 14px;
    font-weight: 200;
}

.empty-status{
    color:$hc-label-text;
    font-style: italic;
}

.hc-select{
    background-color: $action-bar-back-ground;
}

.hc-form-field-content-wrapper, .hc-form-field-flex {
  height: 34px;
}

.form-label{
  color:$hc-label-text;
}

.documentation-detail-subheader-content{
    margin-left: 55px;
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    height: 50px;
    align-items: center;
    font-weight: 600;
    font-size: 16px;
    width:700px;
    padding-top: 5px;
}

.back-to-triggers{
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    align-items: center;
    width:25px;
    font-size: 28px;
    font-weight: 700;
    height: 80%;
    border-right: 2px solid $gray-400;
    margin-right: 10px;
    a{
        cursor: pointer;
        color:$gray-400;
    }
}

.tabs > li, .tabs a, .tabs button.btn {
    color:$hc-label-text;
    transition: background-color 0.25s;
    font-size: 15px;
    font-weight: 600;


&.active{
    color:black !important;
}

}

.event-date {
  margin-left: 15px;
}

.disabled {
    cursor: not-allowed;
    pointer-events: none;
    background-color: #EEEEEE;
}

.select-health-date {
  width: 255px;
}

.calendar{
    color:$primary-blue;
    float:right;
    padding-top: 2px;
}

.public-health-input{
    border-radius: 0px;
}

.public-health-date {
  align-items: center;
  padding-right: 10px;
}

.document-review-nav-container{
    display: -webkit-box;
    display: -ms-flexbox;
    display:flex;
    justify-content: space-between;
    width:100%;
    border-bottom: 1px solid $subheader-border-color;
    background-color: $e-gray;
    height: 61px;
    padding-left: $form-padding-left;
    padding-right: 2%;
    padding-top: 5px;
}

.right-document-details-container {
    display: -webkit-box;
    display: -ms-flexbox;
    display:flex;
    flex-direction:column;
    align-items: flex-start;
    justify-content: flex-start;
    width: 40%;
    border-left: 2px solid $subheader-border-color;
    min-height:119vh;
    background-color: $details-back-ground;
}

.right-document-details-content{
    width: 95%;
    height: 100%;
    padding-left:  2%;
}

.save-group-buttons, .edit-group-buttons {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    flex-direction:row;
    padding-top: 5px;
    padding-bottom: 10px;
}

.saveButton{
    margin-left: 15px;
    width: $buttonwidth;
}

.requiredClass {
    border-color: $red;
    margin-right: 10px;
}

.requiredInput{
    border: 1px solid $red;
}

.btn:focus{
    outline:0;
}

.description-top-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
}

.associated-chips {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  flex-wrap: wrap;
}

.associated-items {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  align-items: flex-start;
  cursor: pointer;

  .non-associated {
    margin: 2px 0 0 0;
  }

  .empty {
    padding-left: 5px;
    color:$hc-label-text;
    font-style: italic;
    font-size: 13px;
    padding-top: 2px;
  }
  .associated-item {
    border: solid $gray-300;
    background-color: $gray-300;
    padding-right: 5px;
    border-radius: 5px;
    margin: 0 2.5px 5px 2.5px;
  }
  .view {
    cursor: default;
  }
}

.dropdown-menu {
    overflow: auto;
}

.div-break {
  padding-top: 15px;
  margin-left: 0px !important;
  padding-left: 0px !important;
  word-wrap: break-word;
  margin-right: 15px;
}

.editDocumentation{
  width: $buttonwidth;
  margin-right: 20px;
}

.uneditable-input {
  cursor: default;
}

.edit-bar {
  background-color: #286C96;
  color: white;
}

.view-mode {
  padding-left: $form-padding-left +10 !important;
}

.helper {
  font-size: 13px;
  color: $shadow-gray;
}

.fifteen-margin-left {
  margin-left: 15px;
}

.pui-input {
  width: 50%;
}


