import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { DatexPipe } from 'app/shared/pipes/datex-pipe';
import { PipesModule } from 'app/shared/pipes/pipes.module';
import { TriggerGraphComponent } from './trigger-graph/trigger-graph.component';
import { SmartViewCoagulationTemplateComponent } from './smartview-coagulation-template/smartview-coagulation-template.component';
import { SmartViewRenalInjuryTemplateComponent } from './smartview-renal-injury-template/smartview-renal-injury-template.component'


@NgModule({
  exports: [TriggerGraphComponent],
  imports: [
    CommonModule,
    PipesModule
  ],
  declarations: [
    TriggerGraphComponent,
    SmartViewCoagulationTemplateComponent,
    SmartViewRenalInjuryTemplateComponent
   ]
})
export class TriggerGraphsModule { }
