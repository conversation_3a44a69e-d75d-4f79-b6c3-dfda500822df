import { GroupUsersComponent } from './user-management/group-users/group-users.component';
import { UserManagementComponent } from './user-management/user-management.component';
import { NgModule } from '@angular/core';
import { Routes, RouterModule } from '@angular/router';
import { AuthGuardService } from 'app/shared/services/auth-guard.service';
import { AdminComponent } from './admin.component';

export const routes: Routes = [
  {
    path: '',
    canActivate: [AuthGuardService],
    children: [
      { path: '', component: AdminComponent},
      { path: 'safety-users', component: UserManagementComponent},
      { path: 'safety-users/group-users', component: GroupUsersComponent},
      { path: 'safety-users/groups', loadChildren: () => import('./group/group.module').then(m => m.GroupModule)},
    ]
  }
];

@NgModule({
  imports: [RouterModule.forChild(routes)],
  exports: [RouterModule]
})

export class AdminRoutingModule { }
