// import { PipesModule } from 'app/shared/pipes/pipes.module';
// import { async, ComponentFixture, TestBed } from '@angular/core/testing';

// import { RelatedTriggerMedMedMedTemplateComponent } from './related-trigger-med-med-med-template.component';

// describe('RelatedTriggerMedMedMedTemplateComponent', () => {
//   let component: RelatedTriggerMedMedMedTemplateComponent;
//   let fixture: ComponentFixture<RelatedTriggerMedMedMedTemplateComponent>;

//   beforeEach(async(() => {
//     TestBed.configureTestingModule({
//       declarations: [ RelatedTriggerMedMedMedTemplateComponent ],
//       imports: [PipesModule]
//     })
//     .compileComponents();
//   }));

//   beforeEach(() => {
//     fixture = TestBed.createComponent(RelatedTriggerMedMedMedTemplateComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
