<div class="multipleTriggerDataFlg">
    <div class="col-md-8">Additional triggering event details to include (optional)
    </div>
    <div class="col-sm-2 trigger-btn">
        <span class="btn btn-link btn-no-style font-thirteen" (click)="selectAllAvailable()">Select All</span>&nbsp;
        <div class="btn-no-style select-divider">|</div>&nbsp;
        <span class="btn btn-link btn-no-style font-thirteen left-align" (click)="selectNoneAvailable()"> Clear All</span>
    </div>
  </div>
  <table class="table table-striped triggerTable">
    <colgroup>
      <col style="width:15px">
      <col>
      <col>
      <col>
      <col>
      <col>
    </colgroup>
    <thead>
      <tr>
        <th></th>
        <th class="sortable" (click)="onClickSort('triggerDts')" [ngClass]="columnStyle('triggerDts')" id="related-trigger-date-time">
            Trigger Date/Time
        </th>
        <th class="header-font" id="related-trigger-current-unit">
            Current Unit
        </th>
        <th class="header-font" id="related-trigger-time-transfer">
            Date/Time of Transfer
        </th>
        <th class="header-font" id="related-trigger-previous-unit">
            Previous Unit
        </th>
        <th id="related-trigger-service">
          Service
        </th>
      </tr>
    </thead>
    <tbody>
      <tr *ngFor="let trigger of relatedTriggerOptions">
        <input type="checkbox" name="includedTrigger" class="included-trigger-checkbox" (click)="onRelatedTriggerSelect(trigger)" [checked]="trigger.selected">
        <td>{{ trigger.triggerDts | datex: 'MM/DD/YYYY HH:mm' }}</td>
        <td>{{trigger.triggerUnitName}}</td>
        <td>{{trigger.triggerDts | datex: 'MM/DD/YYYY HH:mm'}}</td>
        <td>{{trigger.prereqEventUnitName}}</td>
        <td>{{trigger.encounter.serviceLine}}</td>
      </tr>
    </tbody>
  </table>
