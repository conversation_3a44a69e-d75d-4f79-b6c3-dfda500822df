import { AlternateTemplateService } from 'app/ui-templates/alternate-template-service';
import { Component, OnInit } from '@angular/core';
import { RelatedTriggersTemplateBase } from '../related-triggers-template-base';

@Component({
  selector: 'related-trigger-event-prereq-event-template',
  templateUrl: './related-trigger-event-prereq-event-template.component.html',
  styleUrls: ['./related-trigger-event-prereq-event-template.component.scss']
})
export class RelatedTriggerEventPrereqEventTemplateComponent extends RelatedTriggersTemplateBase implements OnInit {
  useAlternateTemplateBinding = false;

  constructor(private alternateTemplateService: AlternateTemplateService){
    super();
  }

  ngOnInit(): void {
    this.useAlternateTemplateBinding = this.alternateTemplateService.isTriggerIdInAlternate(this.relatedTriggerOptions[0].triggerId);
  }
}
