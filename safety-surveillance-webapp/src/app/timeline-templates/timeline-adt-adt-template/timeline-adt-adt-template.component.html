
<div class="timeline-template-container">
  <div class="timeline-row">
   <div class="thirds-content">
     Current Unit:
     <div class="template-data-content current-unit">
       {{trigger.triggerUnitName}}
     </div>
   </div>
   <div class="thirds-content">
      Date/Time of Transfer:
      <div class="template-data-content transfer-date">
          {{trigger.triggerDts | datex: 'MM/DD/YYYY HH:mm'}}
      </div>
    </div>
    <div class="thirds-content">
        Previous Unit:
        <div class="template-data-content previous-unit">
            {{trigger.prereqEventUnitName}}
        </div>
      </div>
  </div>
  <div class="timeline-row">
      <div class="left-content">
    <div>Triggering Service Line:</div>
    <div class="template-data-content trigger-service">{{trigger.encounter.serviceLine}}</div>
      </div>
  </div>
</div>
