<div class="timeline-template-container">
    <div class="timeline-row">
      <div class="half-content">
        <div>Lab Value:</div>
        <div class="template-data-content lab-value">
            <span *ngIf="useAlternateTemplateBinding">{{trigger.prereqEventDsc}} {{trigger.prereqEventValueDsc}}</span>
            <span *ngIf="!useAlternateTemplateBinding">{{trigger.triggerDsc}} {{trigger.triggerValueDsc}}</span>
        </div>
        <div class="return-procedure-date">
            <span *ngIf="useAlternateTemplateBinding">({{trigger.prereqEventDts | datex: 'MM/DD/YYYY HH:mm'}})</span>
            <span *ngIf="!useAlternateTemplateBinding">({{trigger.triggerDts | datex: 'MM/DD/YYYY HH:mm'}})</span>
        </div>
      </div>
      <div class="half-content">
        <div>Medication Administered:</div>
        <div class="template-data-content med-administered">
          <div *ngIf="useAlternateTemplateBinding">{{trigger.triggerDsc}} {{trigger.triggerValueDsc}} {{trigger.triggerUnitDsc}}</div>
          <div *ngIf="!useAlternateTemplateBinding">{{trigger.prereqEventDsc}} {{trigger.prereqEventValueDsc}} {{trigger.prereqEventUnitDsc}}</div>
        </div>
        <div class="med-date">
          <span *ngIf="useAlternateTemplateBinding">({{trigger.triggerDts | datex: 'MM/DD/YYYY HH:mm'}})</span>
          <span *ngIf="!useAlternateTemplateBinding">({{trigger.prereqEventDts | datex: 'MM/DD/YYYY HH:mm'}})</span>
        </div>
      </div>
    </div>
    <div class="timeline-row">
        <div  class="half-content">
          Location:
          <div class="template-data-content trigger-location">{{trigger.triggerUnitName}}</div>
        </div>
        <div class="half-content">
          Triggering Service Line:
          <div class="template-data-content trigger-service">{{trigger.encounter.serviceLine}}</div>
        </div>
      </div>
  </div>
