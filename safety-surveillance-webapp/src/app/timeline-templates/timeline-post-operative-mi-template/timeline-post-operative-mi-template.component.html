<div class="timeline-template-container">
  <div class="timeline-row">
      <div class="template-content">
          Lab Value:
          <div class="template-data-content lab-value">
              {{trigger.triggerDsc}} {{trigger.triggerValueDsc}} {{trigger.triggerUnitDsc}}
          </div>
      </div>
      <div class="template-content" style="margin-left:20px;">
         Procedure Type:
          <div class="template-data-content initial-dose">
              {{trigger.relatedEventDsc}}
          </div>
             <div class="initial-date">
              ({{trigger.relatedEventDts | datex: 'MM/DD/YYYY HH:mm'}})
          </div>
      </div>
  </div>
  <div class="timeline-row">
      <div class="template-content">
              <div>Location:</div>
              <span class="template-data-content trigger-location">{{trigger.triggerUnitName}}</span>
      </div>
      <div class="template-content" style="margin-left:20px;">
              <div>Triggering Service Line:</div>
              <span class="template-data-content trigger-service">{{trigger.encounter.serviceLine}}</span>
      </div>
  </div>
</div>
