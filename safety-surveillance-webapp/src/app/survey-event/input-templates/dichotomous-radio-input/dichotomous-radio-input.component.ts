import { Component, OnInit } from "@angular/core";
import { RadioButtonChangeEvent } from "@healthcatalyst/cashmere";
import { SurveyEventFormQuestionOptionData } from "app/shared/models/survey-event/survey-event-form-question-option-data";
import { SurveyEventResponseData } from "app/shared/models/survey-event/survey-event-response-data";
import { SurveyEventEventService } from "app/shared/services/survey-event/survey-event-event-service";
import { InputBaseComponent } from "../input-base/input-base.component";

@Component({
  selector: "dichotomous-radio-input",
  templateUrl: "./dichotomous-radio-input.component.html",
  styleUrls: ["./dichotomous-radio-input.component.scss"],
})
export class DichotomousRadioInputComponent
  extends InputBaseComponent
  implements OnInit
{
  isOtherChecked: boolean;
  isNoneAboveChecked: boolean;
  otherOption: SurveyEventFormQuestionOptionData;
  noneAboveOption: SurveyEventFormQuestionOptionData;

  constructor(private _surveyEventEventService: SurveyEventEventService) {
    super(_surveyEventEventService);
  }

  ngOnInit(): void {
    super.ngOnInit();

    this.disabledChangeEvent.subscribe((enabled) => {
      if (!enabled) {
        this.clearAllOptions();
      }
    });
  }

  getSavedResponse() {
    if (this.response !== undefined && this.response.responseText != null) {
      return this.response.responseText;
    } else {
      return "";
    }
  }

  clearAllOptions() {
    this.response.responseText = "";
    this.saveResponse();
  }

  saveResponse() {
    if (this.question.response === undefined) {
      this.question.response = new SurveyEventResponseData();
      this.question.response.questionId = this.question.questionId;
    }

    if (this.question.response.responseId === undefined) {
      this.question.response.responseId = this.response.responseId;
    }

    this.question.response.additionalText = "";

    this.question.response.responseText = this.response.responseText;

    this.inputChangeEvent.next(this.question.response);
  }

  onSubQuestionInputChangeEvent(response) {
    this.inputChangeEvent.next(response);
  }

  onInputChange(e: RadioButtonChangeEvent) {
    if (this.isReadOnly) {
      return;
    }
    this.saveResponse();
  }
}
