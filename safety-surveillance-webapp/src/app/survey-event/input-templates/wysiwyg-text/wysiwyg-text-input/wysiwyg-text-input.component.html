 <div *ngIf="!isReadOnly; else readOnlyQuill">
  <quill-editor
    [readOnly]="false"
    d="response.responseText"
    #wysiwyg
    [customToolbarPosition]="toolbarPos"
    [(ngModel)]="wysiwygText"
    name="wsywygTextResponse"
    scrollingContainer=".form-container"
    (onContentChanged)="onTextChange($event)" required>
    <div quill-editor-toolbar>
      <span class="ql-formats">
        <button class="ql-bold" [title]="'Bold'"></button>
        <button class="ql-italic" [title]="'Italic'"></button>
        <button class="ql-underline" [title]="'Underline'"></button>
      </span>
      <span class="ql-formats">
        <button class="ql-list" value="ordered"></button>
        <button class="ql-list" value="bullet"></button>
      </span>
    </div>
  </quill-editor>
</div>
<ng-template #readOnlyQuill>
  <quill-editor
    [readOnly]="true"
    d="response.responseText"
    #wysiwyg
    [modules]="{toolbar: false}"
    [placeholder]="readOnlyPlaceholder"
    [(ngModel)]="wysiwygText"
    name="wsywygTextResponse">
  </quill-editor>
</ng-template>
