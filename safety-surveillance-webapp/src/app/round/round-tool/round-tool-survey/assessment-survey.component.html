<div [ngClass]="{'container-mobile' : isMobile, 'survey-event-container-mobile' : isMobile, 'survey-event-container': !isMobile}">
  <div class="questions-container" *ngIf="!isMobile">
    <survey-event-questions
        [surveyEvent]="surveyEvent"
        [form]="forms.parentForm"
        [childForm]="childForm"
        [childSurveyEvents]="surveyEvent.childSurveyEvents.length"
        [selectedQuestion]="selectedQuestion"
        [questionFormatter]="parentFormQuestionFormatter()">
        <ng-template #tpl let-seed *ngIf="forms.parentForm.name==='Mortality Audit' || forms.parentForm.name==='Pressure Injury Assessment'">
          <div *ngIf="childForm" class="childForm">
            <div *ngFor="let childSurvey of surveyEvent.childSurveyEvents; let i = index">
                <survey-event-questions
                    [surveyEvent]="childSurvey"
                    [selectedQuestion]="selectedQuestion"
                    [form]="childForm"
                    [sectionFormatter]="SectionFormatter(i + 1)"
                    [questionFormatter]="questionFormatter(seed+(i * childForm.sections[0].questions.length))"
                    (deleteChildEvent)="onDeleteChildSurvey($event)"
                    ></survey-event-questions>
            </div>
            <a *ngIf="!isReadOnly" class="hc-link-bold" (click)="addChildSurvey()">+ add another {{childForm.name | lowercase}}</a>
          </div>
        </ng-template>
    </survey-event-questions>
    <div *ngIf="childForm && forms.parentForm.name !== 'Mortality Audit' && forms.parentForm.name !== 'Pressure Injury Assessment'" >
      <div *ngFor="let childSurvey of surveyEvent.childSurveyEvents; let i = index">
          <survey-event-questions
              [surveyEvent]="childSurvey"
              [selectedQuestion]="selectedQuestion"
              [form]="childForm"
              [sectionFormatter]="SectionFormatter(i + 1)"
              (deleteChildEvent)="onDeleteChildSurvey($event)"
              ></survey-event-questions>
      </div>
      <a *ngIf="!isReadOnly" class="hc-link-bold" (click)="addChildSurvey()">+ add another {{childForm.name}}</a>
    </div>
  </div>

  <div id="survey-event-form-container" [ngClass]="{'conatiner-mobile' : isMobile, 'form-container': !isMobile }"  (scroll)="onFormScroll($event)">
    <ng-content select="[comment]"></ng-content>
    <survey-event-form #parentForm
      [isReadOnly]="isReadOnly"
      [(surveyEvent)]="surveyEvent"
      [form]="forms.parentForm"
      [childForm]="childForm"
      [childSurveyEvents]="surveyEvent.childSurveyEvents.length"
      [patientState]="roundStateService.patientState"
      [questionFormatter]="parentFormQuestionFormatter()"
      [formInlineDataDic]="formInlineDataDic">
      <ng-template #tpl let-seed *ngIf="forms.parentForm.name==='Mortality Audit' || forms.parentForm.name==='Pressure Injury Assessment'" >
        <div *ngIf="childForm" class="childForm">
          <div *ngFor="let childSurvey of surveyEvent.childSurveyEvents; let i = index">
            <survey-event-form
                [isReadOnly]="isReadOnly"
                [surveyEvent]="childSurvey"
                [form]="childForm"
                [sectionFormatter]="SectionFormatter(i + 1)"
                [questionFormatter]="questionFormatter(seed+(i * childForm.sections[0].questions.length))"
                (inputChanged)="parentForm.saveForm($event)"
                >
            </survey-event-form>
          </div>
          <div class="child-survey-link">
            <a *ngIf="!isReadOnly" class="hc-link-bold" (click)="addChildSurvey()">+ add another {{childForm.name | lowercase}}</a>
          </div>
        </div>
      </ng-template>
    </survey-event-form>
    <div *ngIf="childForm && this.forms.parentForm.name !== 'Mortality Audit' && this.forms.parentForm.name !== 'Pressure Injury Assessment'">
      <div *ngFor="let childSurvey of surveyEvent.childSurveyEvents; let i = index">
        <survey-event-form
            [isReadOnly]="isReadOnly"
            [surveyEvent]="childSurvey"
            [form]="childForm"
            [sectionFormatter]="SectionFormatter(i + 1)"
            (inputChanged)="parentForm.saveForm($event)"
            >
        </survey-event-form>
      </div>
    </div>
    <ng-content select="[attachFiles]"
    ></ng-content>
    <ng-content select="[footer]"></ng-content>
  </div>
</div>
