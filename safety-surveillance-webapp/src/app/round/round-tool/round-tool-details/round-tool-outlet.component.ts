import { Component, ComponentR<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from "@angular/router";
import { DestroyableComponent } from "@utils/destroyable.component";
import { RoundingIds } from "../../../shared/constants/rounding-ids";
import { DynamicRoundDetailDirective } from "../../../shared/directives/round-details/dynamic-round-detail.directive";
import { MortalityAuditv2DetailsComponent } from "../../mortality-auditv2/mortality-auditv2-details.component";
import { RoundPatientData } from "@models/round/round-patient-data";
import { RoundToolDetailsComponent } from "./round-tool-details.component";
import { RoundStateService } from "../../../shared/services/round/round-state-service";
import { ReadmissionAuditDetailsComponent } from 'app/round/readmission-audit-details/readmission-audit-details.component';
import { combineLatest } from 'rxjs';
import { AuditTool } from '@models/configurable-audit/audit-tool';
import { map } from 'rxjs/operators';
import { ConfigurableAuditPatientDetailViewComponent } from 'app/round/configurable-audit/configurable-audit-patient-detail-view/configurable-audit-patient-detail-view.component';
import { Store } from '@ngrx/store';
import { AppState, getCurrentConfigurableAuditPatientDetails } from 'app/round/state';
import { PatientDetailsViewModel } from '@models/configurable-audit/patient-details-view-model';

@Component({
  selector: 'round-tool-outlet',
  templateUrl: './round-tool-outlet.component.html',
  styleUrls: ['./round-tool-outlet.component.scss']
})
export class RoundToolOutletComponent extends DestroyableComponent implements OnInit, OnDestroy {
  patientDataDto: RoundPatientData;
  @ViewChild(DynamicRoundDetailDirective, { static: true }) dynamicRoundDetail!: DynamicRoundDetailDirective;

  private formId: string;
  private heparinAudit: boolean;

  public configurableAuditInformation$ = this._store.select(getCurrentConfigurableAuditPatientDetails);

  constructor(private _roundStateService: RoundStateService,
    private _surveyStateService: RoundStateService,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private readonly _store: Store<AppState>
  ) {
    super();
  }

  ngOnInit(): void {
    combineLatest([
      this._surveyStateService.newAuditSelectedSubject,
      this.configurableAuditInformation$,
      this._activatedRoute.queryParamMap
    ])
      .pipe(
        this.takeUntilDestroyed(),
        map(([newAuditSelected, configurableAuditInformation, queryParams]) => ({
          newAuditSelected,
          configurableAuditInformation,
          queryParams
        }))
      )
      .subscribe(data => {
        this.formId = data.queryParams.get('formId');
        if (!data.configurableAuditInformation?.patientDetails) {
          this.loadOldPatientDetails();
        } else {
          this.loadNewPatientDetails(data.configurableAuditInformation.auditTool, data.configurableAuditInformation.patientDetails);
        }
      });
  }

  private loadNewPatientDetails(auditTool: AuditTool, patientDetails: PatientDetailsViewModel) {
    const viewContainerRef = this.dynamicRoundDetail.viewContainerRef;

    viewContainerRef.clear();
    const componentRef = viewContainerRef.createComponent<ConfigurableAuditPatientDetailViewComponent>(
      ConfigurableAuditPatientDetailViewComponent);
    componentRef.instance.patientDetail = patientDetails.details;
    componentRef.instance.auditToolPatientDetails = auditTool.auditToolPatientDetails;
  }

  private loadOldPatientDetails() {
    this.heparinAudit = this._router.url.indexOf(RoundingIds.heparinFormId.toLowerCase()) !== -1;

    this.patientDataDto = this._roundStateService.patientState;

    const viewContainerRef = this.dynamicRoundDetail.viewContainerRef;

    viewContainerRef.clear();
    let componentRef: ComponentRef<any>;

    switch (this.formId.toLocaleLowerCase()) {
      case RoundingIds.mortalityAuditFormIdV2.toLocaleLowerCase():
        componentRef = viewContainerRef.createComponent<MortalityAuditv2DetailsComponent>(MortalityAuditv2DetailsComponent);
        break;
      case RoundingIds.readmissionAuditFormId.toLocaleLowerCase():
        componentRef = viewContainerRef.createComponent<ReadmissionAuditDetailsComponent>(ReadmissionAuditDetailsComponent);
        break;
      default:
        componentRef = viewContainerRef.createComponent<RoundToolDetailsComponent>(RoundToolDetailsComponent);
        componentRef.instance.heparinAudit = this.heparinAudit
        break;
    }
    componentRef.instance.patient = this.patientDataDto;
  }

  ngOnDestroy() {
    super.ngOnDestroy();
  }
}
