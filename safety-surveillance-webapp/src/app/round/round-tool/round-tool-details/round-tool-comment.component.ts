import { Component, OnInit } from "@angular/core";
import { SurveyEventEventService } from "app/shared/services/survey-event/survey-event-event-service";
import { RoundStateService } from "app/shared/services/round/round-state-service";
import { SurveyEventService } from "app/shared/services/survey-event/survey-event.service";
import { SurveyEventCommentData } from "app/shared/models/survey-event/survey-event-comment-data";
import { SimpleModalService } from "ngx-simple-modal";
import { ErrorModalComponent } from "app/shared/modals/error-modal/error-modal.component";
import { SurveyEventStatusOption } from "app/shared/models/survey-event/survey-event-status-option";
import { CommentChangeService } from "app/shared/services/comments/comment-change.service";
import { BaseComment } from "app/shared/comments/base-comment";

@Component({
    selector: 'round-tool-comment',
    templateUrl: './round-tool-comment.component.html',
    styleUrls: ['./round-tool-comment.component.scss']
  })
export class RoundToolCommentComponent implements OnInit {
  surveyEventId: string;

  surveyEncounterSource: string;

  commentToAdd: string;
  comments: SurveyEventCommentData[] = new Array<SurveyEventCommentData>();
  baseComments: BaseComment[] = new Array<BaseComment>();

  constructor(
    private dialogService: SimpleModalService,
    private roundStateService: RoundStateService,
    private surveyEventService: SurveyEventService,
    private surveyEventEventService: SurveyEventEventService,
    private commentChangeService: CommentChangeService,
  ) { }

  ngOnInit() {
    this.surveyEventId = this.roundStateService.patientState.surveyEventId;
    this.surveyEventEventService.createParentSurveySubscription(data => {
      if (data.surveyEventId !== this.surveyEventId) {
        this.surveyEventId = data.surveyEventId;
        this.loadComments();
      }
    });

    if (this.surveyEventId) {
      this.loadComments();
    }
  }

  loadComments(): void {
    this.surveyEventService.getComments(this.surveyEventId)
      .subscribe(data => {
        this.comments = data ? data : new Array<SurveyEventCommentData>();
        this.baseComments = this._mapToBaseComments(data);
      });
  }

  onSaveComment(): void {
    this.surveyEventEventService.createOrGetSurveyEvent(SurveyEventStatusOption.NotReviewed, '', '', ).then(c =>c
      .subscribe(surveyEvent => {
        this.surveyEventId = surveyEvent.surveyEventId;
        this.surveyEncounterSource = surveyEvent.encounterSource;
        this.surveyEventEventService.sendSurveyEventUpdate(surveyEvent);
        this.surveyEventService.saveComment(this.surveyEventId, this.commentToAdd, this.surveyEncounterSource)
          .subscribe(data => {
            this.commentChangeService.notifyChange();
            this.commentToAdd = null;
            this.loadComments();
          }, error => this.dialogService.addModal(ErrorModalComponent, {
            errorNumber: error.status,
            errorText: error.statusText,
            errorMessage: error.error
          }));
      }));
  }

  private _mapToBaseComments(comments): BaseComment[] {
    return comments.map(comment => new BaseComment(comment.id, comment.comment, comment.safetyUser, comment.commentDate));
  }
}
