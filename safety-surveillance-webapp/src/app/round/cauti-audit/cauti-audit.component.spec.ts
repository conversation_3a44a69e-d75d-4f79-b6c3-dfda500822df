import { mock, instance } from 'ts-mockito';
import { CautiAuditComponent } from './cauti-audit.component';
import { RoundService } from 'app/shared/services/round/round.service';
import { RoundFilterBarService } from 'app/shared/filter/round/round-filter-bar/round-filter-bar-service';
import { FilterChangeService } from 'app/shared/filter/filter-change-service';
import { LocalStorageService } from 'app/shared/services/local-storage.service';
import { Router } from '@angular/router';
import { SortUtil } from 'app/shared/sort-utility/sort-utility';
import { InfiniteScrollUtil } from 'app/shared/utility/infinite-scroll-util';
import { RoundStateService } from 'app/shared/services/round/round-state-service';
import { SelectedTriggerService } from 'app/shared/services/selected-trigger-service';
import { AuditInfo } from '../services/audit-info';
import { SurveyEventService } from 'app/shared/services/survey-event/survey-event.service';
import { PatientEncounterService } from 'app/shared/services/patient-encounter.service';
import { PermissionsService } from 'app/shared/services/security/permissions.service';

describe('CautiAuditComponent', () => {
  const sortUtil = mock(SortUtil);
  const infiniteScrollUtil = mock(InfiniteScrollUtil);
  const localStorageService = mock(LocalStorageService);
  const roundStateService = mock(RoundStateService);
  const roundService = mock(RoundService);
  const roundFilterBarService = mock(RoundFilterBarService);
  const filterChangeService = mock(FilterChangeService);
  const selectedTriggerService = mock(SelectedTriggerService);
  const patientEncounterService = mock(PatientEncounterService);
  const surveyEventService = mock(SurveyEventService);
  const router = mock(Router);
  const auditInfo = mock(AuditInfo);
  const permissionsService = mock(PermissionsService);

  let component: CautiAuditComponent;

  beforeEach(() => {
    component = new CautiAuditComponent(instance(sortUtil)
                                        ,instance(infiniteScrollUtil)
                                        ,instance(localStorageService)
                                        ,instance(roundStateService)
                                        ,instance(roundService)
                                        ,instance(roundFilterBarService)
                                        ,instance(filterChangeService)
                                        ,instance(selectedTriggerService)
                                        ,instance(patientEncounterService)
                                        ,instance(surveyEventService)
                                        ,instance(router)
                                        ,instance(auditInfo)
                                        ,instance(permissionsService)
                                        );
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  
});
