import { Component, Input, Output, EventEmitter } from '@angular/core';
import { SurveyEventData } from '@models/survey-event/survey-event-data';

@Component({
  selector: 'timeline-card',
  templateUrl: './timeline-card.component.html',
  styleUrls: ['./timeline-card.component.scss']
})
export class TimelineCardComponent {

  @Input() public item: SurveyEventData;

  @Output() public viewDocumentation = new EventEmitter();

  constructor() { }
}
