@import "../../../assets/styles/colors";
@import "../../../assets/styles/_layout";
@import "../../../assets/styles/components/_links";
@import "../../../assets/styles/components/_buttons";
@import "../../../assets/styles/components/time-line";

$top-margin: 40px;

.rounding-tab-option-container {
    display: flex;
    display: -webkit-box;
    display: -ms-flexbox;
    justify-content: space-between;
}

.rounding-document-filters {
    margin-left: $left-margin;
    margin-top: $top-margin;
    width: 400px;
}

a.disabled {
    pointer-events: none;
    color: #ccc;
}

button.disabled {
    cursor: not-allowed;
    pointer-events: none;
    background-color: #ccc !important;
    border-color: #ccc !important;
}

.rounding-filter-container {
    display: flex;
    display: -webkit-box;
    display: -ms-flexbox;
    flex-direction: column;
    justify-content: space-between;
    width: 75%;
    margin-top: 30px;
    height: 60px;
}

.rounding-form-navigation {
    display: flex;
    display: -webkit-box;
    display: -ms-flexbox;
    align-content: flex-end;
    flex-direction: column;
    width: 55%;
    margin-top: $top-margin;
}

.rounding-nav-option {
    width: 300px;
    margin-left: 10px;
}

.rounding-title {
    font-weight: 600;
}

.btn-primary {
    width: 100px;
    margin-top: 5px;
    margin-left: 377px;
}

.time-line-container {
    margin-top: $top-margin;
    margin-left: 12px;
    column-span: 2;
}

.rounding-content {
    display: flex;
    display: -webkit-box;
    display: -ms-flexbox;
    flex-direction: column;
}

.rounding-table {
    width: 100%;

    th {
        font-weight: 600;
    }
}

.data-row {
    padding-top: 8px;
}

.rounding-content {
    display: flex;
    display: -webkit-box;
    display: -ms-flexbox;
    background-color: $white;
    margin: 10px;
    box-shadow: 3px 3px 3px #999999;
    padding-left: 10px;
    padding-right: 5px;
    padding-bottom: 5px;
    width: 1000px;
    min-height: 100px;
    padding-top: 15px;
    border-radius: 5px;
    justify-content: space-between;
}
