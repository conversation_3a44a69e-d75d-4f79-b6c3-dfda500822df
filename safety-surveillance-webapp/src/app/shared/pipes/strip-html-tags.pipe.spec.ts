import {stripHtmlTags, StripHtmlTagsPipe} from "./strip-html-tags.pipe";

fdescribe('StripHtmlTagsPipe', () => {
  it('create an instance', () => {
    const pipe = new StripHtmlTagsPipe();
    expect(pipe).toBeTruthy();
  });

  fit('Should return plain text', () => {
    const a = "<p>Hi</p><strong>John</strong><br/>Doe";

    const b = 'HiJohnDoe';
    expect(stripHtmlTags(a)).toBe(b);
  })

  fit('Should return remove Non-Quill html tags', () => {
    const quillStr = '<p>Hi</p><strong>Joe</strong><br/>hello';
    const htmlStr = `<html><div><pre>${quillStr}</pre></div></html>`;

    expect(new StripHtmlTagsPipe().transform(htmlStr)).toBe(quillStr);
  })
});
