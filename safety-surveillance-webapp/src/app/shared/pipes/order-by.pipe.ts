import { Pipe, PipeTransform } from '@angular/core';
import { SortDirection } from "app/adverse-events/adverse-events-detail/adverse-events-cauti/cauti-patients-confirmed/cauti-patient-list/SortDirection";
import { ColumnSortStatus } from "app/adverse-events/adverse-events-detail/adverse-events-cauti/cauti-patients-confirmed/cauti-patient-list/ColumnSortStatus";
import * as _ from 'lodash';

@Pipe({
  name: 'orderBy',
  pure: false,
})
export class OrderByPipe implements PipeTransform {
  transform(list: any[], args: ColumnSortStatus[]) {
    return _.orderBy(
      list,
      args.filter(c => c.direction !== SortDirection.NoSort).map(c => c.name),
      args.filter(c => c.direction !== SortDirection.NoSort).map(c => this.getSortOrderString(c.direction))
    );
  }

  getSortOrderString(direction: SortDirection) {
    switch (direction) {
      case SortDirection.Ascending:
        return 'asc';
      case SortDirection.Descending:
        return 'desc';
      default:
        return 'asc';
    }
  }
}
