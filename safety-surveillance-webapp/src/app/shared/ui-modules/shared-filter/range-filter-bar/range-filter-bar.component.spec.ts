import { mock, instance } from 'ts-mockito';
import { RangeFilterBarComponent } from './range-filter-bar.component';
import { Injector } from '@angular/core';

describe('RangeFilterBarComponent', () => {
  const injector = mock(Injector);
  let component: RangeFilterBarComponent;

  beforeEach(() => {
    component = new RangeFilterBarComponent(instance(injector));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  
});
