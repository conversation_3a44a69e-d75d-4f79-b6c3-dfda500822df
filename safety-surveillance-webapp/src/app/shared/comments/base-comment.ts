import { SafetyUser } from "@models/safety-user";

export class BaseComment {
    public commentId: number;
    public comment: string;
    public safetyUser: SafetyUser;
    public commentDate: Date;
    public commentIsRichText: boolean;

    constructor(commentId: number, comment: string, safetyUser: SafetyUser, commentDate: Date, commentIsRichText = false) {
        this.commentId = commentId;
        this.comment = comment;
        this.safetyUser = safetyUser;
        this.commentDate = commentDate;
        this.commentIsRichText = commentIsRichText;
    }
}