import { Component } from '@angular/core';
import { Router } from '@angular/router';
import { AppState } from '@appStore/app.state';
import { selectUserPreferences } from '@appStore/user-preferences/user-preferences.selectors';
import { Store } from '@ngrx/store';

@Component({
  selector: 'default-facilities-setting',
  templateUrl: './default-facilities-setting.component.html',
  styleUrls: ['./default-facilities-setting.component.scss']
})
export class DefaultFacilitiesSettingComponent {

  public userPreferences$ = this._store.select(selectUserPreferences);

  constructor(
    private readonly _store: Store<AppState>,
    private readonly _router: Router
  ) {}

  public editView(): void {
    this._router.navigate(['/settings/facilities/edit']);
  }

}
