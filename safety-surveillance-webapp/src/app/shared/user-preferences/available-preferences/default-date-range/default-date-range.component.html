<div class="settings-sub">
    <h2 class="setting-header">DEFAULT DATE RANGE</h2>
    <div class="default-tab">
      <span class="content-label">
        Preferred default date range when logging in for Triggers
      </span>
      <div *ngIf="selectedDefaultDateRange">
        {{selectedDefaultDateRange}}
      </div>
      <div *ngIf="!selectedDefaultDateRange">
          no preferences set
      </div>
    </div>
    <button class="btn btn-secondary" (click)="editView()">Edit</button>
  </div>
  
