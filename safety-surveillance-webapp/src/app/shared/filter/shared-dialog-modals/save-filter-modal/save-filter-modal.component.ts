import { Component, OnInit } from '@angular/core';
import { SimpleModalComponent } from 'ngx-simple-modal';
import { SaveFilterDialog } from '../../save-filter-dialog-model';
import { SavedFilter } from '../../../models/saved-filter';
import { SaveFilterModalResult } from './save-filter-modal-result';

@Component({
  selector: 'save-filter-modal',
  templateUrl: './save-filter-modal.component.html',
  styleUrls: ['./save-filter-modal.component.scss']
})

export class SaveFilterModalComponent extends SimpleModalComponent<SaveFilterDialog, SaveFilterModalResult> implements SaveFilterDialog, OnInit {
  public existingSavedFilters: SavedFilter[];

  public value: string;

  public headerTitle = 'Name your filter set';

  public remainingCharacterCount;

  public filterName = '';

  public disableApply: boolean;

  public savedFilterToUpdate;

  public selectedSavedFilter: '--';

  private nameLengthLimit = 100;

  private nullSavedFilter;

  public filterExists: boolean = false;


  constructor() {
    super();
    this.nullSavedFilter = new SavedFilter();
    this.nullSavedFilter.filterName = '--';
  }

  ngOnInit(): void {
    this.remainingCharacterCount = this.nameLengthLimit;

    this.disableApply = true;

    this.savedFilterToUpdate = this.nullSavedFilter;
  }

  cancel() {
    this.result = null;
    super.close();
  }

  onFilterNameChange(text: string) {
    this.filterName = text;
    this.savedFilterToUpdate = this.nullSavedFilter;

    this.remainingCharacterCount = this.nameLengthLimit - text.length;

    this.validateForm();
  }

  saveFilterSet() {
    const modalResult = new SaveFilterModalResult();
    modalResult.fitlerToOverRide = this.savedFilterToUpdate;
    modalResult.newFilterName = this.filterName;
    this.result = modalResult;
    super.close();
  }

  onSavedFilterChange(savedFilter: SavedFilter) {
    this.filterName = '';
    this.savedFilterToUpdate = savedFilter;
    this.validateForm();

  }

  validateForm() {
    if (this.filterName.length === 0 && this.savedFilterToUpdate === this.nullSavedFilter) {
      this.disableApply = true;
    }
    if (this.existingSavedFilters.map(savedFilter => savedFilter.filterName.toLowerCase()).indexOf(this.filterName.toLowerCase()) !== -1) {
      this.disableApply = true;
      this.filterExists = true
    }
    else {
      this.disableApply = false;
      this.filterExists = false;
    }
  }
}

