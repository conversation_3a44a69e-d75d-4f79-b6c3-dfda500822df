import {Compo<PERSON>, OnInit, Content<PERSON>hildren, <PERSON><PERSON><PERSON><PERSON>, ViewChildren} from '@angular/core';
import { Location } from '@angular/common';
import { Subscription } from 'rxjs';
import { SimpleModalService } from 'ngx-simple-modal';
import { ConfirmModalComponent } from 'app/shared/modals/confirm-modal/confirm-modal.component';
import { ConfirmDialogConstants } from 'app/shared/constants/confirm-dialog-constants';
import { RunAdverseEventsFilterService } from '../../adverse-events/adverse-events-bar/run-adverse-events-filter-bar';
import { FilterConfigService } from './filter-config.service';
import { skip } from 'rxjs/operators';
import { SavedFilter } from 'app/shared/models/saved-filter';
import { FilterChangeService } from '../../filter-change-service';
import { SharedFilterBarComponent } from 'app/shared/ui-modules/shared-filter/shared-filter-bar/shared-filter-bar.component';
import { Hac<PERSON>imeRange<PERSON>ilt<PERSON> } from '../../cauti/cauti-filters/cauti-time-range-filter';
import { FilterBarService } from '../../abstracts/filter-bar-service';
import {FILTER_TYPE, LOCAL_STORAGE_CATEGORY} from "../../shared-filter-save/shared-filter-save.component";
import {SavedFilterType} from "@models/saved-filter-type";
import {LocalStorageCategory} from "../../../constants/local-storage-category";
import {TriggersFacilityFilterBodyService} from "../../../../triggers/triggers-filter-body.service";
import {ClabsiFacilityFilterBodyService} from "../clabsi-filters/clabsi-filter-body-service";
import { SavedFilterService } from 'app/shared/services/saved-filter.service';
import { HacFilter } from '../../febrile-neutropenia/filters/hac-filter';

@Component({
  selector: 'clabsi-filter-bar',
  templateUrl: './clabsi-filter-bar.component.html',
  styleUrls: ['./clabsi-filter-bar.component.scss'],
  providers: [
    {provide: TriggersFacilityFilterBodyService, useClass: ClabsiFacilityFilterBodyService},
    {provide: FILTER_TYPE, useValue: SavedFilterType.Clabsi},
    { provide: LOCAL_STORAGE_CATEGORY, useValue: LocalStorageCategory.activeClabsiSavedFilterLocalStorage }
  ]
})
export class ClabsiFilterBarComponent implements OnInit {

  @ContentChildren('bundleFilter') bundleFilters: QueryList<SharedFilterBarComponent>;
  @ContentChildren(SharedFilterBarComponent)
  filters: SharedFilterBarComponent[] = [];

  @ContentChildren(SharedFilterBarComponent)
  phFilters: SharedFilterBarComponent[] = [];

  filterSubscription: Subscription;
  routerSubscription: Subscription;
  resetSubscription: Subscription;

  public selectedFacilities: string[];
  public facilityOptions: string[];
  public activeSavedFilter: string;
  public canSaveFilter: boolean;

  constructor(
    public timeRangeFilter: HacTimeRangeFilter,
    private filterConfigService: FilterConfigService,
    private filterService: FilterBarService,
    private dialogService: SimpleModalService,
    private runAdverseEventsFilterService: RunAdverseEventsFilterService,
    private filterChangeService: FilterChangeService,
    private location: Location,
    private savedFilterService: SavedFilterService,
  ) { }

  ngOnInit() {
    this.initFilters();

    this.filterSubscription = this.filterService.facilitiesSubject.pipe(skip(2)).subscribe(data => {
      this.selectedFacilities = data;
      this.runAdverseEventsFilterService.clearFilters();
    });

    this.filterChangeService.changeEvent.subscribe((value) => {
      if (value === false) {
        this.savedFilterService.filterChanged();
        this.setCanSaveFilter();
      }
      this.setCanSaveFilter();
    });

    this.timeRangeFilter.isSelected = true;
  }

  initFilters() {
    let timeRangeConfig = this.filterConfigService.generalRangeConfig;
    this.filterService.init(timeRangeConfig.filterBarConfig);
    this.timeRangeFilter.init(timeRangeConfig);
  }

  resetFilters(): void {
    this.resetSubscription = this.dialogService.addModal(ConfirmModalComponent, {
      message: ConfirmDialogConstants.ResetFiltersMsg,
      title: ConfirmDialogConstants.ResetFiltersTitle,
      positiveTrigger: null,
      confirmButtonText: null,
      subMessage: null
    }).subscribe((result: boolean) => {
      if (result) {
        const path = this.location.path();
        let dayFilterSet;
        if (path.indexOf('patients') > 0) {
          dayFilterSet = 1;
        }
        this.timeRangeFilter.clearFilter(dayFilterSet);
        this.filters.forEach(f => f.filter.clearFilter());
        this.phFilters.forEach(f => f.clearFilter());
        this.savedFilterService.filterChanged(true);
        this.filterChangeService.notifyChange();
      }
    });
  }

  protected setCanSaveFilter() {
    this.canSaveFilter = true;

    if (this.filters.filter(c => (<HacFilter>c.filter).count > 0).length === 0) {
      this.canSaveFilter = false;
    }
    if (this.activeSavedFilter !== undefined && this.activeSavedFilter.length > 0) {
      this.canSaveFilter = false;
    }

    this.savedFilterService.updateCanSaveFilter(this.canSaveFilter);
  }
}
