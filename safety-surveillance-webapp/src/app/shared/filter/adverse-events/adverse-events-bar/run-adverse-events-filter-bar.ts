import { AdverseEventPatientRaceFilter } from './../adverse-event-filters/adverse-event-patient-race-filter/adverse-event-patient-race-filter';
import { AdverseEventMitigatingFactorsFilter } from './../adverse-event-filters/adverse-event-root-cause-analysis-filter/adverse-event-mitigating-factors-filter.component';
import { AdverseEventContributingFactorsFilter } from '../adverse-event-filters/adverse-event-root-cause-analysis-filter/adverse-event-contributing-factors-filter.component';
import { AdverseEventTypeFilter } from './../adverse-event-filters/adverse-event-complications-filter/adverse-event-complications-filter';
import { AdverseEventsGlycemicFilter } from './../adverse-event-filters/adverse-event-glycemic-management-filter/adverse-event-glycemic-filter';
import { AdverseEventsCoagulationFilter } from './../adverse-event-filters/adverse-event-coagulation-filter/adverse-event-coagulation-filter';
import { AdverseEventUnitFilter } from './../adverse-event-filters/adverse-event-unit-filter/adverse-event-unit-filter';
import { AdverseEventSeverityFilter } from './../adverse-event-filters/adverse-event-severity-filter/adverse-event-severity-filter';
import { AdverseEventsServiceLineFilter } from './../adverse-event-filters/adverse-event-service-line-filter/adverse-events-service-line-filter';
import { Injectable } from '@angular/core';
import { IFilter } from 'app/shared/filter/iFilter';
import { PatientGrouping } from '../../../models/patient-grouping';
import { LocalStorageService } from '../../../services/local-storage.service';
import { LocalStorageCategory } from '../../../constants/local-storage-category';
import { FiltersObject } from '../../../models/filters-object';
import { AdverseEventFilterCreatorUtil } from '../adverse-event-filters/adverse-event-filter-creator-util';
import { AdverseEventsHAITriggerFilter } from '../adverse-event-filters/adverse-event-hai-trigger-filter/adverse-event-hai-trigger-filter';
import { AdverseEventsMedReversalFilter } from '../adverse-event-filters/adverse-event-med-reversal-filter/adverse-event-med-reversal-filter';
import { AdverseEventsPatientCareTriggerFilter } from '../adverse-event-filters/adverse-event-patient-care-trigger-filter/ae-patient-care-trigger-filter';
import { AdverseEventsPediatricFilter } from '../adverse-event-filters/adverse-event-pediatric-filter/ae-pediatric-filter';
import { AdverseEventsPerinatalTriggerFilter } from '../adverse-event-filters/adverse-event-perinatal-care-trigger-filter/ae-perinatal-trigger-filter';
import { AdverseEventsReadmissionFilter } from '../adverse-event-filters/adverse-event-readmission-filter/ae-readmission-filter';
import { AdverseEventsRenalInjuryFilter } from '../adverse-event-filters/adverse-event-renal-injury-filter/ae-renal-injury-filter';
import { AdverseEventsSurgicalFilter } from '../adverse-event-filters/adverse-event-surgical-trigger-filter/ae-surgical-filter';
import { AdverseEventsPainManagementFilter } from '../adverse-event-filters/adverse-event-pain-management-filter/ae-pain-management-filter';
import { AdverseEventRegulatoryReportingFilter } from '../adverse-event-filters/adverse-event-regulatory-reporting-filter/adverse-event-regulatory-reporting-filter';
import { AdverseEventStatusFilter } from '../adverse-event-filters/adverse-event-status-filter/adverse-event-status-filter';
import { AdverseEventAssociatedProviderFilter } from '../adverse-event-filters/adverse-event-associated-provider-filter/adverse-event-associated-provider-filter';
import { AdverseEventAssociatedDeviceFilter } from '../adverse-event-filters/adverse-event-associated-device-filter/adverse-event-associated-device-filter';
import { AdverseEventHospitalAcquiredPoaFilter } from '../adverse-event-filters/adverse-event-ha-poa-filter/adverse-event-ha-poa-filter';
import { AdverseEventPatientClassificationFilter } from '../adverse-event-filters/adverse-event-patient-classification-filter/adverse-event-patient-classification-filter';
import { PublicHealthDetectionManagementEventFilter } from '../../adverse-events-public-health/adverse-event-public-health-filter/detection-management-filter/public-health-detection-management-event-filter';
import { AdverseEventDetectionFilter } from '../adverse-event-filters/adverse-event-root-cause-analysis-filter/adverse-event-detection-filter.component';
import { AdverseEventOrganizationalOutcomesFilter } from '../adverse-event-filters/adverse-event-root-cause-analysis-filter/adverse-event-organizational-outcomes-filter.component';
import { AdverseEventAmelioratingActionsFilter } from '../adverse-event-filters/adverse-event-root-cause-analysis-filter/adverse-event-ameliorating-actions-filter.component';
import { AdverseEventActionsTakenFilter } from '../adverse-event-filters/adverse-event-root-cause-analysis-filter/adverse-event-actions-taken-filter.component';

@Injectable()
export class RunAdverseEventsFilterService {
    filters: IFilter<PatientGrouping>[] = new Array<IFilter<PatientGrouping>>();
    triggerFilters: IFilter<PatientGrouping>[] = new Array<IFilter<PatientGrouping>>();
    nonTriggerFilters: IFilter<PatientGrouping>[] = new Array<IFilter<PatientGrouping>>();

    preCLickParamFilterObjectState: FiltersObject;
    appliedFiltersCount: number = 0;
    keepClickFilters: boolean;

    constructor(
        private adverseEventPatientClassFilter: AdverseEventPatientClassificationFilter,
        private adverseEventPatientRaceFilter: AdverseEventPatientRaceFilter,
        private adverseEventServiceLineFilter: AdverseEventsServiceLineFilter,
        private adverseEventSeverityFilter: AdverseEventSeverityFilter,
        private adverseEventUnitFilter: AdverseEventUnitFilter,
        private adverseEventRegulatoryReportingFilter: AdverseEventRegulatoryReportingFilter,
        private adverseEventStatusFilter: AdverseEventStatusFilter,
        private adverseEventAssociatedProviderFilter: AdverseEventAssociatedProviderFilter,
        private adverseEventAssociatedDeviceFilter: AdverseEventAssociatedDeviceFilter,
        private adverseEventHaPoaFilter: AdverseEventHospitalAcquiredPoaFilter,
        private adverseEventsCoagulationFilter: AdverseEventsCoagulationFilter,
        private adverseEventsGlycemicFilter: AdverseEventsGlycemicFilter,
        private adverseEventsHaiTriggerFilter: AdverseEventsHAITriggerFilter,
        private adverseEventMedReversalFilter: AdverseEventsMedReversalFilter,
        private adverseEventPainManagementFilter: AdverseEventsPainManagementFilter,
        private adverseEventPatientCareTriggerFilter: AdverseEventsPatientCareTriggerFilter,
        private adverseEventPerinatalFilter: AdverseEventsPerinatalTriggerFilter,
        private adverseEventReadmissionFilter: AdverseEventsReadmissionFilter,
        private adverseEventRenalInjuryFilter: AdverseEventsRenalInjuryFilter,
        private adverseEventSurgicalFilter: AdverseEventsSurgicalFilter,
        private adverseEventPediatricFilter: AdverseEventsPediatricFilter,
        private adverseEventComplicationsFilter: AdverseEventTypeFilter,
        private adverseEventsSeverityFilter: AdverseEventSeverityFilter,
        private localStorageService: LocalStorageService,
        private adverseEventCreatorUtil: AdverseEventFilterCreatorUtil,
        private detectionManagement: PublicHealthDetectionManagementEventFilter,
        private adverseEventContributingFactorsFilter: AdverseEventContributingFactorsFilter,
        private adverseEventMitigatingFactorsFilter: AdverseEventMitigatingFactorsFilter,
        private adverseEventDetectionFilter: AdverseEventDetectionFilter,
        private adverseEventOrganizationalOutcomesnFilter: AdverseEventOrganizationalOutcomesFilter,
        private adverseEventAmelioratingActionsFilter: AdverseEventAmelioratingActionsFilter,
        private adverseEventActionsTakenToReduceRisknFilter: AdverseEventActionsTakenFilter,
    ) {

        this.filters.push(adverseEventPatientClassFilter);
        this.filters.push(adverseEventPatientRaceFilter);
        this.filters.push(adverseEventsCoagulationFilter);
        this.filters.push(adverseEventsGlycemicFilter);
        this.filters.push(adverseEventsHaiTriggerFilter);
        this.filters.push(adverseEventMedReversalFilter);
        this.filters.push(adverseEventPainManagementFilter);
        this.filters.push(adverseEventPatientCareTriggerFilter);
        this.filters.push(adverseEventPediatricFilter);
        this.filters.push(adverseEventPerinatalFilter);
        this.filters.push(adverseEventReadmissionFilter);
        this.filters.push(adverseEventRenalInjuryFilter);
        this.filters.push(adverseEventSurgicalFilter);
        this.filters.push(adverseEventRegulatoryReportingFilter);
        this.filters.push(adverseEventStatusFilter);
        this.filters.push(adverseEventAssociatedProviderFilter);
        this.filters.push(adverseEventAssociatedDeviceFilter);
        this.filters.push(adverseEventHaPoaFilter);
        this.filters.push(adverseEventComplicationsFilter);
        this.filters.push(adverseEventSeverityFilter);

        this.filters.push(adverseEventContributingFactorsFilter);
        this.filters.push(adverseEventMitigatingFactorsFilter);
        this.filters.push(adverseEventDetectionFilter);
        this.filters.push(adverseEventOrganizationalOutcomesnFilter);
        this.filters.push(adverseEventAmelioratingActionsFilter);
        this.filters.push(adverseEventActionsTakenToReduceRisknFilter);

        this.triggerFilters.push(adverseEventsCoagulationFilter);
        this.triggerFilters.push(adverseEventsGlycemicFilter);
        this.triggerFilters.push(adverseEventsHaiTriggerFilter);
        this.triggerFilters.push(adverseEventMedReversalFilter);
        this.triggerFilters.push(adverseEventPainManagementFilter);
        this.triggerFilters.push(adverseEventPatientCareTriggerFilter);
        this.triggerFilters.push(adverseEventPediatricFilter);
        this.triggerFilters.push(adverseEventPerinatalFilter);
        this.triggerFilters.push(adverseEventReadmissionFilter);
        this.triggerFilters.push(adverseEventRenalInjuryFilter);
        this.triggerFilters.push(adverseEventSurgicalFilter);

        this.nonTriggerFilters.push(adverseEventContributingFactorsFilter);
        this.nonTriggerFilters.push(adverseEventMitigatingFactorsFilter);
        this.nonTriggerFilters.push(adverseEventDetectionFilter);
        this.nonTriggerFilters.push(adverseEventOrganizationalOutcomesnFilter);
        this.nonTriggerFilters.push(adverseEventAmelioratingActionsFilter);
        this.nonTriggerFilters.push(adverseEventActionsTakenToReduceRisknFilter);

        this.setAppliedFilterCount();
        this.initAdverseEventFilters();
    }

    initAdverseEventFilters() {
      // This will make sure the adverseEventFilter object in LocalStorage has all the properties.
      const localStorageFilterObject = JSON.parse(this.localStorageService.getLocalStorage(LocalStorageCategory.adverseEventFilterLocalStorage));
      const filterObjectWithAllFilters = {...this.adverseEventCreatorUtil.createNewAdverseEventFilters(), ...localStorageFilterObject};

      this.localStorageService.setLocalStorage(LocalStorageCategory.adverseEventFilterLocalStorage, JSON.stringify(filterObjectWithAllFilters));
    }

    setClickFilterParameter(type: string, filterOption: any) {
        let selectedFilters: FiltersObject;

        if (this.localStorageService.getLocalStorage(LocalStorageCategory.adverseEventFilterLocalStorage)) {
            selectedFilters = JSON.parse(this.localStorageService.getLocalStorage(LocalStorageCategory.adverseEventFilterLocalStorage)
            );
        }
        else {
            selectedFilters = this.adverseEventCreatorUtil.createNewAdverseEventFilters();
        }

        this.preCLickParamFilterObjectState = selectedFilters;
        // figure out which param is coming in and set it
        const filterOptions: Array<any> = [filterOption];

        switch (type) {
            case 'unit':
                this.adverseEventUnitFilter.remoteSet(filterOptions);
                break;
            case 'serviceLine':
                this.adverseEventServiceLineFilter.remoteSet(filterOptions);
                break;
            case 'severity':
                this.adverseEventSeverityFilter.remoteSet(filterOptions);
                break;
            case 'provider':
                this.adverseEventAssociatedProviderFilter.remoteSet(filterOptions);
                break;
            case 'device':
                this.adverseEventAssociatedDeviceFilter.remoteSet(filterOptions);
                break;
            case 'poa':
                this.adverseEventHaPoaFilter.remoteSet(filterOptions);
                break;
            case 'Coagulation':
                this.adverseEventsCoagulationFilter.remoteSet(filterOptions);
                break;
            case 'Glycemic':
                this.adverseEventsGlycemicFilter.remoteSet(filterOptions);
                break;
            case 'Healthcare Associated Infections':
                this.adverseEventsHaiTriggerFilter.remoteSet(filterOptions);
                break;
            case 'Medication Reversal':
                this.adverseEventMedReversalFilter.remoteSet(filterOptions);
                break;
            case 'Patient Care':
                this.adverseEventPatientCareTriggerFilter.remoteSet(filterOptions);
                break;
            case 'Pediatric':
                this.adverseEventPediatricFilter.remoteSet(filterOptions);
                break;
            case 'Perinatal':
                this.adverseEventPerinatalFilter.remoteSet(filterOptions);
                break;
            case 'Readmission':
                this.adverseEventReadmissionFilter.remoteSet(filterOptions);
                break;
            case 'Renal Injury':
                this.adverseEventRenalInjuryFilter.remoteSet(filterOptions);
                break;
            case 'Surgical':
                this.adverseEventSurgicalFilter.remoteSet(filterOptions);
                break;
            case 'Pain Management':
                this.adverseEventPainManagementFilter.remoteSet(filterOptions);
                break;
            case 'complications':
                this.adverseEventComplicationsFilter.remoteSet(filterOptions);
                break;
            case 'aePublicHealth - Detection & Management':
              this.detectionManagement.remoteSet(filterOptions);
              break;
            case 'ContributingFactors':
              this.adverseEventContributingFactorsFilter.remoteSet(filterOptions);
              break;
            case 'MitigatingFactors':
              this.adverseEventMitigatingFactorsFilter.remoteSet(filterOptions);
              break;
            case 'Detection':
              this.adverseEventDetectionFilter.remoteSet(filterOptions);
              break;
            case 'OrganizationalOutcomes':
              this.adverseEventOrganizationalOutcomesnFilter.remoteSet(filterOptions);
              break;
            case 'AmelioratingActions':
              this.adverseEventAmelioratingActionsFilter.remoteSet(filterOptions);
              break;
            case 'ActionsTakenToReduceRisk':
              this.adverseEventActionsTakenToReduceRisknFilter.remoteSet(filterOptions);
              break;
        }

    }

    clearClickFilterParam() {
      const storageValue = this.localStorageService.getLocalStorage(LocalStorageCategory.adverseEventFilterLocalStorage);
      if (this.preCLickParamFilterObjectState && storageValue) {
        this.localStorageService.setLocalStorage(
            LocalStorageCategory.adverseEventFilterLocalStorage,
            JSON.stringify(this.preCLickParamFilterObjectState));
      }
      else {
        this.localStorageService.setLocalStorage(
            LocalStorageCategory.adverseEventFilterLocalStorage,
            JSON.stringify(this.adverseEventCreatorUtil.createNewAdverseEventFilters()));
      }
      this.reloadAllFilters();
    }

    clearFilters(silent = false) {
        this.appliedFiltersCount = 0;
        this.adverseEventServiceLineFilter.clearFilter(silent);
        this.adverseEventSeverityFilter.clearFilter(silent);
        this.adverseEventUnitFilter.clearFilter(silent);
        this.adverseEventContributingFactorsFilter.clearFilter(silent);
        this.adverseEventMitigatingFactorsFilter.clearFilter(silent);
        this.adverseEventDetectionFilter.clearFilter(silent);
        this.adverseEventOrganizationalOutcomesnFilter.clearFilter(silent);
        this.adverseEventAmelioratingActionsFilter.clearFilter(silent);
        this.adverseEventActionsTakenToReduceRisknFilter.clearFilter(silent);
        this.filters.forEach(filter => {
            filter.clearFilter(silent);
        });
    }

    clearUnitFilters() {
        this.adverseEventUnitFilter.clearFilter();
    }

    reloadAllFilters() {
        this.adverseEventUnitFilter.loadSavedOptions();
        this.adverseEventSeverityFilter.loadSavedOptions();
        this.adverseEventServiceLineFilter.loadSavedOptions();

        this.filters.forEach(filter => {
            filter.loadSavedOptions();
        });
    }

    clearFilterOnTypeChange(type: string) {
        if (type === 'nonTrigger') {
            this.triggerFilters.forEach(filter => {
                filter.clearFilter();
            });
        } else if (type === 'trigger') {
            this.nonTriggerFilters.forEach(filter => {
                filter.clearFilter();
            });
        }
    }

    setAppliedFilterCount() {
      this.appliedFiltersCount = 0;
      this.appliedFiltersCount += this.adverseEventUnitFilter.count;
      this.appliedFiltersCount += this.adverseEventServiceLineFilter.count;

      this.filters.forEach(filter => {
          if (filter.count !== undefined) {
              this.appliedFiltersCount += filter.count;
          }
      })

      this.triggerFilters.forEach(triggerFilter => {
        if (triggerFilter.count !== undefined) {
          this.appliedFiltersCount += triggerFilter.count;
        }
      })

      this.nonTriggerFilters.forEach(nonTriggerFilter => {
        if (nonTriggerFilter.count !== undefined) {
          this.appliedFiltersCount += nonTriggerFilter.count;
        }
      })

      return this.appliedFiltersCount;
  }

   getFiltersObject(){
        return JSON.parse(this.localStorageService.getLocalStorage(LocalStorageCategory.adverseEventFilterLocalStorage));
   }

   applyFilterObject(filterObject: string){
       this.localStorageService.setLocalStorage(LocalStorageCategory.adverseEventFilterLocalStorage, filterObject);
       this.reloadAllFilters();
   }
}
