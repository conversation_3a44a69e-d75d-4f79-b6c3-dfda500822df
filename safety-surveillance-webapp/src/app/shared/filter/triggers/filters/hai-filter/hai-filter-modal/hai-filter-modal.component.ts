import { SimpleModalComponent } from 'ngx-simple-modal';
import { Component, OnInit } from '@angular/core';
import { NonConfirmModel } from '../../../../../models/non-confirm-model';
import { TriggerReference } from '../../../../../models/trigger-reference';
import { LocalStorageService } from '../../../../../services/local-storage.service';
import { PickOption } from '@healthcatalyst/cashmere';

@Component({
  selector: 'hai-filter-modal',
  templateUrl: './hai-filter-modal.component.html',
  styleUrls: ['./hai-filter-modal.component.scss']
})
export class HaiFilterModalComponent extends SimpleModalComponent<NonConfirmModel, TriggerReference[]> implements NonConfirmModel, OnInit {

  value: string;
  localStorage: string;
  
  public data: TriggerReference[];
  public headerTitle = 'Healthcare Associated Infection Triggers';
  public valueField = 'triggerId';
  public labelField = 'triggerName';
  public availableLabel = 'Available';
  public selectedLabel = 'Selected';
  public selectedTriggers: TriggerReference[];
  public preselectedUnits: TriggerReference[];

  constructor(private localStorageService: LocalStorageService) {
    super();
  }

  public ngOnInit(): void {
    let savedTriggers;

    if (this.localStorage !== 'adverseEventFilter') {
      savedTriggers = this.localStorageService.getLocalStorage(this.localStorage);
      savedTriggers = JSON.parse(savedTriggers);
    } else {
      const savedFilter = this.localStorageService.getLocalStorage(this.localStorage);
      const parsedSavedFilter = JSON.parse(savedFilter);
      parsedSavedFilter.categoryTriggers.forEach(trigger => {
        if (trigger.triggerName === 'Healthcare Associated Infections') {
          savedTriggers = trigger.triggerReferences;
        }
      });
    }

    if (savedTriggers != null) {
      this.preselectedUnits = savedTriggers.map((data) => <TriggerReference>data);
      this.selectedTriggers = this.preselectedUnits?.length ? [...this.preselectedUnits] : [];
    }
    else {
      this.preselectedUnits = [];
    }
  }

  public applyFilter(): void {
    this.result = this.selectedTriggers;
    super.close();
  }

  public cancel(): void {
    super.close();
  }

  public sort(a: PickOption, b: PickOption): number {
    const x = a.value as TriggerReference;
    const xValue = x.triggerName.toLowerCase();
    const y = b.value as TriggerReference;
    const yValue = y.triggerName.toLowerCase();
    return xValue == yValue ? 0 : xValue > yValue ? 1 : -1;
  }
}
