// import { async, ComponentFixture, TestBed } from '@angular/core/testing';

// import { AdvancedFilterModalComponent } from './advanced-filter-modal.component';
// import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
// import { TriggerReviewService } from 'app/shared/services/trigger-review.service';
// import { HttpService } from 'app/shared/services/http.service';
// import { HttpModule } from '@angular/http';
// import { HttpClientModule } from '@angular/common/http';
// import { AuthService } from 'app/shared/services/auth.service';
// import { ConfigService } from 'app/shared/services/app-config.service';
// import { MockConfigService } from 'app/shared/services/testinginstances/mock-app-config.service';
// import { LoaderService } from 'app/loader/loader.service';
// import { SimpleModalService } from 'ngx-simple-modal';
// import { MockAuthService } from 'app/shared/services/testinginstances/mock-auth.service';
// import { MockComponent } from 'ng-mocks';
// import { HcDualListBoxComponent } from 'app/shared/ui-modules/hc-dual-list-box/hc-dual-list-box/hc-dual-list-box.component';

// describe('AdvancedFilterModalComponent', () => {
//   let component: AdvancedFilterModalComponent;
//   let fixture: ComponentFixture<AdvancedFilterModalComponent>;

//   beforeEach(async(() => {
//     TestBed.configureTestingModule({
//       declarations: [ AdvancedFilterModalComponent,
//         MockComponent(HcDualListBoxComponent) ],
//       schemas: [CUSTOM_ELEMENTS_SCHEMA],
//       imports: [HttpModule, HttpClientModule],
//       providers: [
//         TriggerReviewService,
//         HttpService,
//         {provide: AuthService, useClass: MockAuthService},
//         SimpleModalService,
//         {provide: ConfigService, useClass: MockConfigService},
//         LoaderService
//       ]
//     })
//     .compileComponents();
//   }));

//   beforeEach(() => {
//     fixture = TestBed.createComponent(AdvancedFilterModalComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
