// import { ReadmissionFilter } from './../filters/readmission-filter/readmission-filter';
// import { PerinatalFilter } from './../filters/perinatal-filter/perinatal-filter';
// import { LocalStorageService } from '../../../services/local-storage.service';
// import { async, ComponentFixture, TestBed } from '@angular/core/testing';
// import { Observable, of } from 'rxjs';

// // import { FilterBarComponent } from './filter-bar.component';
// // import { DateRangeService } from 'app/shared/services/date-range.service';
// // import { CUSTOM_ELEMENTS_SCHEMA } from '@angular/core';
// // import { RunFilterService } from '../filters/run-filter-service';
// // import { UnitFilter } from '../filters/unit-filter/unit-filter';
// // import { FilterChangeService } from '../../filter-change-service';
// // import { CoagulationFilter } from '../filters/coagulation-filter/coagulation-filter';
// // import { TriggerService } from '../../../services/trigger.service';
// // import { HttpService } from '../../../services/http.service';
// // import { HttpModule } from '@angular/http';
// // import { AuthService } from '../../../services/auth.service';
// // import { ConfigService } from '../../../services/app-config.service';
// // import { LoaderService } from '../../../../loader/loader.service';
// // import { GlycemicFilter } from '../filters/glycemic-filter/glycemic-filter';
// // import { HaiFilter } from '../filters/hai-filter/hai-filter';
// // import { MedReversalFilter } from '../filters/med-reversal-filter/med-reversal-filter';
// // import { PainManagementFilter } from '../filters/pain-management-filter/pain-management-filter';
// // import { PatientCareFilter } from '../filters/patient-care-filter/patient-care-filter';
// // import { PediatricFilter } from './../filters/pediatric-filter/pediatric-filter';
// // import { RenalInjuryFilter } from '../filters/renal-injury-filter/renal-injury-filter';
// // import { SurgicalFilter } from '../filters/surgical-filter/surgical-filter';
// // import { ServiceLineFilter } from '../filters/service-line-filter/service-line-filter';
// // import { TriggersFilterBarService } from './triggers-filter-bar-service';
// // import { SimpleModalService } from '../../../../../../node_modules/ngx-simple-modal';
// // import { HttpHandler, HttpClient } from '../../../../../../node_modules/@angular/common/http';

// // describe('FilterBarComponent', () => {
// //   let component: FilterBarComponent;
// //   let fixture: ComponentFixture<FilterBarComponent>;

// //   beforeEach(async(() => {
// //     TestBed.configureTestingModule({
// //       imports: [HttpModule],
// //       declarations: [FilterBarComponent],
// //       providers: [
// //         TriggersFilterBarService,
// //         DateRangeService,
// //         LocalStorageService,
// //         RunFilterService,
// //         UnitFilter,
// //         FilterChangeService,
// //         CoagulationFilter,
// //         TriggerService,
// //         HttpService,
// //         AuthService,
// //         ConfigService,
// //         GlycemicFilter,
// //         LoaderService,
// //         HaiFilter,
// //         MedReversalFilter,
// //         PainManagementFilter,
// //         PatientCareFilter,
// //         PediatricFilter,
// //         PerinatalFilter,
// //         ReadmissionFilter,
// //         RenalInjuryFilter,
// //         SurgicalFilter,
// //         ServiceLineFilter,
// //         SimpleModalService,
// //         HttpClient,
// //         HttpHandler],
// //       schemas: [CUSTOM_ELEMENTS_SCHEMA],
// //     })
// //       .compileComponents();
// //   }));

//   beforeEach(() => {
//     fixture = TestBed.createComponent(FilterBarComponent);
//     component = fixture.componentInstance;
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });

//   it('should call forceClose on filterService on Init', () => {
//     // arrange
//     const filterService = fixture.debugElement.injector.get(TriggersFilterBarService);
//     const filterBarServiceSpy = spyOn(filterService, 'forceClose');

//     // act
//     fixture.detectChanges();

//     // assert
//     expect(filterBarServiceSpy).toHaveBeenCalled();
//   });

//   it('should subscribe to FacilitySubject and set value when changed', () => {
//     // arrange
//     const filterService = fixture.debugElement.injector.get(TriggersFilterBarService);
//     const newFacilityName = 'newFacility';

//     // act
//     fixture.detectChanges();
//     filterService.facilitySubject.next(newFacilityName);

//     // assert
//     expect(component.selectedFacility).toEqual(newFacilityName);
//   });

//   it('should subscribe to openSubject and set value when changed', () => {
//     // arrange
//     const filterService = fixture.debugElement.injector.get(TriggersFilterBarService);

//     // act
//     fixture.detectChanges();
//     filterService.openSubject.next(true);

//     // assert
//     expect(component.filterDrawerOpen).toEqual(true);
//   });

//   it('should show resetConfirm dialog when resetFilters is called', () => {
//     // arrange
//     const dialogService = fixture.debugElement.injector.get(DialogService);
//     const dialogServiceSpy = spyOn(dialogService, 'addDialog').and.returnValue(Observable.from([true]));

//     // act
//     fixture.detectChanges();
//     component.resetFilters();

//     // assert
//     expect(dialogServiceSpy).toHaveBeenCalled();
//   });

//   it('should call filter resetMethods when resetConfirm result is true', () => {

//     // arrange
//     fixture.detectChanges();
//     const dialogService = fixture.debugElement.injector.get(DialogService);
//     const dialogServiceSpy = spyOn(dialogService, 'addDialog').and.returnValue(Observable.from([true]));

//     const runFilterServce = fixture.debugElement.injector.get(RunFilterService);
//     const runFilterServceSpy = spyOn(runFilterServce, 'clearFilters');

//     const filterService = fixture.debugElement.injector.get(TriggersFilterBarService);
//     const filterServceSpy = spyOn(filterService, 'forceClose');

//     // act
//     component.resetFilters();

//     // assert
//     expect(dialogServiceSpy).toHaveBeenCalled();
//     expect(runFilterServceSpy).toHaveBeenCalled();
//     expect(filterServceSpy).toHaveBeenCalled();
//   });

//   it('should NOT call filter resetMethods when resetConfirm result is false', () => {

//     // arrange
//     fixture.detectChanges();
//     const dialogService = fixture.debugElement.injector.get(DialogService);
//     const dialogServiceSpy = spyOn(dialogService, 'addDialog').and.returnValue(Observable.from([false]));

//     const runFilterServce = fixture.debugElement.injector.get(RunFilterService);
//     const runFilterServceSpy = spyOn(runFilterServce, 'clearFilters');

//     const filterService = fixture.debugElement.injector.get(TriggersFilterBarService);
//     const filterServceSpy = spyOn(filterService, 'forceClose');

//     // act
//     component.resetFilters();

//     // assert
//     expect(dialogServiceSpy).toHaveBeenCalled();
//     expect(runFilterServceSpy).not.toHaveBeenCalled();
//     expect(filterServceSpy).not.toHaveBeenCalled();
//   });
// });
