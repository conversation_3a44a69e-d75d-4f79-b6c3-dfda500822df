<div class="post-load-filters">
  <div class="active-saved-filter" *ngIf="savedFilters.length > 0">
    <div class="filter-select">
      <div class="dropdown">
        <div cdkOverlayOrigin #originOverlay class="dropdown-toggle saved-filter" data-toggle="dropdown" (click)="openSavedFilterList(originOverlay, dropdownMenu)">
          <span class="drop-down" *ngIf="activeSavedFilter.length > 0">{{activeSavedFilter}}</span>
          <span class="drop-down" *ngIf="activeSavedFilter.length == 0">Saved filter sets</span>
          <span class="drop-down" type="button" id="facility-dropdown" aria-haspopup="true" aria-expanded="true">
            <span class="fa fa-chevron-down drop-arrow" id="facility-dropdown"></span>
          </span>
        </div>
        <ng-template cdk-portal #dropdownMenu>
          <div [@fadeInOut] class="saved-filters-dropdown-menu">
            <div class="arrow-up"></div>
            <div class="saved-filters-dropdown-content">
              <div class="saved-filter-option" *ngFor="let savedFilter of savedFilters" (click)="onSavedFilterChange(savedFilter)">
                <div class="saved-filter-row" *ngIf="!savedFilter.edit">
                  <div class="saved-filter-name">
                    <span>{{savedFilter.filterName}}</span>
                  </div>
                  <div class="saved-filter-action-buttons">
                    <i class="fa fa-pencil" aria-hidden="true" (click)="onEditClick(savedFilter)"></i>
                    <i class="fa fa-trash-o" (click)="onDeleteClick(savedFilter)" aria-hidden="true"></i>
                  </div>
                </div>
                <div class="saved-filter-facilities">
                  Facilities:
                </div>
                <!-- <ul> -->
                  <li class="saved-filter-facilities" *ngFor="let facility of savedFilter?.facilities">
                    <span>{{facility}}</span>
                  </li>
                <!-- </ul> -->
              </div>
            </div>
          </div>
        </ng-template>
      </div>
    </div>
  </div>
</div>