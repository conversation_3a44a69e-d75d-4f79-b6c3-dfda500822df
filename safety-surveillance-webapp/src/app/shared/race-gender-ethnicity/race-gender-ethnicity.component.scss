@import "@healthcatalyst/cashmere/scss/mixins";
@import "../../../assets/styles/components/buttons";
@import "../../../assets/styles/_layout";
@import "../../../assets/styles/_colors.scss";

ngContainer {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    flex-direction: column;
  }

  .row {
    display: -webkit-box;
    display: -ms-flexbox;
    display: flex;
    flex-direction: row;
    margin-left: 1px;

    @include media-breakpoint-down(md) {
      display: block;
      margin-right: 0;
    }
  }


  hc-form-field {
    width: 300px;
    margin-top: 15px;
  }

  .right-field {
    margin-left: 25px;
    @include media-breakpoint-down(md) {
      margin-left: 0;
    }
  }
