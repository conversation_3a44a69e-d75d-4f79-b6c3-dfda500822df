import { Injectable } from '@angular/core';
import { SurveyEventData } from 'app/shared/models/survey-event/survey-event-data';
import { SurveyEventFormData } from 'app/shared/models/survey-event/survey-event-form-data';
import { SurveyEventFormQuestionData } from 'app/shared/models/survey-event/survey-event-form-question-data';
import { SurveyEventFormQuestionOptionData } from 'app/shared/models/survey-event/survey-event-form-question-option-data';
import { SurveyEventResponseData } from 'app/shared/models/survey-event/survey-event-response-data';
import { SurveyEventEventService } from '../../survey-event/survey-event-event-service';
import { SurveyEventService } from '../../survey-event/survey-event.service';
import { AuditHelper } from '../audit-helper';
import { DressingAssessmentDisplayRule } from './dressing-assessment-display-rule';
import { IResponseChange } from './response-change.interface';
import { ISurveyEventDataLoad } from './survey-event-data-load.interface';

/*
  DressingAssessmentRule - For question 6, if an option is selected, we need to take that value and append it to
  question7 on load and when a value is changed.  This requires altering responses because if we have to disable the
  option because someone unselected #6.
*/
@Injectable()
export class DressingAssessmentRule implements IResponseChange, ISurveyEventDataLoad {
  q6QuestionId: string;
  q7QuestionId: string;
  q7DressingAssessmentOptionId: string;

  constructor(private surveyEventEventService: SurveyEventEventService,
    private surveyEventService: SurveyEventService) {
    this.q6QuestionId = '3F7C4BBC-FF9C-4BDB-8BA0-6517B9B8B0EC'; // These are hard-coded id's from the database.
    this.q7QuestionId = '866B0432-6007-4413-A5D0-112C071E303E';
    this.q7DressingAssessmentOptionId = 'FDC70350-E43D-492D-A1B9-1C9F96C197AC';
  }

  onSurveyEventDataLoad(event: SurveyEventData, form: SurveyEventFormData) {
    const question = AuditHelper.GetQuestionFromForm(this.q6QuestionId, form);
    if (question) {
      const response = AuditHelper.GetResponseByQuestionId(question.questionId, event);
      this.processQuestion(response, event, form);
    }
  }

  onResponseChange(response: SurveyEventResponseData, event: SurveyEventData, form: SurveyEventFormData) {
    if (response.questionId.toLowerCase() === this.q6QuestionId.toLowerCase()) {
      const question = AuditHelper.GetQuestionFromForm(this.q6QuestionId, form);
      if (question) {
        this.processQuestion(response, event, form);
      }
    }
  }

  processQuestion(response: SurveyEventResponseData, event: SurveyEventData, form: SurveyEventFormData) {
    const question7 = AuditHelper.GetQuestionFromForm(this.q7QuestionId, form);
    const dressingAssessmentOption = AuditHelper.GetOptionByIdFromQuestion(this.q7DressingAssessmentOptionId, question7);

    if (response && response.responseOptionId) {
      if (dressingAssessmentOption){
        dressingAssessmentOption.isReadOnly = false;
        dressingAssessmentOption.isActive = true;

        const question6 = AuditHelper.GetQuestionFromForm(this.q6QuestionId, form);
        const response7 = AuditHelper.GetResponseByQuestionId(question7.questionId, event);
        DressingAssessmentDisplayRule.addQuestion6TextToDressingAssessment(dressingAssessmentOption, question6, response, response7);
      }
    } else {
      if (dressingAssessmentOption) {
        const originalDressingAssessment = dressingAssessmentOption.text.split('<br />')[0];
        dressingAssessmentOption.text = originalDressingAssessment;
        dressingAssessmentOption.isReadOnly = true;
        dressingAssessmentOption.isActive = false;

        this.clearOutDressingAssessmentOption(question7, dressingAssessmentOption, event);
      }
    }

    this.surveyEventEventService.questionUpdated.next(question7);
  }

  clearOutDressingAssessmentOption(question7: SurveyEventFormQuestionData, dressingAssessmentOption: SurveyEventFormQuestionOptionData, event: SurveyEventData) {
    const response = AuditHelper.GetResponseByQuestionId(question7.questionId, event);
    if (response && response.responseText) {
      const text = response.responseText.split(',').filter(x => x === dressingAssessmentOption.optionId);
      response.responseText = null;
      if (text.length > 1) {
        response.responseText = text.join(',');
      }

      this.surveyEventService.saveResponse(response).subscribe((data) => {
        let index = event.responses.findIndex(r => r.questionId === response.questionId);
        if (index >= 0) {
          event.responses[index] = data;
        } else {
          event.responses.push(data);
        }
        event.surveyEventId = response.surveyEventId;
        this.surveyEventEventService.sendSurveyEventUpdate(event);
      });
      this.surveyEventEventService.responseSaved.next(response);
    }
  }
}
