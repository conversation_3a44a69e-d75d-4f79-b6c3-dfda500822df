import { SurveyEventData } from 'app/shared/models/survey-event/survey-event-data';
import { SurveyEventFormData } from 'app/shared/models/survey-event/survey-event-form-data';
import { SurveyEventFormQuestionOptionData } from 'app/shared/models/survey-event/survey-event-form-question-option-data';
import { SurveyEventResponseData } from 'app/shared/models/survey-event/survey-event-response-data';
import { TestingHelper } from 'app/shared/utility/testing/testing-helper';
import { DressingAssessmentDisplayRule } from './dressing-assessment-display-rule';


describe('DressingAssessmentDisplayRule', () => {
  let subject: DressingAssessmentDisplayRule;

  let event: SurveyEventData;
  let form: SurveyEventFormData;
  let response: SurveyEventResponseData;
  let question6Id: string;
  let question7Id: string;
  let question4Id: string;
  let question7DressingAssessmentOptionId: string;

  beforeEach(() => {
    subject = new DressingAssessmentDisplayRule()

    question6Id = subject.q6QuestionId;
    question7Id = subject.q7QuestionId;
    question4Id = 'question4';
    question7DressingAssessmentOptionId = subject.q7DressingAssessmentOptionId;

    event = TestingHelper.CreateSurveyEventWithResponsesFromQuestionIds([question6Id, question7Id, question4Id]);
    form = TestingHelper.CreateFormWithMultipleQuestions([question6Id, question7Id, question4Id]);
    form.sections[0].questions[0].options = [new SurveyEventFormQuestionOptionData()];
    form.sections[0].questions[0].options[0].isActive = true;
    form.sections[0].questions[0].options[0].isReadOnly = false;
    form.sections[0].questions[0].options[0].optionId = '12';
    form.sections[0].questions[0].options[0].text = 'Confirmed Dressing is clean ...';

    form.sections[0].questions[1].options = [new SurveyEventFormQuestionOptionData()];
    form.sections[0].questions[1].options[0].isActive = true;
    form.sections[0].questions[1].options[0].isReadOnly = false;
    form.sections[0].questions[1].options[0].optionId = question7DressingAssessmentOptionId
    form.sections[0].questions[1].options[0].text = 'Dressing Assessment';

    event.responses[0].responseOptionId = '12'
    event.responses[1].responseText = question7DressingAssessmentOptionId + ','
    response = event.responses[1]; // for this rule, it needs to run on response to question 7
  })

  describe('onSurveyEventDataLoad', () => {

    it('should change question 7 back to dressing assessment when no options are selected', () => {
      // arrange
      const expectedValue = 'Dressing Assessment';
      event.responses[1].responseText = '';

      // act
      subject.onSurveyEventDataLoad(event, form);

      // assert
      expect(form.sections[0].questions[1].options[0].text).toBe(expectedValue);
    })

    it('should change question 7 should add the options from question 6', () => {
      // arrange
      const expectedValue = `<div>${form.sections[0].questions[1].options[0].text}</div><br /><div style="font-weight: 600;color: #767676;">${form.sections[0].questions[0].questionText} *</div><div>${form.sections[0].questions[0].options[0].text}</div>`;
      response.responseOptionId = form.sections[0].questions[0].options[0].optionId; // needs to be something

      // act
      subject.onSurveyEventDataLoad(event, form);

      // assert
      expect(form.sections[0].questions[1].options[0].isActive).toBe(true);
      expect(form.sections[0].questions[1].options[0].isReadOnly).toBe(false);
      expect(form.sections[0].questions[1].options[0].text).toBe(expectedValue);
    })
  })

  describe('onResponseChange', () => {
    it('should change question 7 back to dressing assessment when no options are selected', () => {
      // arrange
      const expectedValue = 'Dressing Assessment';
      event.responses[1].responseText = '';

      // act
      subject.onResponseChange(response, event, form);

      // assert
      expect(form.sections[0].questions[1].options[0].text).toBe(expectedValue);
    })

    it('should change question 7 should add the options from question 6', () => {
      // arrange
      const expectedValue = `<div>${form.sections[0].questions[1].options[0].text}</div><br /><div style="font-weight: 600;color: #767676;">${form.sections[0].questions[0].questionText} *</div><div>${form.sections[0].questions[0].options[0].text}</div>`;
      response.responseOptionId = form.sections[0].questions[0].options[0].optionId; // needs to be something

      // act
      subject.onResponseChange(response, event, form);

      // assert
      expect(form.sections[0].questions[1].options[0].isActive).toBe(true);
      expect(form.sections[0].questions[1].options[0].isReadOnly).toBe(false);
      expect(form.sections[0].questions[1].options[0].text).toBe(expectedValue);
    })
  })
})
