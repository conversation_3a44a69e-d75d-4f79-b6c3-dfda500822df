import { Observable, of } from "rxjs";
import { HttpService } from "../http.service";
import { AppConfigSettings } from "app/shared/models/app-config-settings";
import { HttpHeaders } from "@angular/common/http";

export class MockHttpService implements Pick<HttpService, keyof HttpService> {
  appConfigSettings: AppConfigSettings;
  headers: HttpHeaders;

  makeApiGetRequest<T>(actionUrl: string, showGlobalLoader: boolean, cacheable: boolean = false): Observable<T> {
    return of();
  }

  makeApiGetFileRequest(actionUrl: string) {
    return of();
  }

  makeApiGetZipFileRequest(actionUrl: string) {
    return of();
  }

  makeApiPostRequest<T>(actionUrl: string, body: any, showGlobalLoader: boolean, cacheable: boolean = false): Observable<T> {
    return of();
  }

  makeApiDeleteRequest<T>(actionUrl: string, showGlobalLoader: boolean, cacheable: boolean = false): Observable<T> {
    return of();
  }

  makeApiPutRequest<T>(actionUrl: string, body: any, showGlobalLoader: boolean = true, cacheable: boolean = false): Observable<T> {
    return of();
  }
}
