import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs'
import { IAdverseEventService } from 'app/shared/services/adverse-event.service';
import { FacilityFiltersBody } from 'app/shared/models/facility-filters-body';
import { DataExportPostBody } from 'app/shared/models/data-export-post-body';
import { TriggerEventReview } from 'app/shared/models/trigger-review';
import { AeFrequencyData } from 'app/shared/models/ae-frequency-data';
import { TriggerCategoryGroup } from 'app/shared/models/trigger-category-group';
import { SummaryDefinition } from 'app/shared/models/summary-definition';

@Injectable()
export class MockAdverseEventServiceBuilder {
  private mockAdverseEventService: MockAdverseEventService;

  constructor() {
    this.mockAdverseEventService = new MockAdverseEventService();
  }

  setAdverseEventAggregations(value: any): MockAdverseEventServiceBuilder {
    this.mockAdverseEventService.mockSubCategoryAggregated = value;
    return this;
  }

  build(): MockAdverseEventService {
    return this.mockAdverseEventService;
  }
}

export class MockAdverseEventService implements IAdverseEventService {

  public mockSubCategoryAggregated: any;

  getAdverseEventsAggregations(startDt: Date, endDt: Date, body: FacilityFiltersBody) {
    return of(this.mockSubCategoryAggregated);
  }

  getAdverseEventsFrequency(startDt: Date, endDt: Date, body: FacilityFiltersBody) {
    return of(AeFrequencyData);
  }

  getAdverseEventCounts(body: DataExportPostBody, startDt: Date, endDt: Date): Observable<number> {
    throw new Error('Method not implemented.');
  }

  exportToCsv(startDt: Date, endDt: Date, fileName: string, body: DataExportPostBody) {
    throw new Error('Method not implemented.');
  }

  getAdverseEventsByLocation(facilities: string[], startDt: Date, endDt: Date): Observable<TriggerEventReview[]> {
    throw new Error('Method not implemented.');
  }

  deleteAdverseEvent(triggerEventReview: TriggerEventReview, encounterId: string, encounterSource: string): Observable<boolean> {
    throw new Error('Method not implemented.');
  }

  getSeverityName(severityLetter: string): string {
    return 'mockName';
  }
}
