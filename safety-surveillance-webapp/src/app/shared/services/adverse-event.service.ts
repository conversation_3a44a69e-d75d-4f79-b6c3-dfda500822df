import { DataExportPostBody } from './../models/data-export-post-body';
import { TriggerEventReview } from './../models/trigger-review';
import { FiltersObject } from 'app/shared/models/filters-object';
import { Injectable } from '@angular/core';
import { Observable, Observer } from 'rxjs';
import { HttpService } from './http.service';
import { AuthService } from './auth.service';
import { ConfigService } from './app-config.service';
import { AppConfigSettings } from '../models/app-config-settings';
import { FacilityFiltersBody } from '../models/facility-filters-body';
import { DateRangeService } from './date-range.service';
import { AeFrequencyData } from '../models/ae-frequency-data';
import { PositiveTrigger } from '../models/positive-trigger';
import { DateInput } from '../models/date-input';
import { ExportElementGroup } from '../models/export-element-group';
import { AeRatesAggregated } from '../models/ae-rates-aggregated';
import { SummaryDefinition } from '../models/summary-definition';
import { DataExportReportOptions } from '../models/data-export-report-options';
import { PostExportConfigurableAuditInputDto } from '@models/configurable-audit/post-export-configurable-audit-input-dto';

export interface IAdverseEventService {
  getAdverseEventCounts(body: DataExportPostBody, startDt: Date, endDt: Date): Observable<number>;
  exportToCsv(startDt: Date, endDt: Date, fileName: string, body: DataExportPostBody);
  getAdverseEventsByLocation(facilities: string[], startDt: Date, endDt: Date): Observable<TriggerEventReview[]>;
  getAdverseEventsAggregations(startDt: Date, endDt: Date, body: FacilityFiltersBody): Observable<any>;
  deleteAdverseEvent(triggerEventReview: TriggerEventReview, sourceId: string, sourceDataId: string, encounterId: string, encounterSource: string): Observable<boolean>;
}

@Injectable()
export class AdverseEventService implements IAdverseEventService {
  config: AppConfigSettings;

  constructor(private httpService: HttpService, private authService: AuthService, private configSrvc: ConfigService, private dateRangeService: DateRangeService) {
    this.config = this.configSrvc.config;
  }

  getAdverseEventCounts(body: DataExportPostBody, startDt: Date, endDt: Date): Observable<number> {
    const startString = this.dateRangeService.GetDateStringNoOffSet(startDt);
    const endString = this.dateRangeService.GetDateStringNoOffSet(endDt);

    return this.httpService.makeApiPostRequest<number>('/adverseevents/count' + '?start=' + startString + '&end=' + endString, body, true);
  }

  exportToCsv(startDt: Date, endDt: Date, fileName: string, body: DataExportPostBody) {
    const startString = this.dateRangeService.GetDateStringNoOffSet(startDt);
    const endString = this.dateRangeService.GetDateStringNoOffSet(endDt);

    return this.httpService.makeApiPostRequest('/adverseevents/export' + '?start=' + startString + '&end=' + endString + '&fileName=' + fileName, body, false);
  }

  getExportColumns(options: DataExportReportOptions): Observable<ExportElementGroup[]> {
    const url = `/adverseevents/properties?objectName='patientEncounter'&reportType=${options.reportType}&surveillanceType=${options.surveillanceType}`;
    return this.httpService.makeApiGetRequest(url, true);
  }

  getAdverseEventsByLocation(facilities: string[], startDt: Date, endDt: Date): Observable<TriggerEventReview[]> {
    const startString = this.dateRangeService.GetDateStringNoOffSet(startDt);
    const endString = this.dateRangeService.GetDateStringNoOffSet(endDt);

    return this.httpService.makeApiPostRequest('/adverseevents/aeByFacility' + '?start=' + startString + '&end=' + endString, facilities, true);
  }

  getAdverseEventsAggregations(startDt: Date, endDt: Date, body: FacilityFiltersBody): Observable<any> {
    const startString = this.dateRangeService.GetDateStringNoOffSet(startDt);
    const endString = this.dateRangeService.GetDateStringNoOffSet(endDt);
    return this.httpService.makeApiPostRequest('/adverseevents/aeAggregates' + '?start=' + startString + '&end=' + endString, body, true);
  }

  getAdverseEventsFrequency(startDt: Date, endDt: Date, body: FacilityFiltersBody): Observable<AeFrequencyData> {
    const startString = this.dateRangeService.GetDateStringNoOffSet(startDt);
    const endString = this.dateRangeService.GetDateStringNoOffSet(endDt);
    return this.httpService.makeApiPostRequest('/adverseevents/aeFrequency' + '?start=' + startString + '&end=' + endString, body, true);
  }

  getFrequencyDetail(date: string, category: string, body: FacilityFiltersBody): Observable<PositiveTrigger[]> {
    return this.httpService.makeApiPostRequest('/adverseevents/frequencydetail' + '?date=' + date + '&category=' + category, body, true);
  }

  getAdverseEventRates(startDate: Date, endDate: Date, chartType: string, body: FacilityFiltersBody, breakoutType: string = ''): Observable<Array<AeRatesAggregated[]>> {
    const startString = this.dateRangeService.GetDateStringNoOffSet(startDate);
    const endString = this.dateRangeService.GetDateStringNoOffSet(endDate);
    return this.httpService.makeApiPostRequest<Array<AeRatesAggregated[]>>(
      '/AdverseEvents/rates' +
        '?startDate=' +
        startString +
        '&endDate=' +
        endString +
        '&aggregateType=' +
        chartType +
        '&breakoutType=' +
        breakoutType,
      body,
      true
    );
  }

  deleteAdverseEvent(triggerEventReview: TriggerEventReview, sourceId: string, sourceDataId: string, encounterId: string, encounterSource: string): Observable<boolean> {
    var url = `/triggers/reviews/delete?id=${triggerEventReview.reviewId}&encounterId=${encounterId}&encounterSource=${encounterSource}&sourceId=${sourceId}&sourceDataId=${sourceDataId}`
    return this.httpService.makeApiDeleteRequest<boolean>(url, true);
  }

  getSeverityName(severityLetter: string): string {
    switch (severityLetter) {
      case 'A':
        return 'A - Circumstances that have the capacity to cause harm';
      case 'B':
        return 'B - Circumstances that did not reach the patient';
      case 'C':
        return 'C - Circumstances that reached the patient but did not cause harm';
      case 'D':
        return 'D - Circumstances that reached the patient and required monitoring or intervention to confirm that it resulted in no harm to the patient';
      case 'E':
        return 'E - Temporary harm to the patient requiring intervention';
      case 'F':
        return 'F - Temporary harm to the patient and required initial or prolonged hospitalization';
      case 'G':
        return 'G - Permanent patient harm';
      case 'H':
        return 'H - Intervention required to sustain life';
      case 'I':
        return 'I - Patient Death';
    }
  }
}
