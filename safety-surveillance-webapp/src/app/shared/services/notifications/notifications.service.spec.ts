import { mock, instance } from 'ts-mockito';
import { NotificationsService } from './notifications.service';
import { HttpService } from '../http.service';

describe('NotificationsService', () => {
  const httpService = mock(HttpService);
  let service: NotificationsService;

  beforeEach(() => {
    service = new NotificationsService(instance(httpService));
  });

  it('should create', () => {
    expect(service).toBeTruthy();
  });
  
});
