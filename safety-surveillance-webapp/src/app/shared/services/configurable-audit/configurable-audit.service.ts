import { Injectable } from '@angular/core';
import { HttpService } from '../http.service';
import { Observable } from 'rxjs';
import { AuditTool } from '@models/configurable-audit/audit-tool';
import { GetPatientListForConfigurableAuditInputDto } from '@models/configurable-audit/get-patient-list-for-configurable-audit-input-dto';
import { PatientListViewModel } from '@models/configurable-audit/patient-list-view-model';
import { GetPatientDetailsInputDto } from '@models/configurable-audit/get-patient-details-input-dto';
import { PatientDetailsViewModel } from '@models/configurable-audit/patient-details-view-model';
import { PostExportConfigurableAuditInputDto } from '@models/configurable-audit/post-export-configurable-audit-input-dto';
import { GetAuditToolExportListCountViewModel } from '@models/configurable-audit/get-audit-tool-export-list-count-view-model';
import { AuditToolExport } from '@models/configurable-audit/audit-tool-export';

@Injectable()
export class ConfigurableAuditService {

  constructor(
    private readonly _httpService: HttpService
  ) { }

  public getConfigurableAuditById(auditToolId: string): Observable<AuditTool> {
    return this._httpService.makeApiGetRequest(
      `/configurableAudit?auditToolId=${auditToolId}`,
      true
    );
  }

  public getConfigurableAuditExportColumnsById(auditToolId: string): Observable<{ columns: AuditToolExport[]}> {
    return this._httpService.makeApiGetRequest(
      `/configurableAudit/exportColumns?auditToolId=${auditToolId}`,
      true
    );
  }

  public getPatientListForConfigurableAudit(inputDto: GetPatientListForConfigurableAuditInputDto): Observable<PatientListViewModel> {
    return this._httpService.makeApiPostRequest<PatientListViewModel, GetPatientListForConfigurableAuditInputDto>(
      '/configurableAudit/patientList',
      inputDto,
      true
    );
  }

  public getPatientDetailsForConfigurableAudit(inputDto: GetPatientDetailsInputDto): Observable<PatientDetailsViewModel> {
    return this._httpService.makeApiPostRequest<PatientDetailsViewModel>(
      '/configurableAudit/patientDetail',
      inputDto,
      true
    );
  }

  public getCountForDataExportRange(inputDto: PostExportConfigurableAuditInputDto): Observable<GetAuditToolExportListCountViewModel> {
    return this._httpService.makeApiPostRequest<GetAuditToolExportListCountViewModel>(
      '/configurableAudit/export/count',
      inputDto,
      true
    );
  }
}
