import { Injectable } from "@angular/core";
import { Router } from "@angular/router";
import { RoundPatientData } from "@models/round/round-patient-data";
import { SurveyEventFormData } from "@models/survey-event/survey-event-form-data";
import { RoundFilterBarService } from "app/shared/filter/round/round-filter-bar/round-filter-bar-service";
import { RoundStateService } from "./round-state-service";
import { RoundService } from "./round.service";

@Injectable()
export class RoundFacadeService {
  constructor(
    private roundService: RoundService,
    private filterService: RoundFilterBarService,
    private roundStateService: RoundStateService,
    private router: Router,
  ) {}

  loadPatientDataAndRouteToDocumentation(encounterId, encounterSource, form: SurveyEventFormData, isReadOnly) {
    const selectedFacilities = this.filterService.getFacilities();
    this.roundService
      .getRoundingPatient(
        encounterId,
        encounterSource,
        selectedFacilities,
        form.formId
      )
      .subscribe((data) => {
        this.routeToPatientDocumentation(data, form, isReadOnly);
      });
  }

  routeToRoundPatientDocumentation(patient: RoundPatientData, form: SurveyEventFormData) {
    this.routeToPatientDocumentation(patient, form, true);
  }

  getAvailableRoundingFormsForPatientEncounter(encounterId, encounterSource) {
    const selectedFacilities = this.filterService.getFacilities();

    return this.roundService.getAvailableRoundingFormsForPatientEncounter(encounterId, encounterSource, selectedFacilities);
  }

  private routeToPatientDocumentation(patient: RoundPatientData,
    form: SurveyEventFormData,
    isReadOnly: boolean) {
        this.roundStateService.patientState = patient;
        this.roundStateService.patientState.surveyEventId = form.surveyEventId;
        this.roundStateService.patientState.deviceDescription = form.deviceDescription;
        this.roundStateService.patientState.deviceId = form.deviceId;
        this.roundStateService.patientState.woundDescription = form.woundDescription;
        this.roundStateService.patientState.woundId = form.woundId;
        this.roundStateService.patientState.woundLocationDsc = form.woundLocationDSC;
        this.roundStateService.patientState.woundTypeDsc = form.woundTypeDSC;
        this.roundStateService.patientState.lineLaterality = form.deviceLaterality;

        this.router.navigate(["/patientdetail/roundtool"], {
            queryParams: { isReadOnly: isReadOnly, formId: form.formId },
        });
    }
}
