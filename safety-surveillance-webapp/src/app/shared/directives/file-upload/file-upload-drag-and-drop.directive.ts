import {Directive, EventEmitter, HostBinding, HostListener, Output} from '@angular/core';

@Directive({
  selector: '[fileUploadDragAndDrop]'
})
export class FileUploadDragAndDropDirective {

  @Output()
  filesDropped = new EventEmitter<any>();

  private readonly initial = '#eeeeee';
  private readonly itemWithin = '#b0b0b0';

  @HostBinding('style.background-color') private background = this.initial;

  @HostListener('dragover', ['$event']) public dragOver(event: any){
    event.preventDefault();
    event.stopPropagation();
    this.background = this.itemWithin;
  }

  @HostListener('dragleave', ['$event']) public dragLeave(event: any) {
    event.preventDefault();
    event.stopPropagation();
    this.background = this.initial;
  }

  @HostListener('drop', ['$event']) public drop(event: any) {
    event.preventDefault();
    event.stopPropagation();
    this.background = this.initial;
    const files = event.dataTransfer.files;
    if (files.length > 0) {
      console.log(files);
      this.filesDropped.emit(files);
    }
  }

  constructor() { }

}
