// import { async, ComponentFixture, TestBed } from '@angular/core/testing';

// import { LandingRapDiscMedTemplateComponent } from './landing-rap-disc-med-template.component';
// import { PipesModule } from 'app/shared/pipes/pipes.module';
// import { PositiveTrigger } from 'app/shared/models/positive-trigger';

// describe('LandingRapDiscMedTemplateComponent', () => {
//   let component: LandingRapDiscMedTemplateComponent;
//   let fixture: ComponentFixture<LandingRapDiscMedTemplateComponent>;
//   let testTrigger: PositiveTrigger;

//   beforeEach(async(() => {
//     TestBed.configureTestingModule({
//       declarations: [ LandingRapDiscMedTemplateComponent ],
//       imports: [PipesModule]
//     })
//     .compileComponents();
//   }));

//   beforeEach(() => {
//     fixture = TestBed.createComponent(LandingRapDiscMedTemplateComponent);
//     component = fixture.componentInstance;
//     testTrigger = new PositiveTrigger();
//     testTrigger.triggerDts = new Date('1/1/2017');
//     testTrigger.relatedEventDts = new Date('12/1/2015');
//     testTrigger.prereqEventDts = new Date('7/6/1979');
//     testTrigger.relatedEventDsc = 'Something in the hospital';
//     component.trigger = testTrigger;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
