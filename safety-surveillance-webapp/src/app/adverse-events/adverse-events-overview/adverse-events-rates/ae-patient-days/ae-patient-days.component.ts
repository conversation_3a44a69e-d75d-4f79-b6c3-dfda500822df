import { Component } from '@angular/core';
import { RatesBaseComponent } from '../rates-base/rates-base.component';
import { TooltipHelper } from 'app/shared/charting/tooltip-helper';

@Component({
  selector: 'ae-patient-days',
  templateUrl: './ae-patient-days.component.html',
  styleUrls: ['./ae-patient-days.component.scss']
})
export class AePatientDaysComponent extends RatesBaseComponent {

  constructor(tooltipHelper: TooltipHelper) {
    super(tooltipHelper);
    this.graphName = 'Events per 1,000 Patient Days*'
    this.yAxisTitle = '# of Events per 1,000 Patient Days'
  }
}
