<div class="chart-area-container">
  <div *ngIf="adverseEventsByUnit.length > 0"
       [ngClass]="{
        'chart' : !topFive,
        'chart-small' : topFive
      }">
      <div class="title-bar-container">
        <div *ngIf="!topFive" class="chart-title">Breakout by: Location</div>
        <div *ngIf="topFive" class="chart-title">Events by Location</div>
        <div class="top-right-container">
          <ng-container *ngIf="!multipleSeverityScalesFlg; else elseBlock ">
            <app-adverse-descriptions [arrowSide]='arrowSide'></app-adverse-descriptions>
          </ng-container>
          <ng-template #elseBlock>
            <app-adverse-descriptions [arrowSide]='arrowSide' [showLabel]="true" [showCategory]="true" [scaleType]="1"></app-adverse-descriptions>
            <app-adverse-descriptions class="extra-left-space" [arrowSide]='arrowSide' [showLabel]="true" [showCategory]="true" [scaleType]="2"></app-adverse-descriptions>
          </ng-template>
        </div>
      </div>
      <p class="chart-subtitle">{{ dateInput.start | datex: 'MM/DD/YYYY' }} - {{ dateInput.end | datex: 'MM/DD/YYYY' }}</p>
      <div *ngFor="let obj of adverseEventsByUnit" class="event-container">
        <div (click)="adverseEventByUnitClick(obj)"
            class="clickable-text">
            {{obj.adverseEventUnit}}
        </div>
        <div *ngIf="multipleSeverityScalesFlg" class="bar-container">
          <div class="bar-row-container">
            <div class="inner-bar" [ngStyle]="{width: (obj.total/adverseEventsByUnitFullBarValue | percent)}">
              <div class="severity-container" [ngStyle]="{width: (obj.total/obj.total | percent)}">
                <div class="gray-bar"></div>
              </div>
            </div>
            <div class="bar-labels">{{obj.total}}</div>
          </div>
        </div>

        <ng-container *ngFor="let s of obj.severityBreakout;let i = index">
          <div class="bar-container" *ngIf="multipleSeverityScalesFlg || (!multipleSeverityScalesFlg && i < 1)">
            <div class="bar-row-container">
              <div class="inner-bar" [ngStyle]="{width: (obj.total/adverseEventsByUnitFullBarValue | percent)}">
                <img *ngIf="multipleSeverityScalesFlg" [src]="scaleIconPaths[i]" alt="" class="severity-icon">
                <ng-container *ngFor="let a of s.ratingCountCollection">
                  <div *ngIf="a.count>0" class="severity-container"
                       [ngStyle]="{width: (a.count/obj.total | percent)}">
                    <div [popper]="toolTip"
                         [popperShowOnStart]="false"
                         [popperTrigger]="'hover'"
                         [popperPlacement]="'bottom'"
                         [popperDisableStyle]="false"
                         [popperStyles]="{'width': '200px'}"
                         (popperOnShown)="setToolTipValue(a)"
                         class="pop-popcorn-box">
                         <severity-bar [severity]="a"></severity-bar>
                    </div>
                  </div>
                </ng-container>
                <div class="bar-labels">{{s.total}}</div>
              </div>
            </div>
          </div>
        </ng-container>
      </div>
      <div class="footer-container flex-end" *ngIf="adverseEventsByUnit.length >= 5 && topFive">
        <a routerLink="/analytics/main/barcharts" id="view-all-unit" class="clickable-text" (click)="aeOverviewComponent.aeNavigation('Unit','NonTrigger')">View All</a>
      </div>
  </div>
  <div *ngIf="adverseEventsByUnit.length === 0 && !topFive" class="chart-no-data">
      There is no data to display in this view
    </div>
</div>

<popper-content class='inside-popper' #toolTip (click)="toolTip.hide()">
  <div>
    <p>Documented Events: <strong>{{toolTipValue}}</strong></p>
    <p>
      Severity: <strong>{{toolTipSeverityTitle}}</strong><br>
      {{toolTipSeverityDescription}}
    </p>

  </div>
</popper-content>
