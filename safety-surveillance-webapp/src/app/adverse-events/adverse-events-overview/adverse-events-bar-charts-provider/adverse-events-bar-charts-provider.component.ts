import { Component, OnIni<PERSON>, On<PERSON><PERSON><PERSON>, OnDestroy, Input } from '@angular/core';
import { Router } from '@angular/router';
import { AdverseEventData } from 'app/shared/models/adverse-event-data';
import { AdverseEventsFilterBarService } from 'app/shared/filter/adverse-events/adverse-events-bar/adverse-events-filter-bar-service';
import { RunAdverseEventsFilterService } from 'app/shared/filter/adverse-events/adverse-events-bar/run-adverse-events-filter-bar';
import { FilterOption } from 'app/shared/filter/filter-option';
import { LocalStorageService } from 'app/shared/services/local-storage.service';
import { LocalStorageCategory } from 'app/shared/constants/local-storage-category';
import { AdverseEventsOverviewComponent } from '../adverse-events-overview.component';
import { AdverseEventProviderAggregated } from 'app/shared/models/adverse-event-provider-aggregated';
import { DateInput } from 'app/shared/models/date-input';
import { Subscription } from 'rxjs';
import { SeverityDefinitions } from 'app/shared/charting/severity-definitions';
import { FeatureFlagService } from 'app/shared/services/feature-flag.service';
import { FeatureFlags } from 'app/shared/constants/feature-flags';

@Component({
  selector: 'adverse-events-bar-charts-provider',
  templateUrl: './adverse-events-bar-charts-provider.component.html',
  styleUrls: ['./adverse-events-bar-charts-provider.component.scss']
})
export class AdverseEventsBarChartsProviderComponent implements OnInit, OnDestroy, OnChanges {

  healthSystem: string;

  public selectedFacilities: string[];

  public multiFacility: boolean;

  public dateInput: DateInput;

  arrowSide = 'left';

  public adverseEventsByProvider: AdverseEventProviderAggregated[] = [];
  public adverseEventsByProviderFullBarValue: number;
  public toolTipSeverityTitle: string;
  public toolTipSeverityDescription: string;
  public aiScalePath: string;
  public sixScalePath: string;

  public toolTipValue: string;

  public multipleSeverityScalesFlg = false;

  @Input() public topFive: boolean;

  @Input() public alternativeData: AdverseEventProviderAggregated[] = [];

  @Input() public aeData: AdverseEventData;

  private dateInputSubscription: Subscription;
  private scaleIconPaths: string[];

  constructor(
    private router: Router,
    private filterService: AdverseEventsFilterBarService,
    private runFilterService: RunAdverseEventsFilterService,
    private localStorageService: LocalStorageService,
    public aeOverviewComponent: AdverseEventsOverviewComponent,
    public featureFlagService: FeatureFlagService) {

      this.scaleIconPaths = [
        'assets/images/A-I.svg',
        'assets/images/6point.svg'
      ];
  }

  ngOnInit() {
    this.selectedFacilities = JSON.parse(this.localStorageService.getLocalStorage(LocalStorageCategory.selectedFacilitiesLocalStorage));

    this.multiFacility = this.selectedFacilities.length > 1;

    this.dateInputSubscription = this.filterService.dateInputSubject.subscribe(input => {
      if (input !== undefined) {
        this.dateInput = input;
      }
    });

    if (this.topFive) {
      this.formatData(this.alternativeData);
    } else {
      this.formatData(this.aeData);
    }

    this.multipleSeverityScalesFlg = this.featureFlagService.getFeatureFlagByName(FeatureFlags.multipleSeverityScalesFlg);
  }

  ngOnDestroy() {
    if (this.dateInputSubscription && !this.dateInputSubscription.closed) {
      this.dateInputSubscription.unsubscribe();
    }
  }

  ngOnChanges() {
    this.selectedFacilities = JSON.parse(this.localStorageService.getLocalStorage(LocalStorageCategory.selectedFacilitiesLocalStorage));

    this.multiFacility = this.selectedFacilities.length > 1;

    this.filterService.dateInputSubject.subscribe(input => {
        if (input !== undefined) {
          this.dateInput = input;
        }
      });

    if (this.topFive) {
      this.formatData(this.alternativeData);
    } else {
      this.formatData(this.aeData);
    }
  }

  formatData(data) {
    // Provider Data Formatting
    if (data) {
      if (this.topFive) {
        this.adverseEventsByProvider = data.slice(0, 5);
      } else {
        this.adverseEventsByProvider = data.adverseEventsByProvider;
      }

      if (this.adverseEventsByProvider.length > 0) {
        this.adverseEventsByProviderFullBarValue = this.adverseEventsByProvider[0].total;
      } else {
        this.adverseEventsByProviderFullBarValue = 0;
      }
    }
  }

  adverseEventByProviderClick(providerFullName: string, masterProviderId: string) {
    if (this.multiFacility) {
      return;
    }
    const filterOption: FilterOption = new FilterOption(providerFullName, masterProviderId);

    this.localStorageService.setLocalStorage(LocalStorageCategory.adversePatientListStartDateLocalStorage, this.dateInput.start.toISOString());
    this.localStorageService.setLocalStorage(LocalStorageCategory.adversePatientListEndDateLocalStorage, this.dateInput.end.toISOString());

    this.runFilterService.setClickFilterParameter('provider', filterOption);

    this.router.navigate(['/analytics/patientlist']);
  }

  setToolTipValue(severityItem: any) {
    this.toolTipValue = severityItem.count;
    this.toolTipSeverityTitle = severityItem.rating;
    this.toolTipSeverityDescription = severityItem.description;
  }
}
