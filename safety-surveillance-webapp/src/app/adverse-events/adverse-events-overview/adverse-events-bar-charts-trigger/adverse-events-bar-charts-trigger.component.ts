import { AdverseEventTriggerAggregated } from 'app/shared/models/adverse-event-trigger-aggregated';
import { AdverseEventsFilterBarService } from './../../../shared/filter/adverse-events/adverse-events-bar/adverse-events-filter-bar-service';
import { FiltersObject, TriggerCategories } from 'app/shared/models/filters-object';
import { Component, OnInit, Input, OnChanges, OnDestroy } from '@angular/core';
import { Router } from '@angular/router';
import { DateInput } from '../../../shared/models/date-input';
import { RunAdverseEventsFilterService } from '../../../shared/filter/adverse-events/adverse-events-bar/run-adverse-events-filter-bar';
import { Subscription } from 'rxjs';
import { AdverseEventData } from 'app/shared/models/adverse-event-data';

@Component({
  selector: 'adverse-events-bar-charts-trigger',
  templateUrl: './adverse-events-bar-charts-trigger.component.html',
  styleUrls: ['./adverse-events-bar-charts-trigger.component.scss']
})
export class AdverseEventsBarChartsTriggerComponent implements OnInit, OnDestroy, OnChanges {

  healthSystem: string;

  public selectedFacilities: string[];

  public multiFacility: boolean;

  public dateInput: DateInput;

  arrowSide = 'right';

  public adverseEventsByTrigger: AdverseEventTriggerAggregated[] = [];
  public adverseEventsByTriggerFullBarValue: number;

  public questionPath: string;

  @Input() public topFive: boolean;

  @Input() public alternativeData: AdverseEventTriggerAggregated[] = [];

  @Input() public aeData: AdverseEventData;

  private dateInputSubscription: Subscription;

  constructor(
    private router: Router,
    private filterService: AdverseEventsFilterBarService,
    private runFilterService: RunAdverseEventsFilterService) {
    this.questionPath = 'assets/images/question.png';
  }

  ngOnInit() {
    this.dateInputSubscription = this.filterService.dateInputSubject.subscribe(input => {
      if (input !== undefined) {
        this.dateInput = input;
      }
    });

    if (this.topFive) {
      this.formatData(this.alternativeData);
    } else {
      this.formatData(this.aeData);
    }
  }

  ngOnDestroy() {
    if (this.dateInputSubscription && !this.dateInputSubscription.closed) {
      this.dateInputSubscription.unsubscribe();
    }
  }

  ngOnChanges() {
    this.filterService.dateInputSubject.subscribe(input => {
      if (input !== undefined) {
        this.dateInput = input;
      }
    });

    if (this.topFive) {
      this.formatData(this.alternativeData);
    } else {
      this.formatData(this.aeData);
    }
  }

  formatData(data) {
    if (data) {
      if (this.topFive) {
        this.adverseEventsByTrigger = data.slice(0, 5);
      } else {
        this.adverseEventsByTrigger = data.adverseEventsByTriggerName;
      }

      if (this.adverseEventsByTrigger.length > 0) {
        this.adverseEventsByTriggerFullBarValue = this.adverseEventsByTrigger.map(obj => obj.positiveTriggers).reduce((a, b) => Math.max(a, b));
      } else {
        this.adverseEventsByTriggerFullBarValue = 0;
      }
    }
  }

  adverseEventByTriggerClick(trigger: AdverseEventTriggerAggregated) {
    if (this.multiFacility) {
      return;
    }
    const triggerId = trigger.triggerId;
    const triggerName = trigger.triggerName
    const triggerCategory = trigger.triggerCategory
    const filterOption = {
      triggerId: String(triggerId),
      triggerName: triggerName
    }

    this.runFilterService.setClickFilterParameter(triggerCategory, filterOption);

    this.router.navigate(['/analytics/patientlist']);
  }
}
