import { Component, Input, OnInit, OnD<PERSON>roy, OnChanges } from '@angular/core';
import { Router } from '@angular/router';
import { AdverseEventsFilterBarService } from 'app/shared/filter/adverse-events/adverse-events-bar/adverse-events-filter-bar-service';
import { RunAdverseEventsFilterService } from 'app/shared/filter/adverse-events/adverse-events-bar/run-adverse-events-filter-bar';
import { LocalStorageService } from 'app/shared/services/local-storage.service';
import { AdverseEventsOverviewComponent } from '../adverse-events-overview.component';
import { AdverseEventIndividualTypeAggregated } from 'app/shared/models/adverse-event-individual-type-aggregated';
import { AdverseEventData } from 'app/shared/models/adverse-event-data';
import { DateInput } from 'app/shared/models/date-input';
import { Subscription } from 'rxjs';
import { LocalStorageCategory } from 'app/shared/constants/local-storage-category';
import { FilterOption } from 'app/shared/filter/filter-option';
import { SeverityDefinitions } from 'app/shared/charting/severity-definitions';
import { FeatureFlagService } from 'app/shared/services/feature-flag.service';
import { FeatureFlags } from 'app/shared/constants/feature-flags';

@Component({
  selector: 'adverse-events-bar-charts-individual-type',
  templateUrl: './adverse-events-bar-charts-individual-type.component.html',
  styleUrls: ['./adverse-events-bar-charts-individual-type.component.scss']
})
export class AdverseEventsBarChartsIndividualTypeComponent implements OnInit, OnDestroy, OnChanges {

  @Input() public topFive: boolean;
  @Input() public alternativeData: AdverseEventIndividualTypeAggregated[] = [];
  @Input() public aeData: AdverseEventData;

  healthSystem: string;

  public selectedFacilities: string[];

  public multiFacility: boolean;

  public dateInput: DateInput;
  private dateInputSubscription: Subscription;

  arrowSide = 'left';

  public adverseEventsByIndividualType: AdverseEventIndividualTypeAggregated[] = [];
  public adverseEventsByIndividualTypeFullBarValue: number;

  public toolTipSeverityTitle: string;
  public toolTipSeverityDescription: string;
  public aiScalePath: string;
  public sixScalePath: string;
  public multipleSeverityScalesFlg = false;

  public toolTipValue: string;
  private scaleIconPaths: string[];

  constructor(
    private router: Router,
    private filterService: AdverseEventsFilterBarService,
    private runFilterService: RunAdverseEventsFilterService,
    private localStorageService: LocalStorageService,
    public aeOverviewComponent: AdverseEventsOverviewComponent,
    public featureFlagService: FeatureFlagService) {
    this.scaleIconPaths = [
      'assets/images/A-I.svg',
      'assets/images/6point.svg'
    ];
  }

  ngOnInit() {
    this.selectedFacilities = JSON.parse(this.localStorageService.getLocalStorage(LocalStorageCategory.selectedFacilitiesLocalStorage));

    this.multiFacility = this.selectedFacilities.length > 1;

    this.dateInputSubscription = this.filterService.dateInputSubject.subscribe(input => {
      if (input !== undefined) {
        this.dateInput = input;
      }
    });

    if (this.topFive) {
      this.formatData(this.alternativeData);
    } else {
      this.formatData(this.aeData);
    }

    this.multipleSeverityScalesFlg = this.featureFlagService.getFeatureFlagByName(FeatureFlags.multipleSeverityScalesFlg);
  }

  ngOnDestroy(){
    if(this.dateInputSubscription && !this.dateInputSubscription.closed){
      this.dateInputSubscription.unsubscribe();
    }
  }

  ngOnChanges(){
    this.selectedFacilities = JSON.parse(this.localStorageService.getLocalStorage(LocalStorageCategory.selectedFacilitiesLocalStorage));

    this.multiFacility = this.selectedFacilities.length > 1;

    this.filterService.dateInputSubject.subscribe(input => {
        if (input !== undefined) {
          this.dateInput = input;
        }
      });

    if (this.topFive) {
      this.formatData(this.alternativeData);
    } else {
      this.formatData(this.aeData);
    }
  }

  formatData(data){
    if (data) {
      if (this.topFive) {
        this.adverseEventsByIndividualType = data.slice(0, 5);
      } else {
        this.adverseEventsByIndividualType = data.adverseEventsByIndividualType;
      }

      if (this.adverseEventsByIndividualType.length > 0) {
        this.adverseEventsByIndividualTypeFullBarValue = this.adverseEventsByIndividualType[0].total;
      } else {
        this.adverseEventsByIndividualTypeFullBarValue = 0;
      }
    }
  }

  adverseEventByIndividualTypeClick(individualTypeName: string, individualTypeId: string){
    if(this.multiFacility){
      return;
    }
    const filterOption: FilterOption = new FilterOption(individualTypeName, individualTypeId);

    this.localStorageService.setLocalStorage(LocalStorageCategory.adversePatientListStartDateLocalStorage, this.dateInput.start.toISOString());
    this.localStorageService.setLocalStorage(LocalStorageCategory.adversePatientListEndDateLocalStorage, this.dateInput.end.toISOString());

    const filterObjectStr = this.localStorageService.getLocalStorage(LocalStorageCategory.adverseEventFilterLocalStorage);
    const filterObject = JSON.parse(filterObjectStr)
    filterObject.individualType = [filterOption];
    this.localStorageService.setLocalStorage(LocalStorageCategory.adverseEventFilterLocalStorage, JSON.stringify(filterObject));


    this.router.navigate(['/analytics/patientlist']);
  }

  setToolTipValue(severityItem: any) {
    this.toolTipValue = severityItem.count;
    this.toolTipSeverityTitle = severityItem.rating;
    this.toolTipSeverityDescription = severityItem.description;
  }
}
