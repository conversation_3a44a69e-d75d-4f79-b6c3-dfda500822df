import { Component, Input } from '@angular/core';
import { TotalTriggersFiredAndIncidentsReported } from '@models/analytics/total-triggers-fired-and-incidents-reported';

@Component({
  selector: 'total-triggers-fired-and-incidents-reported',
  templateUrl: './total-triggers-fired-and-incidents-reported.component.html',
  styleUrls: ['./total-triggers-fired-and-incidents-reported.component.scss'],
})
export class TotalTriggersFiredAndIncidentsReportedComponent {

  @Input() public totals: TotalTriggersFiredAndIncidentsReported;

  constructor() { }

}
