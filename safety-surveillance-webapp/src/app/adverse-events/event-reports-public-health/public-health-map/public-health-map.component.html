
<div class="map-container">
  <div class="map-title">
    Patients (by zipcode) with public health surveillance triggers
  </div>
  <div class="azmap-host">
    <azmap [points]="atlasPoints$ | async" (loaded)="onMapLoaded()" #azmap>
        <ng-template azPopup let-item>
          <div class="popup">
            <div class="trigger-item" *ngFor="let t of item.triggers">
              <div class="link" (click)="takeUserToPatientList(t)"><a>{{t.triggerCount}}</a></div>
              <div class="trigger-name"><span>{{t.triggerName}}</span></div>
            </div>
          </div>
        </ng-template>
    </azmap>
  </div>
</div>
