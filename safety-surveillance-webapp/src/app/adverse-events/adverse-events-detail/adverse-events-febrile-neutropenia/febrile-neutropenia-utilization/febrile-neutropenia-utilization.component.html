<div class="container">
  <div class="graph-title-container">
    <div class="graph-name">{{graphName}}</div>
    <div class="severity-help">
      <div [popper]="descriptionPopper" [popperShowOnStart]="false" [popperTrigger]="'click'" [popperPlacement]="'right'"
      [popperDisableStyle]="true">
      <img [src]="questionPath" #severityToolTip alt="" class="clickable">
    </div>
    <popper-content class="inside-popper" #descriptionPopper (click)="descriptionPopper.hide()">
      <div class="description-container">
        <div class="arrow_box_left">
          <div class="description-text">
            <span>{{graphDescription}}</span>
          </div>
        </div>
      </div>
    </popper-content>
    </div>
  </div>
  <div class="x-axis-panel" *ngIf="utilizationData">
    <hc-label class="x-axis-title">X-Axis:</hc-label>
    <span>
      <hc-radio-group [(ngModel)]="selectedXAxisType" inline="true">
        <hc-radio-button *ngFor="let x of xAxisTypes"
                        [value]="x"
                        (change)="onXAxisChanged($event)">
          {{ x }}
        </hc-radio-button>
      </hc-radio-group>
    </span>
  </div>
  <line-graph *ngIf="utilizationData && utilizationData.length > 0" class="graph-container" graph
    chartId="asdf"
    [data]="utilizationData"
    [textAlign]="'left'"
    xAxisRotation="40"
    [toolTipFormatter]="tooltipHelper.percentTooltip"
    [yValuesFormatter]="valueFormatter"
    gridTop=10
    gridBottom='20%'
    [yMax]=100
    [showLegend]="true"
    [xAxisTitle]="xAxisTitle"
    [gridBottomPercent]="gridBottomPercent"
    yAxisTitle="Rate of Antibiotic Regimen over Total FN Cases">
  </line-graph>
  <div class="empty" *ngIf="!utilizationData || utilizationData.length === 0">
    No data to display
  </div>
  <p>
    * Antibiotic Utilization Numerator: count of patients with specific antibiotic regimen, consisting of specified antibiotics received within first 24 hours following admit
  </p>
  <p>
    * Antibiotic Utilization Denominator: count of admissions in the filtered FN population (not limited to top 10 regimens)
  </p>
</div>
