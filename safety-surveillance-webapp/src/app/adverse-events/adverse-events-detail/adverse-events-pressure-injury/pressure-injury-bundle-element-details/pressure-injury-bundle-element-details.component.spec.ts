// import { async, ComponentFixture, TestBed } from '@angular/core/testing';

// import { PressureInjuryBundleElementDetailsComponent } from './pressure-injury-bundle-element-details.component';

// describe('PressureInjuryBundleElementDetailsComponent', () => {
//   let component: PressureInjuryBundleElementDetailsComponent;
//   let fixture: ComponentFixture<PressureInjuryBundleElementDetailsComponent>;

//   beforeEach(async(() => {
//     TestBed.configureTestingModule({
//       declarations: [ PressureInjuryBundleElementDetailsComponent ]
//     })
//     .compileComponents();
//   }));

//   beforeEach(() => {
//     fixture = TestBed.createComponent(PressureInjuryBundleElementDetailsComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });
// });
