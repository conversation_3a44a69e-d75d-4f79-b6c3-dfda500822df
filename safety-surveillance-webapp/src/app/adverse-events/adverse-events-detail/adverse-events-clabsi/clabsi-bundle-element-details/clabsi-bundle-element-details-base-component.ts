import { HacType } from 'app/shared/constants/hac-type';
import { XAxisService } from 'app/shared/services/x-axis.service';
import { XAxisType } from 'app/shared/models/public-health/x-axis-type';
import { RadioButtonChangeEvent } from '@healthcatalyst/cashmere';
import { OnInit, OnDestroy, Directive } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { Subscription } from 'rxjs';
import { LocalStorageService } from 'app/shared/services/local-storage.service';
import { LocalStorageCategory } from 'app/shared/constants/local-storage-category';
import { FilterChangeService } from 'app/shared/filter/filter-change-service';
import { BreakoutTypes } from 'app/shared/constants/breakout-types';
import { HacBundleElementsDetailData } from '../../adverse-events-shared/models/hac-bundle-elements-details-data';
import { ClabsiFacilityFilterBodyService } from 'app/shared/filter/clabsi/clabsi-filters/clabsi-filter-body-service';
import { FilterBarService } from 'app/shared/filter/abstracts/filter-bar-service';

@Directive()
export abstract class ClabsiBundleElementDetailsBaseComponent implements OnInit, OnDestroy {

  private dateInputSubscription: Subscription;
  private filterChangeSubscription: Subscription;
  urlSubscription: Subscription;
  selectedFacilities: string[];
  multiFacility: boolean;
  type: string;

  chartId = 'elementDetailsGraph';
  breakoutStratification: string = BreakoutTypes.Unit;
  breakoutView: string = 'compliance'

  hacTypeString = HacType.Clabsi;

  public startDT: Date;
  public endDT: Date;

  public xAxisTypes: string[];
  public xAxisTitle: string;
  public selectedXAxisType: string = XAxisType[2];
  public showXAxis: boolean;
  public gridBottomPercent = '';

  public elementData: HacBundleElementsDetailData; //use this to populate the graphs on the page

  protected hasLoadedInitData: boolean = false;

  constructor(private baseActivatedRoute: ActivatedRoute,
    private baseLocalStorageService: LocalStorageService,
    private baseFilterService: FilterBarService,
    private filterBodyService: ClabsiFacilityFilterBodyService,
    private baseFilterChangeService: FilterChangeService,
    private xAxisService: XAxisService) {  }

  ngOnInit() {
    this.baseOnInit();

    this.urlSubscription = this.baseActivatedRoute.params.subscribe(params => {
      if (this.hasLoadedInitData) {
        this.loadXAxisData();
        this.baseLoadData();
      }
    })

    this.dateInputSubscription = this.baseFilterService.dateInputSubject.subscribe(input => {
      if (input !== undefined) {
        this.startDT = new Date(input.start);
        this.endDT = new Date(input.end);
        if (this.hasLoadedInitData) {
          this.loadXAxisData();
          this.baseLoadData();
        }
      }
    });

    this.filterChangeSubscription = this.baseFilterChangeService.changeEvent.subscribe(data => {
      if (this.hasLoadedInitData) {
        this.loadXAxisData();
        this.baseLoadData();
      }
    });

    this.loadXAxisData();
    this.baseLoadData();
  }

  ngOnDestroy() {
    if (this.urlSubscription) {
      this.urlSubscription.unsubscribe();
    }
    if (this.dateInputSubscription) {
      this.dateInputSubscription.unsubscribe();
    }
    if  (this.filterChangeSubscription) {
      this.filterChangeSubscription.unsubscribe();
    }
  }

  public loadFacilitiesData() {
    this.selectedFacilities = JSON.parse(this.baseLocalStorageService.getLocalStorage(LocalStorageCategory.selectedFacilitiesLocalStorage));

    if (this.selectedFacilities == null) {
      return;
    }

    if (this.selectedFacilities.length > 1) {
      this.multiFacility = true;
    } else {
      this.multiFacility = false;
    }
  }

  public baseOnInit() {
    // override
  }

  loadXAxisData() {
    this.selectedXAxisType = this.xAxisService.getXAxisType(this.hacTypeString);
    this.xAxisTypes = this.xAxisService.loadXAxisData(this.startDT, this.endDT);
    this.xAxisTitle = this.xAxisService.getXAxisTitle(this.selectedXAxisType);
    this.selectedXAxisType = this.xAxisService.setDateChangeXAxis(this.hacTypeString, this.startDT, this.endDT);
    this.gridBottomPercent = this.xAxisService.setGridBottomPercent(this.selectedXAxisType);
  }

  public baseLoadData() {
    // override
  }

  getFiltersObject() {
    return this.filterBodyService.getFacilityFilterBody();
  }

  onBreakoutByClick(value: string) {
    this.breakoutStratification = value;
  }

  onViewChange(value: string) {
    this.breakoutView = value;
  }

  public onXAxisChanged(e: RadioButtonChangeEvent) {
    this.selectedXAxisType = e.value;
    this.xAxisTitle = this.xAxisTitle = this.xAxisService.getXAxisTitle(this.selectedXAxisType);
    this.xAxisService.setXAxisType(this.selectedXAxisType, this.hacTypeString);
    this.gridBottomPercent = this.xAxisService.setGridBottomPercent(this.selectedXAxisType);

    this.baseLoadData();
  }
}
