import { XAxisService } from 'app/shared/services/x-axis.service';
import { XAxisType } from 'app/shared/models/public-health/x-axis-type';
import { RadioButtonChangeEvent } from '@healthcatalyst/cashmere';
import { Component, OnInit, OnDestroy, OnChanges } from '@angular/core';
import { Subscription, combineLatest } from 'rxjs';
import { ActivatedRoute } from '@angular/router';
import { FilterChangeService } from 'app/shared/filter/filter-change-service';
import { LocalStorageService } from 'app/shared/services/local-storage.service';
import { LocalStorageCategory } from 'app/shared/constants/local-storage-category';
import { ClabsiFacilityFilterBodyService } from 'app/shared/filter/clabsi/clabsi-filters/clabsi-filter-body-service';
import { HacType } from 'app/shared/constants/hac-type';
import { HacBundleComplianceSummaryData } from '../../adverse-events-shared/models/hac-bundle-compliance-summary-data';
import { distinctUntilChanged, switchMap } from 'rxjs/operators';
import { HacService } from 'app/shared/services/hac.service';
import { FilterBarService } from 'app/shared/filter/abstracts/filter-bar-service';

@Component({
  selector: 'clabsi-summary',
  templateUrl: './clabsi-summary.component.html',
  styleUrls: ['./clabsi-summary.component.scss']
})
export class ClabsiSummaryComponent implements OnInit, OnDestroy {
  public summaryData: HacBundleComplianceSummaryData;
  public startDT: Date;
  public endDT: Date;
  public selectedFacilities: string[];
  public multiFacility: boolean;
  public complianceText = ' Overall Compliance';
  public graphType = { text: '' };
  public bundleType: string;
  public hacType = HacType.Clabsi;
  public bundleTypeAndNames = [];
  public capitalType = '';
  public bundleName = '';

  public xAxisTypes: string[];
  public xAxisTitle: string;
  public selectedXAxisType: string = XAxisType[2];
  public gridBottomPercent: string = '';

  public activeBundleType: string;
  loadDataSubscription: Subscription;

  constructor(
    private activatedRoute: ActivatedRoute,
    private hacService: HacService,
    private filterService: FilterBarService,
    private filterChangeService: FilterChangeService,
    private localStorageService: LocalStorageService,
    private clabsiFacilityFilterBodyService: ClabsiFacilityFilterBodyService,
    private xAxisService: XAxisService
  ) { }

  ngOnInit() {
    this.activeBundleType = this.activatedRoute.snapshot.queryParamMap.get(
      'type'
    );
    this.setBundleText();

    this.loadDataSubscription = combineLatest(
      this.filterService.dateInputSubject.pipe(distinctUntilChanged()),
      this.filterChangeService.changeSubject,
      this.activatedRoute.queryParams.pipe(distinctUntilChanged())
    ).subscribe(
      ([input, p, params]) => {
        if (input !== undefined) {
          this.startDT = new Date(input.start);
          this.endDT = new Date(input.end);
          this.activeBundleType = params['type'];
          this.capitalType = this.activeBundleType.charAt(0).toUpperCase() + this.activeBundleType.slice(1);
          this.getBundleTypesNames();
          this.loadXAxisData();
          this.loadData();
        }
      }
    );
  }

  ngOnDestroy() {
    if (this.loadDataSubscription) {
      this.loadDataSubscription.unsubscribe();
    }
  }

  setBundleText() {
    this.bundleTypeAndNames.forEach(element => {
      if (element.bundleType === this.capitalType) {
        this.bundleName = element.bundleNm;
      }
    });
    this.graphType.text = this.bundleName + this.complianceText;
    this.bundleType = this.capitalType;
  }

  loadXAxisData() {
    this.selectedXAxisType = this.xAxisService.getXAxisType(HacType.Clabsi);
    this.xAxisTypes = this.xAxisService.loadXAxisData(this.startDT, this.endDT);
    this.xAxisTitle = this.xAxisService.getXAxisTitle(this.selectedXAxisType);
    this.selectedXAxisType = this.xAxisService.setDateChangeXAxis(HacType.Clabsi, this.startDT, this.endDT);
    this.gridBottomPercent = this.xAxisService.setGridBottomPercent(this.selectedXAxisType);
  }

  loadData() {
    this.selectedFacilities = JSON.parse(
      this.localStorageService.getLocalStorage(
        LocalStorageCategory.selectedFacilitiesLocalStorage
      )
    );

    if (this.selectedFacilities == null) {
      return;
    }

    if (this.selectedFacilities.length > 1) {
      this.multiFacility = true;
    } else {
      this.multiFacility = false;
    }

    this.clabsiFacilityFilterBodyService.getFacilityFilterBody()
      .pipe(
        switchMap(body => this.hacService
          .getBundleComplianceSummaryData(
            this.startDT,
            this.endDT,
            body,
            HacType.Clabsi,
            this.activeBundleType,
            this.selectedXAxisType
          ))
      )
      .subscribe(data => {
        this.summaryData = data;
      });
  }

  getBundleTypesNames() {
    this.hacService.getElementTypeNames(this.hacType).subscribe(data => {
      this.bundleTypeAndNames = data;
      this.setBundleText();
    });
  }

  public onXAxisChanged(e: RadioButtonChangeEvent) {
    this.selectedXAxisType = e.value;
    this.xAxisTitle = this.xAxisTitle = this.xAxisService.getXAxisTitle(this.selectedXAxisType);
    this.xAxisService.setXAxisType(this.selectedXAxisType, HacType.Clabsi);
    this.gridBottomPercent = this.xAxisService.setGridBottomPercent(this.selectedXAxisType);

    this.loadData();
  }
}
