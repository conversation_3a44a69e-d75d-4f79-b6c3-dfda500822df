<div class="x-axis-panel" *ngIf="utilizationGraphs && showXAxis">
  <hc-label class="x-axis-title">X-Axis:</hc-label>
  <span>
    <hc-radio-group [(ngModel)]="selectedXAxisType" inline="true">
      <hc-radio-button *ngFor="let x of xAxisTypes"
                      [value]="x"
                      (change)="onXAxisChanged($event)">
        {{ x }}
      </hc-radio-button>
    </hc-radio-group>
  </span>
</div>
<div *ngFor="let bundle of utilizationGraphs" >

  <named-graph-container *ngIf="bundle"
    [title]="bundle.elementName"
    [description]="bundle.elementDescription"
    linkText="Details >>"
    (detailsClickEvent)="onDetailsClick(bundle.elementId)"
    >
    <!-- User story 413888 Disable Control Chart Button Within PSM -->

    <hac-utilization-element graph *ngIf="bundle.elementId === 'LineUtilizationRate'"
      [chartId]="bundle.elementId"
      [data]="bundle.elementData"
      graphType="line"
      [tooltipFormatter]="getFormatter(bundle.elementId)"
      [xAxisTitle]="xAxisTitle"
      gridBottomPercent="30%"
    ></hac-utilization-element>
    <hac-utilization-element graph *ngIf="bundle.elementId === 'MedianlineDurationDays'"
      [chartId]="bundle.elementId"
      [data]="bundle.elementData"
      graphType="bar"
      [tooltipFormatter]="getFormatter(bundle.elementId)"
      [xAxisTitle]="xAxisTitle"
      [gridBottomPercent]="gridBottomPercent"
    ></hac-utilization-element>
    <hac-utilization-element graph *ngIf="bundle.elementId !== 'LineUtilizationRate' && bundle.elementId !== 'MedianlineDurationDays'"
      [chartId]="bundle.elementId"
      [data]="bundle.elementData"
      graphType="stacked-bar"
      [tooltipFormatter]="getFormatter(bundle.elementId)"
      [xAxisTitle]="xAxisTitle"
      [gridBottomPercent]="gridBottomPercent"
    ></hac-utilization-element>


  </named-graph-container>
</div>
