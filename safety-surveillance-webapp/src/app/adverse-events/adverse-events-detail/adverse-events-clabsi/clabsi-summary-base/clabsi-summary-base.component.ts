import { Component, OnInit, OnChanges, Input } from '@angular/core';
import { AeRatesAggregated } from 'app/shared/models/ae-rates-aggregated';
import { GraphSeries } from 'app/shared/charting/graph-series';
import { ChartItem } from 'app/shared/charting/chart-item';
import { Colors } from 'app/shared/charting/colors';
import { MarkLineHelper } from 'app/shared/charting/mark-line-helper';
import { TooltipHelper } from 'app/shared/charting/tooltip-helper';

@Component({
  selector: 'clabsi-summary-base',
  template: 'No UI'
})
export class ClabsiSummaryBaseComponent implements OnInit, OnChanges {

  @Input()
  data: AeRatesAggregated[]

  @Input()
  goal: number;

  graphSeries: GraphSeries[];
  oneGraphSeries: GraphSeries;
  graphData: ChartItem;
  markLine: object;

  @Input()
  graphName: string;

  @Input()
  chartId: string;

  @Input()
  description: string;

  @Input()
  type: string;

  colors = Colors;

  constructor(
    private markLineHelper: <PERSON><PERSON><PERSON><PERSON>el<PERSON>,
    public tooltipHelper: TooltipHelper) {
  }

  ngOnInit() {

  }

  ngOnChanges() {
    this.markLine = this.markLineHelper.goalLineOptions(this.valueFormatter(this.goal), '{c}% goal');
    this.graphSeries = [];
    this.oneGraphSeries = new GraphSeries();
    this.oneGraphSeries.chartItems = [];
    this.oneGraphSeries.name = this.graphName;
    this.mapData();
  }

  valueFormatter(value: any) {
    return value * 100;
  }

  private mapData() {
    if (this.data) {
      this.data.forEach(point => {
        this.graphData = new ChartItem(point.category, point.product);
        this.graphData.key = point.category;
        this.graphData.value = point.product;
        this.graphData.label = point.category;
        this.graphData.isPartialDataSet = point.isPartialDataSet;
        this.graphData.denominator = point.denominator;
        this.graphData.numerator = point.numerator;

        this.oneGraphSeries.chartItems.push(this.graphData);
      });
      this.graphSeries.push(this.oneGraphSeries);
    }
  }
}
