import { mock, instance } from 'ts-mockito';
import { CautiOverviewComponent } from './cauti-overview.component';
import { XAxisService } from './../../../../shared/services/x-axis.service';
import { CautiService } from 'app/shared/services/cauti.service';
import { FilterChangeService } from 'app/shared/filter/filter-change-service';
import { LocalStorageService } from 'app/shared/services/local-storage.service';
import { Router } from '@angular/router';
import { FilterBarService } from 'app/shared/filter/abstracts/filter-bar-service';
import { CautiFilterBodyService } from '../services/cauti-filter-body.service';
import { AppConfigService } from 'app/shared/services/config.service';

describe('CautiOverviewComponent', () => {
  const cautiService = mock(CautiService);
  const cautiFilterBodyService = mock(CautiFilterBodyService);
  const router = mock(Router);
  const filterBarService = mock(FilterBarService);
  const filterChangeService = mock(FilterChangeService);
  const localStorageService = mock(LocalStorageService);
  const appConfigService = mock(AppConfigService);
  const xAxisService = mock(XAxisService);

  let component: CautiOverviewComponent;

  beforeEach(() => {
    component = new CautiOverviewComponent(instance(cautiService), instance(cautiFilterBodyService), instance(router), instance(filterBarService),
                                            instance(filterChangeService), instance(localStorageService), instance(appConfigService), instance(xAxisService));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  
});

// import { async, ComponentFixture, TestBed, inject } from '@angular/core/testing';

// import { CautiOverviewComponent } from './cauti-overview.component';
// import { CUSTOM_ELEMENTS_SCHEMA, Component } from '@angular/core';
// import { CautiService } from 'app/shared/services/cauti.service';
// import { MockCautiService } from 'app/shared/services/testingInstances/mock-cauti-service';
// import { CautiFilterBarService } from 'app/shared/filter/cauti/cauti-filter-bar/cauti-filter-bar-service';
// import { MockCautiFilterBarService } from 'app/shared/services/testingInstances/mock-cauti-filter-bar-service';
// import { DateInput } from 'app/shared/models/date-input';
// import { FilterChangeService } from 'app/shared/filter/filter-change-service';
// import { MockFilterChangeService } from 'app/shared/services/testingInstances/mock-filter-change-service';
// import { LocalStorageService } from 'app/shared/services/local-storage.service';
// import { MockLocalStorageService } from 'app/shared/services/testingInstances/mock-local-storage-service';
// import { CautiOverviewData } from '../models/cauti-overview-data';
// import { Observable, of } from 'rxjs';
// import { AeRatesAggregated } from 'app/shared/models/ae-rates-aggregated';

// const mockComponent = (options: Component): Component => {
//   class Mock {

//   }
//   const metadata: Component = {
//       selector: options.selector,
//       template: options.template || '',
//       inputs: options.inputs,
//       outputs: options.outputs
//   };
//   return Component(metadata)(<any>Mock);
// };

// const mockDateInput: DateInput = {
//   start: new Date('January 1, 2018 00:00:00'),
//   end: new Date('January 2, 2018 23:59:59'),
//   label: 'testDateRange'
// }

// let mockLocalStorage = `{
//   "selectedFacilityLocalStorage": ["testFacility"],
//   "cautiSelectedStartDate": "startDate",
//   "cautiSelectedEndDate": "endDate",
//   "adverseEventFilterLocalStorage": {}
// }`

// describe('CautiOverviewComponent', () => {
//   let component: CautiOverviewComponent;
//   let fixture: ComponentFixture<CautiOverviewComponent>;

//   beforeEach(async(() => {
//     TestBed.configureTestingModule({
//       declarations: [
//         CautiOverviewComponent
//       ],
//       schemas: [ CUSTOM_ELEMENTS_SCHEMA ],
//       providers: [
//         { provide: CautiService, useClass: MockCautiService },
//         { provide: CautiFilterBarService, useValue: new MockCautiFilterBarService(mockDateInput) },
//         { provide: FilterChangeService, useClass: MockFilterChangeService },
//         { provide: LocalStorageService, useValue: new MockLocalStorageService(mockLocalStorage) }
//       ]
//     })
//     .compileComponents();
//   }));

//   beforeEach(() => {
//     fixture = TestBed.createComponent(CautiOverviewComponent);
//     component = fixture.componentInstance;
//     fixture.detectChanges();
//   });

//   it('should create', () => {
//     expect(component).toBeTruthy();
//   });

//   describe('on initialization', () => {

//     it('should subscribe to the dateInputSubject observable, populate the start & end date variables, and call the setLocalStorage method', () => {
//         // Arrange
//         const seLocalStorageSpy = spyOn(component, 'setLocalStorage')

//         // Act
//         component.ngOnInit();

//         // Assert
//         expect(seLocalStorageSpy).toHaveBeenCalled();
//         expect(component.startDT).toEqual(mockDateInput.start);
//         expect(component.endDT).toEqual(mockDateInput.end);
//       });
//   });

//   describe('when calling the loadData function', () => {

//     it('should populate the selectedFacilities variable with the selected facilities from local storage',
//       inject([LocalStorageService], (mockLocalStorageService: MockLocalStorageService) => {
//         // Arrange
//         const localStorageSpy = spyOn(mockLocalStorageService, 'getLocalStorage').and.callFake(() => {
//           return mockLocalStorage
//         })
//         spyOn(mockLocalStorageService, 'setLocalStorage')

//         // Act
//         component.loadData();

//         // Assert
//         expect(localStorageSpy).toHaveBeenCalled();
//       }));

//     it('it should subscribe to the getOverviewData observable',
//       inject([LocalStorageService, CautiService], (mockLocalStorageService: MockLocalStorageService, mockCautiService: MockCautiService) => {
//         // Arrange
//         spyOn(mockLocalStorageService, 'getLocalStorage').and.callFake(() => {
//           return mockLocalStorage
//         })
//         spyOn(mockLocalStorageService, 'setLocalStorage')
//         const getOverviewDataSpy = spyOn(mockCautiService, 'getOverviewData').and.returnValue(of(new CautiOverviewData()));

//         // Act
//         component.loadData();

//         // Assert
//         expect(getOverviewDataSpy).toHaveBeenCalled();
//       }));
//     });

//   describe('when calling the setLocalStorage method', () => {
//     it('should set 2 localstorage properties to start and end dates from the dateInput variable',
//       inject([LocalStorageService], (mockLocalStorageService: MockLocalStorageService) => {
//         // Arrange
//         const setSpy = spyOn(mockLocalStorageService, 'setLocalStorage').and.callFake(() => {
//           const startDate = mockDateInput.start;
//           const endDate = mockDateInput.end;
//           const mockLocalStorageHolder = JSON.parse(mockLocalStorage)
//           mockLocalStorageHolder.cautiSelectedStartDate = startDate;
//           mockLocalStorageHolder.cautiSelectedEndDate = endDate;
//           mockLocalStorage = JSON.stringify(mockLocalStorageHolder);
//         })

//         // Act
//         component.setLocalStorage();

//         // Assert
//         const localStart = mockDateInput.start.toISOString();
//         const localEnd = mockDateInput.end.toISOString();
//         expect(setSpy).toHaveBeenCalledTimes(2);
//         expect(JSON.parse(mockLocalStorage).cautiSelectedStartDate).toEqual(localStart);
//         expect(JSON.parse(mockLocalStorage).cautiSelectedEndDate).toEqual(localEnd);
//     }));
//   });
// });
