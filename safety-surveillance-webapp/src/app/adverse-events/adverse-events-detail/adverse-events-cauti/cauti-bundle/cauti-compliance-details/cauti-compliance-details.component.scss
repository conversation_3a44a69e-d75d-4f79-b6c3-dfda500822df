@import "../../../../../../assets/styles/components/_select-list";
@import "../../../../../../assets/styles/_layout";
@import "../../../../../../assets/styles/components/_buttons";
@import "../../../../../../assets/styles/_colors";

.breakout-nav-container {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  justify-content: space-between;
  width: 100%;
  margin: 10px 0px 10px 0px;
}

.left-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}

.right-content {
  display: -webkit-box;
  display: -ms-flexbox;
  display: flex;
  align-items: center;
}

.spacer {
  margin-right: 10px;
}

.breakout-text-style {
  font-size: 14px;
  font-weight: 400;
  color: $hc-gray;
}

.clickable-text {
  font-size: 14px;
  font-weight: 400;
  color: $primary-blue;
  text-decoration: none;
  margin-bottom: 0px;
  cursor: pointer;
}

.selected-text {
  font-size: 14px;
  font-weight: 600;
  color: $hc-text;
  text-decoration: none;
  margin-bottom: 0px;
  cursor: pointer;
}

.x-axis-panel {
  display: flex;
  margin-right: 50px;
  min-height: 30px;
  align-self: flex-end;
  justify-content: flex-end;
  padding-bottom: 10px;
}
.x-axis-title {
 font-weight: 600;
 float: left;
 margin-top: 5px;
 margin-right: 5px;
}
