import { FilterOption } from './../../../../shared/filter/filter-option';
import { CautiFilterBodyService } from './../services/cauti-filter-body.service';
import { Router } from '@angular/router';
import { LocalStorageService } from 'app/shared/services/local-storage.service';
import { Injectable } from '@angular/core';
import { CautiFilterObject } from './cauti-filter-object';


@Injectable()
export class CautiFilterService {
  private endDt;
  private startDt;
  private filterObject;

  constructor(
    private localStorageService: LocalStorageService,
    private router: Router,
    private filterBodyService: CautiFilterBodyService) {

  }

  setCautiAdvancedFilters(dates, cautiFilterObject: CautiFilterObject) {
    this.filterObject = this.filterBodyService.createNewFilterBody(cautiFilterObject.parentFilterStore);
    this.setDates(dates);

    this.filterObject.advanced = [new FilterOption(cautiFilterObject.filterOptionName, cautiFilterObject.filterOptionValue)];

    this.saveFilters(cautiFilterObject);
  }

  setCautiDateFilterOnly(dates, cautiFilterObject: CautiFilterObject) {
    this.filterObject = this.filterBodyService.createNewFilterBody(cautiFilterObject.localFilterStore);

    this.setDates(dates);

    this.saveFilters(cautiFilterObject);
  }

  private setDates(dates) {
    const date = new Date(dates);
    this.startDt = new Date(date.getFullYear(), date.getMonth(), 1);
    this.endDt = new  Date(date.getFullYear(), date.getMonth() + 1, 0);
    this.endDt.setHours(23, 59, 59, 999);
  }

  private saveFilters(cautiFilterObject: CautiFilterObject) {
    this.localStorageService.setLocalStorage(cautiFilterObject.localFilterStore, JSON.stringify(this.filterObject));
    this.localStorageService.setLocalStorage(cautiFilterObject.localStorageEnd, this.endDt.toISOString());
    this.localStorageService.setLocalStorage(cautiFilterObject.localStorageStart, this.startDt.toISOString());
    this.router.navigate([cautiFilterObject.clickAddress], {queryParams: {'linkVisitType': 'click'}});
  }
}
