import { mock, instance } from 'ts-mockito';
import { HacUtilizationSummaryBaseComponent } from './hac-utilization-summary-base.component';
import { Router, ActivatedRoute } from '@angular/router';
import { CautiService } from 'app/shared/services/cauti.service';
import { HacService } from 'app/shared/services/hac.service';
import { FilterBarService } from 'app/shared/filter/abstracts/filter-bar-service';
import { FilterChangeService } from 'app/shared/filter/filter-change-service';
import { LocalStorageService } from 'app/shared/services/local-storage.service';
import { TooltipHelper } from 'app/shared/charting/tooltip-helper';
import { CautiFilterBodyService } from '../../adverse-events-cauti/services/cauti-filter-body.service';
import { ClabsiFacilityFilterBodyService } from 'app/shared/filter/clabsi/clabsi-filters/clabsi-filter-body-service';
import { PressureInjuryFilterBodyService } from 'app/shared/filter/pressure-injury/filters/pressure-injury-filter-body-service';
import { XAxisService } from 'app/shared/services/x-axis.service';

describe('HacUtilizationSummaryBaseComponent', () => {
  const router = mock(Router);
  const activatedRoute = mock(ActivatedRoute);
  const cautiService = mock(CautiService);
  const hacService = mock(HacService);
  const filterBarService = mock(FilterBarService);
  const filterChangeService = mock(FilterChangeService);
  const localStorageService = mock(LocalStorageService);
  const tooltipHelper = mock(TooltipHelper);
  const cautiFilterBodyService = mock(CautiFilterBodyService);
  const clabsiFacilityFilterBodyService = mock(ClabsiFacilityFilterBodyService);
  const pressureInjuryFilterBodyService = mock(PressureInjuryFilterBodyService);
  const xAxisService = mock(XAxisService);

  let component: HacUtilizationSummaryBaseComponent;

  beforeEach(() => {
    component = new HacUtilizationSummaryBaseComponent(instance(router),
                                                        instance(activatedRoute),
                                                        instance(cautiService),
                                                        instance(hacService),
                                                        instance(filterBarService),
                                                        instance(filterChangeService),
                                                        instance(localStorageService),
                                                        instance(tooltipHelper),
                                                        instance(cautiFilterBodyService),
                                                        instance(clabsiFacilityFilterBodyService),
                                                        instance(pressureInjuryFilterBodyService),
                                                        instance(xAxisService));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  
});
