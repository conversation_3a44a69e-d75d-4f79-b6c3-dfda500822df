<span class="fa-stack fa-1x" (click)="openNotifications()" [popper]="popper1content" [popperTrigger]="'click'"
[popperStyles]="{'background-color': 'white', 'z-index': 13, 'padding': '0 10px'}"
[popperHideOnClickOutside]="true" [popperPlacement]="'bottom'">
  <i class="fa fa-bell fa-stack-1x fa-inverse bell"></i>
  <span class="badge" *ngIf="badge">{{badge}}</span>
</span>

<popper-content #popper1content>
    <div *ngIf="notifications && notifications.length > 0"
      infinite-scroll
      [infiniteScrollDistance]="2"
      [infiniteScrollUpDistance]="2"
      class="notifications-container"
      [alwaysCallback]="true"
      (scrolledUp)="infiniteScrollUtil.onScrollUp()"
      (scrolled)="infiniteScrollUtil.onScrollDown()"
      [scrollWindow]="false"
    >
      <notification-item-host  *ngFor="let n of notifications | slice :infiniteScrollUtil.sliceStart() :infiniteScrollUtil.sliceEnd()"
      [data]="n"
      (viewItemSelected)=popper1content.hide()>
    </notification-item-host>

    </div>
  <div class="no-notifications" *ngIf="!notifications || notifications.length === 0">
    No new notifications.
  </div>
</popper-content>
