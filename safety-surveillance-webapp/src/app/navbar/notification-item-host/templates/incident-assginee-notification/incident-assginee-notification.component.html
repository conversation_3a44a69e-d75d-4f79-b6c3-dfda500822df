<div class="notification" (click)="viewNotification()" [ngClass]="{'isNew': notification.isNew }">
  <div class="notification-text" *ngIf="templateData">
    The assignee of <span class="bold">Incident Report #{{templateData.IncidentId}}</span> that you submitted on {{templateData.SubmittedDateTime | datex :'MM/DD/yyyy'}} has changed
  </div>
  <div class="notification-text" *ngIf="!templateData">
    {{notification.notificationText}}
  </div>
  <div class="notification-footer">
    <div class="hc-link" (click)="navigateToTrigger()">
      View >
    </div>
    <div class="timestamp">
      {{notification.createdDateTime | datex: 'MM/DD/YYYY HH:mm'}}
    </div>
  </div>
</div>
