import {Component, EventEmitter, Input, OnInit, Output} from '@angular/core';
import {Router} from '@angular/router';
import {NotificationsService} from 'app/shared/services/notifications/notifications.service';
import {SelectedTriggerService} from 'app/shared/services/selected-trigger-service';
import {TriggerService} from 'app/shared/services/trigger.service';
import {NotificationItemComponent} from "../../../notification-item/notification-item.component";

@Component({
  selector: 'app-incident-status-notification',
  templateUrl: './incident-status-notification.component.html',
  styleUrls: ['./incident-status-notification.component.scss', '../../../notification-item/notification-item.component.scss']
})
export class IncidentStatusNotificationComponent extends NotificationItemComponent implements OnInit {

  constructor(private selectedTriggerService: SelectedTriggerService,
              private router: Router,
              private triggerService: TriggerService,
              notificationsService: NotificationsService) {
    super(notificationsService);
  }

  ngOnInit() {
  }

  navigateToTrigger() {
    this.viewItemSelected.emit();
    this.router.navigate([`/incident/submit/${this.templateData.IncidentId}`], {queryParams: {isReadOnly: true}});
  }

}
