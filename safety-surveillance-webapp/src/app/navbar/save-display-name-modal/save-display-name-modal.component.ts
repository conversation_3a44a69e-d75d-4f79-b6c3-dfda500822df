import {Component, Inject, OnInit} from '@angular/core';
import {SimpleModalComponent, SimpleModalService} from 'ngx-simple-modal';
import {HcToasterService, HcToastOptions} from '@healthcatalyst/cashmere';
import {AppConfigService} from 'app/shared/services/config.service';
import {SafetyUser} from 'app/shared/models/safety-user';
import {ErrorModalComponent} from 'app/shared/modals/error-modal/error-modal.component';
import {UserInfoService} from 'app/shared/services/user-info.service';
import {PsmGroupService} from 'app/shared/services/security/psm-group.service';
import {AuthService} from 'app/shared/services/auth.service';
import {Router} from "@angular/router";
import {UrlUtilities} from "../../shared/utility/url-util";
import {GroupService} from "../../shared/services/group.service";
import {UserManagementService} from "../../shared/services/user-management/user-management.service";
import {LocalStorageService} from "../../shared/services/local-storage.service";
import {LocalStorageCategory} from "../../shared/constants/local-storage-category";
import { APP_BASE_HREF } from '@angular/common';

export interface SaveDisplayNameModel {
  username: string;
}

@Component({
  selector: 'save-display-name-modal',
  templateUrl: './save-display-name-modal.component.html',
  styleUrls: ['./save-display-name-modal.component.scss']
})
export class SaveDisplayNameModalComponent extends SimpleModalComponent<SaveDisplayNameModel, string> implements OnInit {
  toasterOptions: HcToastOptions;
  readonly username: string;
  firstName: string;
  lastName: string;
  email: string;
  initialFacility: string;
  public notificationType: string | undefined | null;

  constructor(private toaster: HcToasterService,
              private userInfoService: UserInfoService,
              private psmGroupService: PsmGroupService,
              private router: Router,
              private authService: AuthService,
              private appconfigService: AppConfigService,
              private dialogService: SimpleModalService,
              private groupService: GroupService,
              private userManagementService: UserManagementService,
              private localStorageService: LocalStorageService,
              @Inject(APP_BASE_HREF) private baseHref: string) {
    super();
    this.toasterOptions = ({
      body: 'Name and Email address successfully saved',
      clickDismiss: true,
      header: 'Settings saved',
      position: 'top-full-width',
      timeout: 3000,
      type: 'success'
    });
    this.notificationType = UrlUtilities.getQueryParamByKey('notificationType');
    if (this.notificationType === 'sentinel') {
      this.email = UrlUtilities.getQueryParamByKey('email');
      this.initialFacility = UrlUtilities.getQueryParamByKey('facility');
    }
  }

  ngOnInit(): void {
  }

  onSaveClick(): void {
    console.log('Save display name');
    const user = new SafetyUser();

    user.userName = this.username;
    user.displayName = this.firstName + ' ' + this.lastName;
    user.email = this.email;
    user.createdBy = 'HealthCatalyst';

    this.appconfigService.addSafetyUser(user).subscribe(
      () => {
        this.userInfoService.userInfo = user;
        // Check if we're coming from a sentinel event notification email
        if (this.notificationType && this.notificationType === 'sentinel') {
          this.userManagementService.addSentinelUser(this.email).subscribe(
            res => { },
            error => { },
            () => {
              this.toaster.addToast(this.toasterOptions);
              setTimeout(() => {
                  this.authService.login();
                  this.localStorageService.setLocalStorage(LocalStorageCategory.selectedFacilitiesLocalStorage, this.initialFacility);
                  if (this.router.getCurrentNavigation().extractedUrl.root.hasChildren()) {
                    this.router.navigate(this.router.getCurrentNavigation().extractedUrl.root.children['primary'].segments);
                  }
                },
                3000);
            });
        }
      },
      error => this.dialogService.addModal(ErrorModalComponent, {
        errorNumber: error.status,
        errorText: error.statusText,
        errorMessage: error.error
      }),
      () => {
        if (!this.notificationType || this.notificationType !== 'sentinel') {
          this.toaster.addToast(this.toasterOptions);

          setTimeout(() => {
              // new users have access to submit an incident so we hard refresh the page
              // to load the new permissions
              window.location.href = `${this.baseHref}/`;
            },
            3000);
        }
      }
    );
    this.result = user.displayName;
    this.close();
  }
}
