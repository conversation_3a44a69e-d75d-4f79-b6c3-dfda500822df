import { ComponentFixture, TestBed } from '@angular/core/testing';

import { ConfigurableAuditDataExportConfigurationComponent } from './configurable-audit-data-export-configuration.component';

describe('ConfigurableAuditDataExportConfigurationComponent', () => {
  let component: ConfigurableAuditDataExportConfigurationComponent;
  let fixture: ComponentFixture<ConfigurableAuditDataExportConfigurationComponent>;

  beforeEach(async () => {
    await TestBed.configureTestingModule({
      declarations: [ ConfigurableAuditDataExportConfigurationComponent ]
    })
    .compileComponents();

    fixture = TestBed.createComponent(ConfigurableAuditDataExportConfigurationComponent);
    component = fixture.componentInstance;
    fixture.detectChanges();
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
});
