import { createAction, props } from "@ngrx/store";
import { IncidentProgressBarSection } from "app/incident/incident-progress-bar/incident-progress-bar.component";

export const toggleShowAllOpenReportedIncidents = createAction(
  "[Incident] Toggle Show All Open Reported Incidents"
);

export const toggleShowAssignToMeOnly = createAction(
  "[Incident] Toggle Assign To Me Only"
);

export const toggleShowDuplicates = createAction(
  "[Incident] Toggle Show Duplicates"
);

export const loadIncidentReviewRequiredPermissions = createAction(
  "[Incident] Load Review Page permissions"
);

export const loadIncidentReviewData = createAction(
  "[Incident] Load Incident Review Data"
);

export const clearIncidentReviewData = createAction(
  "[Incident] Clear Incident Review Data"
);

export const setIncidentReviewListProperties = createAction('[Incident] Set Incident Review List Properties', props<{ pageNumber: number, encounterId: string }>());

export const getReportedIncidents = createAction(
  "[Incident] Get Reported Incidents",
  props<{ facilities: string[] }>()
);

export const clearReportedIncidents = createAction(
  "[Incident] Clear Reported Incidents"
);

export const setProgressBarSection = createAction(
  '[Incident] Set Progress Bar Section',
  props<{ section: IncidentProgressBarSection }>()
);

export const setIncidentStateVariables = createAction(
  '[Incident] Set State Variables',
  props<{
      showActionPlanning: boolean
  }>()
);