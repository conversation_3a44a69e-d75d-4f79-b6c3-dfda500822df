@import "../../../assets/styles/colors";

.documentation-progress-bar {
    
    display: flex;
    flex-direction: row;
    align-items: center;
    gap: 20px;

    &-area {
        flex-grow: 1;

        .bar-with-ap {
            display: flex;
            flex-direction: row;
            align-items: flex-start;
            gap: 50px;

            .bar-item {
                display: flex;
                flex-direction: column;
                text-align: center;
                cursor: pointer;
                min-width: 235px;

                &.active {
                    cursor: default;
                    pointer-events: none;

                    .bar-item-title {
                        color: $gray-700;
                        font-weight: 600;
                    }

                    .bar-item-circle {
                        background-color: rgb(80, 80, 80);
                        border: 1px solid rgb(80, 80, 80);
                    }
                }

                &-circle {
                    height: 20px;
                    width: 20px;
                    border-radius: 50%;
                    margin: auto;
                    z-index: 10;
                    top: 0;
                    position: absolute;
                    left: calc(50% - 10px);
                    background-color: #d6d6d6;
                    border: 1px solid $primary-blue;
                }

                &-top {
                    position: relative;
                    height: 20px;
                }

                &-bar {
                    position: absolute;
                    top: 7px;
                    width: calc(50% + 25px);
                    height: 6px;
                    background-color: #d6d6d6;
                }

                &-title {
                    font-size: 16px;
                    font-weight: 500;
                    color: $primary-blue;
                }
            }

            .review-section {
                .bar-item-bar {
                    left: 50%;
                }
            }

            .ap-section {
                .bar-item-bar {
                    right: 50%;
                }
            }
        }

        .bar-review-only {
            display: flex;
            flex-direction: column;

            .bar-item-title {
                font-size: 16px;
                font-weight: 600;
                color: $gray-700;
            }
        }
    }

    .last-saved-dts {
        flex-shrink: 1;
        background-color: $primary-green;
        color: #fff;
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 5px;
        border-radius: 15px;
        padding: 3px 10px;
    }
}
