import { AfterViewInit, Component, On<PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { FilterBarService } from 'app/shared/filter/abstracts/filter-bar-service';
import { HacFilterBarService } from 'app/shared/filter/cauti/cauti-filter-bar/hac-filter-bar.service';
import { FilterConfigService, FILTER_CONFIG } from 'app/shared/filter/clabsi/clabsi-filter-bar/filter-config.service';
import { FILTER_STORAGE } from 'app/shared/filter/adverse-events/adverse-event-filters/adverse-event-filter-creator-util';
import { FilterConfigSettings } from 'app/shared/filter/cauti/cauti-filter-bar/filter-config-settings';
import { map, startWith, take, withLatestFrom } from 'rxjs/operators';
import { FilterChangeService } from 'app/shared/filter/filter-change-service';
import { ConfigService } from 'app/shared/services/app-config.service';
import { AdverseEventsFilterBarService } from 'app/shared/filter/adverse-events/adverse-events-bar/adverse-events-filter-bar-service';
import { RoundStateService } from 'app/shared/services/round/round-state-service';
import { DestroyableComponent } from '@utils/destroyable.component';
import { Store } from '@ngrx/store';
import { IncidentPageActions } from 'app/incident/state/actions';
import { getReportedIncidents } from 'app/incident/state';
import { AssociatedPerson } from '@models/incidents/associated-person-model';
import { TriggerReviewService } from 'app/shared/services/trigger-review.service';
import { SafetyUser } from '@models/safety-user';
import { HcSort, HcSortable, HcTableDataSource, PaginationComponent, Sort } from '@healthcatalyst/cashmere';
import { LocalStorageService } from 'app/shared/services/local-storage.service';
import { ErrorModalComponent } from 'app/shared/modals/error-modal/error-modal.component';
import { SimpleModalService } from 'ngx-simple-modal';
import { LocalStorageCategory } from 'app/shared/constants/local-storage-category';
import { BehaviorSubject } from 'rxjs';
import { IncidentLinkState } from '@models/incidents/incident-link-state';
import { LinkIncidentsService } from 'app/shared/services/incidents/link-incidents.service';
import { LinkState } from '@models/incidents/link-state';
import { AppState } from '@appStore/app.state';
import { selectCurrentFacilities } from '@appStore/facility-select/facility-select.reducer';

export interface ReportedIncidentListDto {
  incidentId: number;
  submittedDate: Date;
  status: string;
  assignedTo: string[];
  assignedToDisplayNames: string[];
  occuredOn: Date;
  isAnonymous: boolean;
  associatedLocations: string[];
  description: string;
  associatedPerson: AssociatedPerson;
  individualOrEntityName: string;
  isDuplicate: boolean;
  facility: string;
}

@Component({
  selector: 'incident-reported',
  templateUrl: './incident-reported.component.html',
  styleUrls: ['./incident-reported.component.scss'],
  providers: [
    HacFilterBarService,
    { provide: AdverseEventsFilterBarService, useExisting: HacFilterBarService },
    { provide: FilterBarService, useExisting: HacFilterBarService },
    { provide: FilterConfigService, useClass: FilterConfigService },
    { provide: FILTER_CONFIG, useValue: FilterConfigSettings.incidentReported },
    { provide: FILTER_STORAGE, useValue: FilterConfigSettings.incidentReported.filterStorage },
  ]
})
export class IncidentReportedComponent extends DestroyableComponent implements OnInit, AfterViewInit, OnDestroy {
  public healthSystem: string;
  public selectedFacilities: string[];

  public readonly reportedIncidents$ = this._store.select(getReportedIncidents);

  public reportedIncidents: ReportedIncidentListDto[];

  public displayedColumns: string[] = ['status', 'assignedToDisplayNames', 'individualOrEntityName', 'description', 'occuredOn', 'associatedLocations', 'incidentId', 'submittedDate'];
  public displayedColumnsWithFacility: string[] = ['facility', 'status', 'assignedToDisplayNames', 'individualOrEntityName', 'description', 'occuredOn', 'associatedLocations', 'incidentId', 'submittedDate'];
  public dataSource: HcTableDataSource<ReportedIncidentListDto>;
  private sortByLocalStorageName = LocalStorageCategory.reportedIncidentsSortBy;
  private sortOrderLocalStorageName: string = LocalStorageCategory.reportedIncidentsOrderBy;

  public pageNumber = 1;
  public pageOpts = [50];

  @ViewChild(PaginationComponent)
  public paginator: PaginationComponent;

  @ViewChild(HcSort, { static: true })
  private readonly _sort: HcSort;

  get length(): number {
    return this.reportedIncidents?.length || 0;
  }

  private _isLoaded = false;

  public popoverLinkedIncidents$ = new BehaviorSubject<{
    primaryIncidentId: number,
    currentIncidentId: number
  }>(null);

  public selectedFacilities$ = this._store.select(selectCurrentFacilities);

  constructor(
    private readonly _filterService: FilterBarService,
    private readonly _filterChangeService: FilterChangeService,
    private readonly _configService: ConfigService,
    private readonly _router: Router,
    private readonly _activatedRoute: ActivatedRoute,
    private readonly _store: Store<AppState>,
    private readonly _triggerReviewService: TriggerReviewService,
    private readonly _localStorageService: LocalStorageService,
    private readonly _dialogService: SimpleModalService,
    private readonly _linkIncidentsService: LinkIncidentsService
  ) {
    super();
  }

  public ngOnInit(): void {
    this._store.dispatch(IncidentPageActions.clearReportedIncidents());
    this.healthSystem = this._configService.config.healthSystem;
    this._filterChangeService.changeEvent.pipe(startWith(true)).subscribe(c => this.loadData());

    this.selectedFacilities$.pipe(this.takeUntilDestroyed()).subscribe(data => {
      this.selectedFacilities = data;
      this.loadData();
    });
  }

  public ngAfterViewInit(): void {
    this.reportedIncidents$
      .pipe(
        withLatestFrom(this._triggerReviewService.getSafetyUsers()),
        map(([reportedIncidents, safetyUsers]) => reportedIncidents.map(incident => {
          const incidentTableData = { ...incident } as ReportedIncidentListDto;
          return this._formatDataForTable(incidentTableData, safetyUsers);
        }))
      )
      .subscribe(
        (reportedIncidents) => {
          if (!this.reportedIncidents?.length && reportedIncidents?.length > 0 && !this._isLoaded) {
              if (this.paginator) {
                this._formatTable(reportedIncidents, true);
                this._isLoaded = true;
              }
          } else {
            this._formatTable(reportedIncidents);
          }

          this.reportedIncidents = reportedIncidents;
        },
        (error: any) =>
          this._dialogService.addModal(ErrorModalComponent, {
            errorNumber: error.status,
            errorText: error.statusText,
            errorMessage: error.error,
          })
      );
  }

  private _formatTable(reportedIncidents: ReportedIncidentListDto[], applySort: boolean = false): void {
    const previousSortBy = this._localStorageService.getLocalStorage(this.sortByLocalStorageName) || 'occuredOn';
    const previousOrder = this._localStorageService.getLocalStorage(this.sortOrderLocalStorageName) === 'asc' ? 'asc' : 'desc';

    if (reportedIncidents?.length && this.paginator && this._sort) {
      if (applySort) {
        const sortable: HcSortable = {
          id: previousSortBy,
          start: previousOrder as 'asc' | 'desc',
          disableClear: true
        };

        this._sort.sort(sortable);
      }

      const source = new HcTableDataSource(reportedIncidents);
      source.paginator = this.paginator;
      source.sort = this._sort;
      this.dataSource = source;

    }
  }

  private _formatDataForTable(listData: ReportedIncidentListDto, safetyUsers: SafetyUser[]): ReportedIncidentListDto {
    if (listData.assignedTo) {

      if (Array.isArray(listData.assignedTo)) {
        listData.assignedToDisplayNames = this._matchUsers(listData.assignedTo, safetyUsers);
      }
    }
    (<any>listData).individualOrEntityName = listData.associatedPerson?.personTypeId === 7 ? listData.associatedPerson?.entityName : `${listData.associatedPerson?.lastName}, ${listData.associatedPerson?.firstName}`;


    return listData;
  }

  private _matchUsers(users: string[], safetyUsers: SafetyUser[]): string[] {
    const displayNames: string[] = [];
    users.map(user => {
      const matchingUser = safetyUsers.find(x => x.userName.toLowerCase() === user.toLowerCase());

      if (matchingUser?.displayName) {
        displayNames.push(matchingUser?.displayName);
      }

      return;
    });

    return displayNames;
  }

  public scrollToPaginator(): void {
    if (this._isLoaded) {
      const element = document.getElementById('pso-footer');
      if (document.contains(element)) {
        setTimeout(() => {
          element.scrollIntoView({ block: "end", inline: "nearest" });
        }, 1);
      }
    }
  }

  public onClickSort(sort: Sort): void {
    this._localStorageService.setLocalStorage(this.sortByLocalStorageName, sort.active);
    this._localStorageService.setLocalStorage(this.sortOrderLocalStorageName, sort.direction);
  }

  public loadData(): void {
    const facilities = this.selectedFacilities || this._filterService.getFacilities();
    this._store.dispatch(IncidentPageActions.getReportedIncidents({ facilities: facilities }));
  }

  public ngOnDestroy(): void {
    super.ngOnDestroy();
  }

  public onIncidentClick(item: ReportedIncidentListDto): void {
    this._router.navigate([`/incident/submit/${item.incidentId}`],
      { relativeTo: this._activatedRoute, queryParams: { isReadOnly: !!item.submittedDate } });
  }

  public getIncidentLinks(incidentId: number): void {
    this._linkIncidentsService
      .getLinkedIncidents(
        incidentId
      )
      .pipe(take(1))
      .subscribe((incidentLinkStates) => {
        this.popoverLinkedIncidents$.next({
          primaryIncidentId: incidentLinkStates.find(x => x.linkState === LinkState.Primary)?.incidentId,
          currentIncidentId: incidentId
        });
      });
  }

  public popoverClosed(): void {
    this.popoverLinkedIncidents$.next(null);
  }
}
