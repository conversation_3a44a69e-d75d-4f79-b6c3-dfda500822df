import { mock, instance } from 'ts-mockito';
import { AppStartComponent } from './app-start.component';
import { Router } from '@angular/router';
import { AppConfigService } from './shared/services/config.service';
import { RoutingService } from './shared/services/routing-service';
import { PermissionsService } from './shared/services/security/permissions.service';
import { PsmAuthService } from './shared/services/security/psm-auth.service';

describe('AppStartComponent', () => {
  const router = mock(Router);
  const appConfigService = mock(AppConfigService);
  const routingSerivce =  mock(RoutingService);
  const permissionService = mock(PsmAuthService);

  let component: AppStartComponent;

  beforeEach(() => {
    component = new AppStartComponent(instance(router), instance(appConfigService), instance(routingSerivce), instance(permissionService));
  });

  it('should create', () => {
    expect(component).toBeTruthy();
  });
  
});
