import { mock, instance } from 'ts-mockito';
import { ImprovementService } from './improvement.service';
import { DateRangeService } from 'app/shared/services/date-range.service';
import { HttpService } from 'app/shared/services/http.service';

describe('ImprovementService', () => {
  const httpService = mock(HttpService);
  const dateRangeService = mock(DateRangeService);

  let service: ImprovementService;

  beforeEach(() => {
    service = new ImprovementService(instance(httpService), instance(dateRangeService));
  });

  it('should create', () => {
    expect(service).toBeTruthy();
  });
  
});
