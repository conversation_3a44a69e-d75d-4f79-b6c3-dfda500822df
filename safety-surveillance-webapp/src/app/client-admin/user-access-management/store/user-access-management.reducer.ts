import { createReducer, on } from "@ngrx/store";
import { UserAccessManagementActions } from "./actions";
import {FacilityUser} from "@models/client-admin/user-management/facility-user";
import { FeatureFlagsType } from "../feature-flags.type";

export interface UserAccessManagementState {
    users: FacilityUser[];
    usersLoadStatus: 'NOT_LOADED' | 'LOADING' | 'LOADED';
    featureFlags: FeatureFlagsType;
    loading: boolean;
    showUnassignedUsers: boolean;
  }

  const initialState: UserAccessManagementState = {
    users: null,
    usersLoadStatus: 'NOT_LOADED',
    featureFlags: { IncidentFlag: false },
    loading: false,
    showUnassignedUsers: false
  };

  export const userAccessManagementReducer = createReducer<UserAccessManagementState>(
    initialState,
    on(UserAccessManagementActions.loadUserList, (state) => {
      return {
        ...state,
        loading: true,
        usersLoadStatus: 'LOADING'
      };
    }),
    on(UserAccessManagementActions.loadUserListSuccess, (state, { users }) => {
      return {
        ...state,
        loading: false,
        users: users,
        usersLoadStatus: 'LOADED'
      };
    }),
    on(UserAccessManagementActions.loadUserListFailure, (state, { errors }) => {
      return {
        ...state,
        loading: false,
        usersLoadStatus: 'NOT_LOADED'
      };
    }),
    on(UserAccessManagementActions.loadNeededFeatureFlagsSuccess, (state, { updatedFeatureFlags }) => {
      return {
        ...state,
        featureFlags: {...updatedFeatureFlags},
        loading: false
      };
    }),
    on(UserAccessManagementActions.loadNeededFeatureFlagsFailure, (state, { errors }) => {
      return {
        ...state,
        loading: false
      };
    }),
    on(UserAccessManagementActions.toggleUnassignedUsers, (state, { showUnassignedUsers }) => {
      return {
        ...state,
        showUnassignedUsers
      };
    })
  );
