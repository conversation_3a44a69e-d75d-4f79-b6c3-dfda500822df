#edit-users-modal {
    .hc-modal-body {
        padding: 0px !important;
        overflow: visible;
    }

    .scrollable {
        padding: 15px 25px;
        height: auto;

        .add-row {
            margin-top: 15px;
            padding: 0;
            min-width: 50px;
        }
    }

    .main-form {
        display: flex;
        flex-direction: column;
        gap: 15px;

        .entry-row {
            display: flex;
            flex-direction: column;
            gap: 10px;

            .text-row,
            .multi-select-row {
                display: flex;
                flex-direction: row;
                gap: 5px;
            }

            .text-row {
                hc-form-field {
                    flex-basis: 25%;
                }
            }

            .multi-select-column {
                display: flex;
                flex-direction: column;
                gap: 5px;

                .multi-select-label {
                    font-size: 1rem;
                }

                .multi-select-row {
                    ng-select {
                        width: 100%;
                    }
                }
            }

            .action-button {
                min-width: 50px;
                width: 50px;
            }
        }
    }
}
