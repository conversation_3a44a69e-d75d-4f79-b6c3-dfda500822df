<hc-modal id="add-custom-locations-modal">
    <hc-modal-header>Add Custom Locations</hc-modal-header>
    <hc-modal-body>
        <hc-form-field>
            <hc-label>Custom Location Name - (one per line):</hc-label>
            <textarea [formControl]="locations" required hcInput rows="12" cols="5"></textarea>
        </hc-form-field>
        These should only be names of locations in your facility.
    </hc-modal-body>
    <hc-modal-footer>
        <button hc-button buttonStyle="link" (click)="cancel()">Cancel</button>
        <button [disabled]="!locations?.valid || (loading$ | async) === true" hc-button buttonStyle="primary"
            (click)="save()">
            Save
        </button>
    </hc-modal-footer>
</hc-modal>