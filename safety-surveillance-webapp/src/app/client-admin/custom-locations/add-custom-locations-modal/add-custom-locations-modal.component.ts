import { AfterViewInit, ChangeDetectionStrategy, Component } from '@angular/core';
import { UntypedFormControl } from '@angular/forms';
import { ActiveModal } from '@healthcatalyst/cashmere';
import { AddCustomLocationsResponseDTO } from '@models/client-admin/add-custom-locations-response-dto';
import { DestroyableComponent } from '@utils/destroyable.component';
import { CustomLocationService } from 'app/shared/services/client-admin/custom-location.service';
import { BehaviorSubject } from 'rxjs';
import { exhaustMap, filter, map } from 'rxjs/operators';

@Component({
  selector: 'add-custom-locations-modal',
  templateUrl: './add-custom-locations-modal.component.html',
  styleUrls: ['./add-custom-locations-modal.component.scss'],
  changeDetection: ChangeDetectionStrategy.OnPush
})
export class AddCustomLocationsModalComponent extends DestroyableComponent implements AfterViewInit {

  public locations = new UntypedFormControl();
  public readonly loading$ = new BehaviorSubject<boolean>(false);

  constructor(
    public readonly activeModal: ActiveModal<string, AddCustomLocationsResponseDTO>,
    private readonly _customLocationService: CustomLocationService
  ) {
    super();
  }

  public ngAfterViewInit(): void {
    this.loading$.pipe(
      filter(loading => loading === true && this.locations.valid && this.locations.value),
      map(() => (this.locations.value as string).split(/\n/).filter(x => x)),
      exhaustMap(locationNames => {
        return this._customLocationService.addCustomLocations(locationNames, this.activeModal.data);
      }),
      this.takeUntilDestroyed()
    ).subscribe(response => {
      if (response) {
        this.activeModal.close(response);
      }
    },
      // Error handling
      () => {
        this.loading$.next(false);
      });
  }

  public save(): void {
    this.loading$.next(true);
  }

  public cancel(): void {
    this.activeModal.dismiss();
  }

}
