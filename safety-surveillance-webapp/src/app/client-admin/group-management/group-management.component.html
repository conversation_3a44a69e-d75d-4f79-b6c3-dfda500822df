<div id="group-management">
    <h3>
        <a class="client-admin-breadcrumb" routerLink="/client-admin">Client Admin</a>
        <i class="fa fa-chevron-right"></i>
        <span>Group Management</span>
    </h3>
    <div *ngIf="groupManagementFilterType$ | async as filterType" class="group-management-filter-type">
        <div class="group-management-filter-type-option" [ngClass]="{'selectable': filterType !== 1}" (click)="setFilterType(1)">
            All Groups
        </div>
        |
        <div class="group-management-filter-type-option" [ngClass]="{'selectable': filterType !== 2}" (click)="setFilterType(2)">
            Facility Access Groups
            <hc-icon style="color: #000" fontSet="hc-icons" fontIcon="hci-hospital-tall" hcIconSm></hc-icon>
        </div>
        |
        <div class="group-management-filter-type-option" [ngClass]="{'selectable': filterType !== 3}" (click)="setFilterType(3)">
            Role Permission Groups
            <img src="assets/icons/person-lock.svg" height="15">
        </div>
    </div>
    <hc-search-bar placeholder="Search Groups" (triggerSearch)="search($event)"></hc-search-bar>
    <div class="group-management-list">
        <div class="group-entry" *ngFor="let group of (groups$ | async)">
            <div class="group-entry-icon">
                <img *ngIf="!group?.isFacilityAccessGroup" src="assets/icons/person-lock.svg" height="20" hcTooltip="Role Permission Group">
                <hc-icon *ngIf="group?.isFacilityAccessGroup" fontSet="hc-icons" fontIcon="hci-hospital-tall" hcIconMd hcTooltip="Facility Access Group"></hc-icon>
            </div>
            <div class="group-entry-heading">{{group?.groupName}}</div>
            <div class="group-entry-link" [routerLink]="['group-detail', group?.groupId]">
                <i class="fa fa-users"></i>
                <span class="group-entry-link-blue">Manage Group</span>
                <span>{{group?.userCount}} users assigned</span>
                <i class="fa fa-chevron-right group-entry-link-blue"></i>
            </div>
        </div>
    </div>
</div>