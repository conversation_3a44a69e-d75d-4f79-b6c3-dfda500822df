@import "src/assets/styles/_colors";
@import "src/assets/styles/components/_buttons";
@import "../styles/client-admin-mixins.scss";

#group-management {
    display: flex;
    flex-direction: column;
    flex: 1;
    gap: 10px;

    @include client-admin-heading();

    .group-management-filter-type {
        display: flex;
        flex-direction: row;
        align-items: center;
        flex-shrink: 1;
        gap: 5px;
        font-size: 15px;

        &-option {
            display: flex;
            flex-direction: row;
            align-items: center;
            gap: 3px;
        }

        .selectable {
            color: $primary-blue;
            font-weight: 600;

            &:hover {
                cursor: pointer;
                color: $button-blue;
            }
        }
    }

    .group-management-list {
        display: flex;
        flex-direction: row;
        align-items: stretch;
        flex-wrap: wrap;
        gap: 20px;

        .group-entry {
            display: flex;
            flex-direction: column;
            flex-basis: calc(33% - 10px);
            align-items: center;
            gap: 10px;

            border-radius: 1px;
            border: 1px solid $gray-200;
            padding: 15px;

            background-color: #fff;

            position: relative;
            padding-bottom: 25px;

            &-icon {
                position: absolute;
                top: 15px;
                right: 15px;
            }

            &-link {
                display: flex;
                flex-direction: row;
                padding: 20px;
                align-items: center;
                gap: 10px;
                border: 1px solid $gray-200;
                border-radius: 2px;

                &-blue {
                    color: $primary-blue;
                    font-weight: 500;
                }

                &:hover {
                    cursor: pointer;
                    background-color: rgb(245, 245, 245);
                    transition: background-color 100ms ease-in-out;
                }
            }

            &-heading {
                font-size: 18px;
                font-weight: 600;
            }

            &-description {
                font-size: 16px;
                color: $gray-600;
            }
        }
    }
}
