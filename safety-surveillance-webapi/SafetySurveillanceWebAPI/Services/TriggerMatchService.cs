using SafetySurveillanceWebApi.Models.Authorize;

namespace SafetySurveillanceWebApi.Services;

using Microsoft.EntityFrameworkCore;

using SafetySurveillanceWebApi.Data;
using SafetySurveillanceWebApi.Models;
using SafetySurveillanceWebApi.Models.Constants;
using SafetySurveillanceWebApi.Repositories;
using SafetySurveillanceWebApi.Models.PublicHealth;
using SafetySurveillanceWebApi.Repositories.PublicHealth;

public class TriggerMatchService : ITriggerMatchService
{
    private readonly ISafetySurveillanceApplicationContext safetySurveillanceApplicationContext;

    private readonly ITriggerReviewRepository triggerReviewRepository;
    private readonly IPublicHealthRepository publicHealthReviewRepository;

    private readonly IApplicationConfigurationRepository appConfigRepository;

    public TriggerMatchService(
        ISafetySurveillanceApplicationContext safetySurveillanceApplicationContext,
        ITriggerReviewRepository triggerReviewRepository,
        IApplicationConfigurationRepository appConfigRepository,
        IPublicHealthRepository publicHealthRepository)
    {
        this.triggerReviewRepository = triggerReviewRepository;
        this.appConfigRepository = appConfigRepository;
        this.safetySurveillanceApplicationContext = safetySurveillanceApplicationContext;
        this.publicHealthReviewRepository = publicHealthRepository;
    }

    public async Task<List<TriggerDetail>> MatchTriggerReviews(
        List<TriggerDetail> triggerEvents, DateRange dateRange,
        string surveillanceType,
        bool positiveReviewsOnly = false,
        bool filterByAeDate = false)
    {
        IEnumerable<TriggerEventToReview> triggerEventToReviewLinks;

        IEnumerable<PublicHealthToReview> publicHealthToReviewLinks;

        publicHealthToReviewLinks =
            await this.triggerReviewRepository.GetPublicHealthToReviewsForTriggerTimeFrameAsync(dateRange, surveillanceType);

        if (!filterByAeDate)
        {
            triggerEventToReviewLinks =
                await this.triggerReviewRepository.GetTriggerEventToReviewsForTriggerTimeFrameAsync(dateRange, surveillanceType);
        }
        else
        {
            triggerEventToReviewLinks =
                await this.triggerReviewRepository.GetTriggerEventToReviewsForAeTimeFrameAsync(dateRange);
        }

        if (positiveReviewsOnly)
        {
            triggerEventToReviewLinks =
                triggerEventToReviewLinks.Where(link => link.TriggerEventReview != null && link.TriggerEventReview.IsAdverseEvent == AdverseEventType.EventDetected);
        }

        if (triggerEventToReviewLinks == null)
        {
            return triggerEvents;
        }

        foreach (var triggerEventToReview in triggerEventToReviewLinks)
        {
            if (triggerEventToReview.TriggerEventReview?.ActionPlanning == null)
            {
                continue;
            }

            triggerEventToReview.TriggerEventReview.ActionPlanning.ContributingFactors =
                triggerEventToReview.TriggerEventReview.ReviewToRootCauseOptions
                    .Where(i => i.Option.Category.Name == "Contributing Factors")
                    .ToList();
        }

        foreach (var triggerEvent in triggerEvents)
        {
            var foundLink = triggerEventToReviewLinks.FirstOrDefault(
                link => link.TriggerId == triggerEvent.TriggerId
                        && link.PatientEncounterId.ToLowerInvariant() == triggerEvent.PatientEncounterId.ToLowerInvariant()
                        && link.TriggerSourceDataId.ToLowerInvariant() == triggerEvent.TriggerSourceDataId.ToLowerInvariant()
                        && link.TriggerEventReview != null);

            var publicLink = publicHealthToReviewLinks.FirstOrDefault(
                link => link.TriggerId == triggerEvent.TriggerId
                        && link.PatientEncounterId.ToLowerInvariant() == triggerEvent.PatientEncounterId.ToLowerInvariant()
                        && link.TriggerSourceDataId.ToLowerInvariant() == triggerEvent.TriggerSourceDataId.ToLowerInvariant());

            if (publicLink != null)
            {
                triggerEvent.PublicHealthEventReview = publicLink.PublicHealthEventReview;
            }

            if (foundLink != null && foundLink.TriggerEventReview != null && !foundLink.TriggerEventReview.IsDeleted)
            {
                triggerEvent.TriggerEventReview = foundLink.TriggerEventReview;
            }
        }

        // Taylor wants to redesign this later
        if (positiveReviewsOnly)
        {
            triggerEvents = surveillanceType == SurveillanceTypeConstants.PublicHealth ? triggerEvents.Where(t => t.PublicHealthEventReview != null).ToList() : triggerEvents.Where(t => t.TriggerEventReview != null).ToList();
        }

        return triggerEvents;
    }

    public async Task<List<TriggerDetail>> MatchPublicHealthEventReviewForPatientEncounter(List<TriggerDetail> triggerEvents, string patientEncounterId)
    {
        var publicHealthToReviewLinks = await this.safetySurveillanceApplicationContext.PublicHealthToReviews
            .Include(ph => ph.PublicHealthEventReview).Where(link => link.PatientEncounterId == patientEncounterId)
            .ToListAsync();

        if (publicHealthToReviewLinks == null)
        {
            return triggerEvents;
        }

        foreach (var triggerEvent in triggerEvents)
        {
            triggerEvent.PublicHealthEventReview = publicHealthToReviewLinks.FirstOrDefault(
                link => link.TriggerId == triggerEvent.TriggerId &&
                        link.PatientEncounterId == triggerEvent.PatientEncounterId &&
                        link.TriggerSourceDataId == triggerEvent.TriggerSourceDataId)?.PublicHealthEventReview;

            string surveillenceType = await this.safetySurveillanceApplicationContext.TriggerReferences
                .Where(ph => ph.TriggerId == triggerEvent.TriggerId).Select(ph => ph.SurveillanceType.ToLower()).FirstOrDefaultAsync();

            if(triggerEvent.PublicHealthEventReview != null)
            {
                triggerEvent.PublicHealthEventReview.AssociatedProviders = await this
                    .safetySurveillanceApplicationContext.AssociatedProviders.Include(ap => ap.Provider).Where(ap =>
                        ap.TriggerReviewId == triggerEvent.PublicHealthEventReview.TriggerReviewId &&
                        ap.EventType == surveillenceType).ToListAsync();
            }
        }

        return triggerEvents;
    }

    public async Task<List<TriggerDetail>> MatchTriggerReviewsForPatientEncounter(
        List<TriggerDetail> triggerEvents,
        string patientEncounterId)
    {
        var triggerEventToReviewLinks = await this.safetySurveillanceApplicationContext.TriggerEventToReviews
            .Include(t => t.TriggerEventReview).ThenInclude(d => d.AssociatedDevices).ThenInclude(ad => ad.Device)
            .Include(t => t.TriggerEventReview).ThenInclude(d => d.AssociatedUnits)
            .Include(t => t.TriggerEventReview).ThenInclude(d => d.AssociatedServiceLines)
            .Include(t => t.TriggerEventReview).ThenInclude(d => d.AssociatedProviders).ThenInclude(ad => ad.Provider)
            .Include(t => t.TriggerEventReview).ThenInclude(d => d.ReviewToRootCauseOptions).ThenInclude(ad => ad.Option)
            .Include(t => t.TriggerEventReview).ThenInclude(ter => ter.TriggerReviewFormData).ThenInclude(trfd => trfd.FormData).ThenInclude(fd => fd.FormDefinition)
            .Where(link => link.PatientEncounterId == patientEncounterId)
            .ToListAsync();

        if (triggerEventToReviewLinks == null)
        {
            return triggerEvents;
        }

        foreach (var triggerEvent in triggerEvents)
        {
            triggerEvent.TriggerEventReview = triggerEventToReviewLinks.FirstOrDefault(
                    link => link.TriggerId == triggerEvent.TriggerId
                            && link.PatientEncounterId.ToLowerInvariant() == triggerEvent.PatientEncounterId.ToLowerInvariant()
                            && link.TriggerSourceDataId.ToLowerInvariant() == triggerEvent.TriggerSourceDataId.ToLowerInvariant()
                            && link.TriggerEventReview != null
                            && link.TriggerEventReview?.IsDeleted == false)
                ?.TriggerEventReview;

            if (triggerEvent.TriggerEventReview != null)
            {
                string reviewType = await this.safetySurveillanceApplicationContext.TriggerReferences
                    .Where(t => t.TriggerId == triggerEvent.TriggerId).Select(t => t.SurveillanceType.ToLower()).FirstOrDefaultAsync();

                triggerEvent.TriggerEventReview.AssociatedProviders =
                    await this.safetySurveillanceApplicationContext.AssociatedProviders.Include(ap => ap.Provider).Where(ap =>
                        ap.TriggerReviewId == triggerEvent.TriggerEventReview.ReviewId && ap.EventType == reviewType).ToListAsync();
            }
        }

        return triggerEvents;
    }

    public async Task<List<TriggerDetail>> MatchTriggerAssignee(string userId, string surveillanceType)
    {
        List<TriggerEventAssignment> eventAssignments =
            this.triggerReviewRepository.GetTriggerEventAssignmentsByIdAsync(userId).Result;

        var triggerIdsToDisplay = this.appConfigRepository.GetSelectedTriggerIds().Result;

        List<TriggerDetail> triggerEvents = new List<TriggerDetail>();

        foreach (TriggerEventAssignment assignee in eventAssignments)
        {
            // ReSharper disable once RedundantAssignment
            var addReview = new TriggerDetail();

            addReview = this.safetySurveillanceApplicationContext.TriggerEvents.Include(c => c.Encounter)
                .Include(tr => tr.TriggerReference).FirstOrDefault(
                    triggerEvent => triggerEvent.TriggerReference.SurveillanceType == surveillanceType
                                    && triggerEvent.TriggerId == assignee.TriggerId
                                    && triggerEvent.PatientEncounterId.ToLower() == assignee.PatientEncounterId.ToLower()
                                    && triggerEvent.TriggerSourceDataId.ToLower() == assignee.TriggerSourceDataId.ToLower()
                                    && triggerIdsToDisplay.Contains(assignee.TriggerId));

            if (addReview != null)
            {
                triggerEvents.Add(addReview);
            }
        }

        foreach (var triggerEvent in triggerEvents)
        {
            triggerEvent.TriggerEventReview = await this.triggerReviewRepository.GetTriggerEventReviewAsync(
                triggerEvent.TriggerId, triggerEvent.PatientEncounterId, triggerEvent.TriggerSourceDataId);

            triggerEvent.PublicHealthEventReview =
                await this.publicHealthReviewRepository.GetPublicHealthEventReviewAsync(triggerEvent.TriggerId,
                    triggerEvent.PatientEncounterId, triggerEvent.TriggerSourceDataId);

        }

        return triggerEvents;
    }

    public async Task<List<TriggerDetail>> MatchPatientWatch(List<string> facilities, string userId, string surveillanceType)
    {
        List<PatientWatch> patientWatches =
            await this.safetySurveillanceApplicationContext.PatientWatches.Where(i => i.UserName == userId)
                .ToListAsync();

        List<string> selectedMrn = patientWatches.Select(m => m.Mrn).ToList();

        List<string> patientEncounters = await this.safetySurveillanceApplicationContext.CommonEncounters
            .Where(m => selectedMrn.Contains(m.Mrn)).Select(a => a.EncounterId).ToListAsync();

        var triggerIdsToDisplay = await this.appConfigRepository.GetSelectedTriggerIds();

        List<TriggerDetail> triggerReviews = await this.safetySurveillanceApplicationContext.TriggerEvents
            .Include(c => c.Encounter).Include(tr => tr.TriggerReference)
            .Where(
                i => patientEncounters.Contains(i.PatientEncounterId)
                     && triggerIdsToDisplay.Contains(i.TriggerId)
                     && facilities.Contains(i.Encounter.Location)
                     && i.TriggerReference.SurveillanceType
                     == surveillanceType).ToListAsync();

        foreach (var triggerEvent in triggerReviews)
        {
            if (triggerEvent != null)
            {
                triggerEvent.TriggerEventReview = await this.triggerReviewRepository.GetTriggerEventReviewAsync(
                    triggerEvent.TriggerId, triggerEvent.PatientEncounterId, triggerEvent.TriggerSourceDataId);

                triggerEvent.PublicHealthEventReview =
                    await this.publicHealthReviewRepository.GetPublicHealthEventReviewAsync(triggerEvent.TriggerId,
                        triggerEvent.PatientEncounterId, triggerEvent.TriggerSourceDataId);
            }
        }

        return triggerReviews;
    }

    public List<TriggerDetail> MatchPatientWatches(List<TriggerDetail> triggerEvents, UserName userName)
    {
        List<PatientWatch> patientWatches =
            this.safetySurveillanceApplicationContext.PatientWatches.ToList();

        foreach (var triggerEvent in triggerEvents)
        {
            if (triggerEvent != null)
            {
                triggerEvent.PatientWatched =
                    patientWatches.Any(p => p.Mrn == (triggerEvent.Encounter?.Mrn ?? "N/A") && p.UserName == userName);
            }
        }

        return triggerEvents;
    }

    public async Task<List<TriggerDetail>> MatchTriggerReference(List<TriggerDetail> triggerEvents)
    {
        List<TriggerReference> references =
            await this.safetySurveillanceApplicationContext.TriggerReferences.ToListAsync();

        foreach (var triggerDetail in triggerEvents)
        {
            triggerDetail.TriggerReference =
                references.FirstOrDefault(reference => reference.TriggerId == triggerDetail.TriggerId);
        }

        return triggerEvents;
    }

    public List<TriggerDetail> MatchTriggerStatuses(List<TriggerDetail> triggerEvents)
    {
        List<TriggerStatus> triggerStatuses =
            this.triggerReviewRepository.GetTriggerStatusesAsync().Result;

        foreach (var triggerEvent in triggerEvents)
        {
            if (triggerEvent != null)
            {
                triggerEvent.TriggerStatus = triggerStatuses.FirstOrDefault(status =>
                    status.TriggerId == triggerEvent.TriggerId
                    && status.PatientEncounterId.ToLowerInvariant() == triggerEvent.PatientEncounterId.ToLowerInvariant()
                    && status.TriggerSourceDataId.ToLowerInvariant() == triggerEvent.TriggerSourceDataId.ToLowerInvariant());
            }
        }

        return triggerEvents;
    }

    public List<SurveyEvent> MatchSurveyTriggerDocumentationStatuses(List<SurveyEvent> surveyEvents)
    {
        List<TriggerStatus> triggerStatuses = this.triggerReviewRepository.GetIncidentStatusesAsync().Result;

        foreach (var surveyEvent in surveyEvents)
        {
            surveyEvent.DocumentationStatus = triggerStatuses.FirstOrDefault(
                status => status.TriggerSourceDataId.ToLower() == surveyEvent.SurveyEventId.ToString().ToLower());
        }

        return surveyEvents;
    }

    public List<TriggerDetail> MatchTriggerEventAssignments(List<TriggerDetail> triggerEvents)
    {
        List<TriggerEventAssignment> eventAssignments =
            this.triggerReviewRepository.GetTriggerEventAssignmentsAsync().Result;

        foreach (var triggerEvent in triggerEvents)
        {
            if (triggerEvent != null)
            {
                triggerEvent.TriggerEventAssignment = eventAssignments.FirstOrDefault(assignment =>
                    assignment.TriggerId == triggerEvent.TriggerId
                    && assignment.PatientEncounterId.ToLowerInvariant() == triggerEvent.PatientEncounterId.ToLowerInvariant()
                    && assignment.TriggerSourceDataId.ToLowerInvariant() == triggerEvent.TriggerSourceDataId.ToLowerInvariant());
            }
        }

        return triggerEvents;
    }

    public List<SurveyEvent> MatchSurveyEventAssignments(List<SurveyEvent> surveyEvents)
    {
        List<SafetyUser> users = this.safetySurveillanceApplicationContext.AuthorizeUsers.ToList();
        List<TriggerEventAssignment> eventAssignments = this.triggerReviewRepository.GetSurveyEventAssignmentsAsync().Result;

        foreach (var surveyEvent in surveyEvents)
        {
            if (surveyEvent != null)
            {
                surveyEvent.DocumentationAssignment = eventAssignments.FirstOrDefault(assignment =>
                    assignment.TriggerSourceDataId.ToLower() == surveyEvent.SurveyEventId.ToString().ToLower());
            }
        }
        surveyEvents.ForEach(c =>
            c.DocumentAssigneesDisplayNames =
                c.DocumentAssignees.Select(d => users.FirstOrDefault(e => e.UserName == d)?.DisplayName));

        return surveyEvents;
    }

    public List<TriggerDetail> ShowUniqueReviewsOnly(List<TriggerDetail> triggerEvents)
    {
        List<TriggerDetail> tempTriggers = new List<TriggerDetail>();

        List<int> reviewIds = new List<int>();

        foreach (TriggerDetail trigger in triggerEvents)
        {
            if (trigger.TriggerEventReview != null && !reviewIds.Contains(trigger.TriggerEventReview.ReviewId))
            {
                tempTriggers.Add(trigger);
                reviewIds.Add(trigger.TriggerEventReview.ReviewId);
            }
        }

        return tempTriggers;
    }

    public async Task<List<SurveyEvent>> MatchSurveyEventsCompletedDates(List<SurveyEvent> surveyEvents)
    {
        var triggerStatusLogData = await this.safetySurveillanceApplicationContext.AuditLogs
            .Where(x => x.Type == "TriggerStatus" && x.NewValue == "Completed" && x.SourceId == "9999")
            .ToListAsync();

        foreach (var surveyEvent in surveyEvents)
        {
            var completedDateTime =
                triggerStatusLogData?.Where(x => x.SourceId == surveyEvent.TriggerEventToReview.TriggerId
                                                && x.PatientEncounterId ==
                                                surveyEvent.TriggerEventToReview.PatientEncounterId
                                                && x.SourceDataId ==
                                                surveyEvent.TriggerEventToReview.TriggerSourceDataId).ToList();

            if (completedDateTime != null && completedDateTime.Count() > 0)
            {
                surveyEvent.CompletedDateTime = completedDateTime.Max(x => x.ChangeDts);
            }
        }

        return surveyEvents.Where(x => x.CompletedDateTime != null).ToList();
    }
}
