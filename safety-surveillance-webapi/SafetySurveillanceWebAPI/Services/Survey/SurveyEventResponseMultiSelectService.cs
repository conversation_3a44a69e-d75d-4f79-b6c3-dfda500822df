namespace SafetySurveillanceWebApi.Services.Survey;

using SafetySurveillanceWebApi.Models;
using SafetySurveillanceWebApi.Models.Constants;
using SafetySurveillanceWebApi.Models.Dtos.Survey;
using SafetySurveillanceWebApi.Services.Survey.MultiSelectTypes;

public class SurveyEventResponseMultiSelectService : ISurveyEventResponseMultiSelectService
{
    private readonly Dictionary<QuestionMultiSelectTypeOption, ISurveyEventTypeMultiSelectService>
        multiSelectTypeDict;

    public SurveyEventResponseMultiSelectService(ISurveyEventTypeMultiSelectFactory multiSelectFactory)
    {
        this.multiSelectTypeDict = multiSelectFactory.GetDictionary();
    }

    public void SetValues(Response response, ResponseDto responseData)
    {
        if (!this.multiSelectTypeDict.ContainsKey(responseData.ResponseMultiSelectTypeId))
        {
            return;
        }

        this.multiSelectTypeDict[responseData.ResponseMultiSelectTypeId].SetValues(response, responseData);
    }

    public bool IsDeleted(ResponseDto responseData)
    {
        if (!this.multiSelectTypeDict.ContainsKey(responseData.ResponseMultiSelectTypeId))
        {
            return true;
        }

        return this.multiSelectTypeDict[responseData.ResponseMultiSelectTypeId].GetCount(responseData) == 0;
    }
}