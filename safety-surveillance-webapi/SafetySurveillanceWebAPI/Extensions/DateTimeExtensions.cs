namespace SafetySurveillanceWebApi.Extensions;

public static class DateTimeExtensions
{
    /// <summary>
    /// Returns the difference in months between 2 dates.
    /// The first date must be the earlier date.
    /// </summary>
    /// <param name="startDate"></param>
    /// <param name="endDate"></param>
    /// <returns></returns>
    public static int DiffInMonths(this DateTime startDate, DateTime? endDate = null)
    {
        var endDateVal = DateTime.Now;
        if (endDate.HasValue)
        {
            endDateVal = endDate.Value;
        }

        if (startDate > endDateVal)
        {
            return 0;
        }

        var months = (endDateVal.Year - startDate.Year) * 12;
        months += endDateVal.Month - startDate.Month;
        while (startDate.AddMonths(months) > endDateVal)
        {
            months--;
        }

        return months;
    }

    /// <summary>
    /// Returns the difference in days between 2 dates.
    /// The first date must be the earlier date.
    /// </summary>
    /// <param name="startDate"></param>
    /// <param name="endDate"></param>
    /// <returns></returns>
    public static int DiffInDays(this DateTime startDate, DateTime? endDate = null)
    {
        var endDateVal = DateTime.Now;
        if (endDate.HasValue)
        {
            endDateVal = endDate.Value;
        }

        if (startDate > endDateVal)
        {
            return 0;
        }

        var dateDifference = endDateVal - startDate;
        var totalDays = dateDifference.TotalHours / 24;
        return (int)Math.Floor(totalDays);
    }

    public static DateTime StartOfTheDay(this DateTime d) => new DateTime(d.Year, d.Month, d.Day, 0, 0, 0);

    public static DateTime EndOfTheDay(this DateTime d) => new DateTime(d.Year, d.Month, d.Day, 23, 59, 59);

    public static DateTime CurrentMonthFirstDay(this DateTime currentDate)
    {
        return new DateTime(currentDate.Year, currentDate.Month, 1);
    }
    public static DateTime PreviousMonthFirstDay(this DateTime currentDate)
    {
        DateTime previous = currentDate.PreviousMonthLastDay();

        return new DateTime(previous.Year, previous.Month, 1);
    }

    public static DateTime PreviousMonthLastDay(this DateTime currentDate)
    {
        return new DateTime(currentDate.Year, currentDate.Month, 1).AddDays(-1);
    }
}
