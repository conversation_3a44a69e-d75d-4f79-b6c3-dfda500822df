namespace SafetySurveillanceWebApi.Repositories;

using SafetySurveillanceWebApi.Models;
using SafetySurveillanceWebApi.Models.Constants;

public interface ISavedFilterRepository
{
    Task<List<SavedFilter>> GetSavedFiltersAsync(string userId, string type);

    Task<bool> SaveFilterAsync(SavedFilter saveFilter);

    Task<bool> DeleteSavedFilterAsync(int savedFilterId);

    Task<bool> UpdateSavedFilterAsync(SavedFilter savedFilter);
}
