namespace SafetySurveillanceWebApi.Repositories;

using SafetySurveillanceWebApi.Data;
using SafetySurveillanceWebApi.Models;

public class HealthSystemRepository : IHealthSystemRepository
{
    private readonly ISafetySurveillanceApplicationContext context;

    public HealthSystemRepository(ISafetySurveillanceApplicationContext safetySurveillanceApplicationContext)
    {
        this.context = safetySurveillanceApplicationContext;
    }

    public HealthSystem GetHealthSystemForDomain(string domain)
    {
        Guard.Against.NullOrEmpty(domain, nameof(domain));

        return this.context.HealthSystems.FirstOrDefault(healthSystem => healthSystem.Domain.Equals(domain, StringComparison.OrdinalIgnoreCase));
    }
}