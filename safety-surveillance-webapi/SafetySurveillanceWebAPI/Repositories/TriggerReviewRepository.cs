using Microsoft.EntityFrameworkCore.Internal;
using SafetySurveillanceWebApi.Infrastructure;
using SafetySurveillanceWebApi.Models.Dtos;

namespace SafetySurveillanceWebApi.Repositories;

using System.Text.Json;
using Microsoft.EntityFrameworkCore;

using SafetySurveillanceWebApi.Data;
using SafetySurveillanceWebApi.Models;
using SafetySurveillanceWebApi.Models.Authorize;
using SafetySurveillanceWebApi.Models.Constants;
using SafetySurveillanceWebApi.Models.PublicHealth;
using SafetySurveillanceWebApi.Models.RootCauseAnalysis;

public class TriggerReviewRepository : ITriggerReviewRepository
{
    private readonly ISafetySurveillanceApplicationContext context;
    private readonly IApplicationConfigurationRepository applicationConfigurationRepository;

    public TriggerReviewRepository(ISafetySurveillanceApplicationContext safetySurveillanceApplicationContext, IApplicationConfigurationRepository applicationConfigurationRepository)
    {
        this.context = safetySurveillanceApplicationContext;
        this.applicationConfigurationRepository = applicationConfigurationRepository;
    }

    public IEnumerable<TriggerEventReview> GetTriggerEventReviews(DateRange dateRange, List<string> facilities, bool filterByAeDate = false)
    {
        Guard.Against.Null(facilities, nameof(facilities));

        if (filterByAeDate)
        {
            return this.context.TriggerEventReviews
                .Where(review =>
                    review.AdverseEventDts >= dateRange.StartDate
                    && review.AdverseEventDts <= dateRange.EndDate
                    && facilities.Contains(review.AdverseEventLocation)
                    && !review.IsDeleted)
                .Include(review => review.AdverseEventCategoryReference)
                .Include(review => review.AdverseEventSubCategoryReference);
        }

        return this.context.TriggerEventReviews
            .Where(review =>
                !review.IsDeleted &&
                review.TriggerDts >= dateRange.StartDate
                && review.TriggerDts <= dateRange.EndDate
                && facilities.Contains(review.AdverseEventLocation))
            .Include(review => review.AdverseEventCategoryReference)
            .Include(review => review.AdverseEventSubCategoryReference);
    }

    public IEnumerable<TriggerEventReview> GetPositiveTriggerEventReviews(DateRange dateRange, bool filterByAeDate = false)
    {
        if (dateRange.EndDate < dateRange.StartDate)
        {
            throw new ArgumentException("endDate can't be before startDate");
        }

        if (filterByAeDate)
        {
            return this.context.TriggerEventReviews
                .Where(review =>
                    !review.IsDeleted &&
                    review.AdverseEventDts >= dateRange.StartDate
                    && review.AdverseEventDts <= dateRange.EndDate
                    && review.IsAdverseEvent == AdverseEventType.EventDetected)
                .Include(review => review.AdverseEventCategoryReference)
                .Include(review => review.AdverseEventSubCategoryReference)
                .Include(review => review.AdverseEventSeverityReference);
        }

        return this.context.TriggerEventReviews
            .Where(review =>
                !review.IsDeleted &&
                review.TriggerDts >= dateRange.StartDate
                && review.TriggerDts <= dateRange.EndDate
                && review.IsAdverseEvent == AdverseEventType.EventDetected)
            .Include(review => review.AdverseEventCategoryReference)
            .Include(review => review.AdverseEventSubCategoryReference)
            .Include(review => review.AdverseEventSeverityReference);
    }

    public async Task<IEnumerable<PublicHealthToReview>> GetPublicHealthToReviewsForTriggerTimeFrameAsync(DateRange dateRange, string surveillanceType)
    {
        return await this.context.PublicHealthToReviews
            .Include(ph => ph.PublicHealthEventReview)
            .ThenInclude(ph => ph.AssociatedProviders
                .Where(ap => ap.EventType == surveillanceType))
            .ThenInclude(ap => ap.Provider)
            .Where(link => link.PublicHealthEventReview.TriggerDateTime >= dateRange.StartDate.AddDays(-1)
                           && link.PublicHealthEventReview.TriggerDateTime <= dateRange.EndDate.AddDays(1)).ToListAsync();
    }

    public async Task<IEnumerable<TriggerEventToReview>> GetTriggerEventToReviewsForTriggerTimeFrameAsync(DateRange dateRange, string surveillanceType)
    {
        return await this.context.TriggerEventToReviews
            .AsSplitQuery()
            .Include(link => link.TriggerEventComments)
            .Include(link => link.TriggerEventReview).ThenInclude(asr => asr.AdverseEventSeverityReference)
            .Include(link => link.TriggerEventReview).ThenInclude(asr => asr.ScaleItem).ThenInclude(si => si.ScaleType)
            .Include(link => link.TriggerEventReview).ThenInclude(asr => asr.ScaleItem).ThenInclude(si => si.SeverityScaleItemCategory)
            .Include(link => link.TriggerEventReview).ThenInclude(asr => asr.AdverseEventCategoryReference)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.TriggerReviewFormData).ThenInclude(trfd => trfd.FormData).ThenInclude(fd => fd.FormDefinition)
            .Include(link => link.TriggerEventReview).ThenInclude(asr => asr.AdverseEventSubCategoryReference)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedDevices).ThenInclude(devices => devices.Device)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedProviders
                .Where(ap => ap.EventType == surveillanceType))
            .ThenInclude(providers => providers.Provider)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedUnits)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedLocations).ThenInclude(al => al.CustomLocation)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedServiceLines)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.ReviewToRootCauseOptions).ThenInclude(devices => devices.Option).ThenInclude(o => o.Category)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.ReviewToRootCauseOptions).ThenInclude(devices => devices.Option).ThenInclude(o => o.ParentOption)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.ActionPlanning).ThenInclude(ap => ap.AssociatedInterventions).ThenInclude(ai => ai.Intervention)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.ActionPlanning).ThenInclude(ap => ap.AssociatedIncidentCategorizations).ThenInclude(aic => aic.IncidentCategorization)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.ActionPlanning).ThenInclude(ap => ap.ResponsibleDepartments)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.ActionPlanning).ThenInclude(ap => ap.ResponsibleIndividuals).ThenInclude(ri => ri.SafetyUser)
            .Where(link => link.TriggerEventReview.TriggerDts >= dateRange.StartDate.AddDays(-1)
                           && link.TriggerEventReview.TriggerDts <= dateRange.EndDate.AddDays(1)
                           && !link.TriggerEventReview.IsDeleted)
            .ToListAsync();
    }

    public async Task<IEnumerable<TriggerEventToReview>> GetTriggerEventToReviewsForAeTimeFrameAsync(DateRange dateRange)
    {
        return await this.context.TriggerEventToReviews
            .Include(link => link.TriggerEventComments)
            .Include(link => link.TriggerEventReview).ThenInclude(asr => asr.AdverseEventSeverityReference)
            .Include(link => link.TriggerEventReview).ThenInclude(asr => asr.ScaleItem).ThenInclude(si => si.ScaleType)
            .Include(link => link.TriggerEventReview).ThenInclude(asr => asr.ScaleItem).ThenInclude(si => si.SeverityScaleItemCategory)
            .Include(link => link.TriggerEventReview).ThenInclude(asr => asr.AdverseEventCategoryReference)
            .Include(link => link.TriggerEventReview).ThenInclude(asr => asr.AssociatedProviders
                .Where(p => p.EventType == SurveillanceTypeConstants.Harm))
            .ThenInclude(ap => ap.Provider)
            .Include(link => link.TriggerEventReview).ThenInclude(asr => asr.AdverseEventSubCategoryReference)
            .Include(link => link.TriggerEventReview).ThenInclude(asr => asr.AssociatedDevices).ThenInclude(d => d.Device)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedUnits)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedLocations).ThenInclude(al => al.CustomLocation)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedServiceLines)
            .Include(link => link.TriggerEventReview).ThenInclude(asr => asr.ReviewToRootCauseOptions).ThenInclude(d => d.Option)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.ActionPlanning).ThenInclude(ap => ap.AssociatedInterventions).ThenInclude(ai => ai.Intervention)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.ActionPlanning).ThenInclude(ap => ap.AssociatedIncidentCategorizations).ThenInclude(aic => aic.IncidentCategorization)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.ActionPlanning).ThenInclude(ap => ap.ResponsibleDepartments)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.ActionPlanning).ThenInclude(ap => ap.ResponsibleIndividuals).ThenInclude(ri => ri.SafetyUser)
            .Where(link => link.TriggerEventReview.AdverseEventDts >= dateRange.StartDate
                           && link.TriggerEventReview.AdverseEventDts <= dateRange.EndDate
                           && !link.TriggerEventReview.IsDeleted)
            .ToListAsync();
    }

    public async Task<TriggerEventToReview> GetTriggerEventToReviewForTriggerDetail(
        string triggerId,
        string patientEncounterId,
        string triggerSourceDataId)
    {
        var triggerEventToReview = await this.context.TriggerEventToReviews.Where(
                link => link.TriggerId == triggerId
                        && link.PatientEncounterId == patientEncounterId
                        && link.TriggerSourceDataId == triggerSourceDataId)
            .Include(t => t.TriggerEventReview)
            .Include(t => t.TriggerEventReview).ThenInclude(d => d.AssociatedDevices).ThenInclude(d => d.Device)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedUnits)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedLocations).ThenInclude(al => al.CustomLocation)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedServiceLines)
            .Include(t => t.TriggerEventReview).ThenInclude(d => d.ReviewToRootCauseOptions).ThenInclude(d => d.Option)
            .OrderBy(t => t.TriggerReviewId)
            .LastOrDefaultAsync();

        if (triggerEventToReview != null && triggerEventToReview.TriggerEventReview != null)
        {
            triggerEventToReview.TriggerEventReview.AssociatedProviders = await this.context.AssociatedProviders
                .Include(ap => ap.Provider).Where(ap =>
                    ap.TriggerReviewId == triggerEventToReview.TriggerReviewId && ap.EventType == SurveillanceTypeConstants.Harm)
                .ToListAsync();
        }

        return triggerEventToReview;
    }

    public async Task<TriggerEventToReview> GetTriggerEventToReviewForIncident(string eventSurveyId)
    {
        var triggerEventToReview = await this.context.TriggerEventToReviews
            .Where(tetr => tetr.TriggerSourceDataId == eventSurveyId)
            .Include(t => t.TriggerEventReview).Include(t => t.TriggerEventReview)
            .ThenInclude(d => d.AssociatedDevices).ThenInclude(d => d.Device)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedUnits)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedLocations).ThenInclude(al => al.CustomLocation)
            .Include(d => d.TriggerEventReview).ThenInclude(d => d.AssociatedServiceLines)
            .Include(t => t.TriggerEventReview)
            .ThenInclude(d => d.ReviewToRootCauseOptions).ThenInclude(d => d.Option).ThenInclude(o => o.ParentOption)
            .OrderBy(t => t.TriggerReviewId)
            .Where(review => !review.TriggerEventReview.IsDeleted)
            .LastOrDefaultAsync();

        if (triggerEventToReview != null && triggerEventToReview.TriggerEventReview != null)
        {
            triggerEventToReview.TriggerEventReview.AssociatedProviders = await this.context.AssociatedProviders
                .Include(ap => ap.Provider).Where(ap =>
                    ap.TriggerReviewId == triggerEventToReview.TriggerReviewId && ap.EventType == SurveillanceTypeConstants.Incident)
                .ToListAsync();
        }

        return triggerEventToReview;
    }

    public async Task<TriggerEventReview> GetTriggerEventReviewAsync(
        string triggerId,
        string patientEncounterId,
        string triggerSourceDataId)
    {
        TriggerEventToReview reviewLink =
            await GetTriggerEventToReviewAsync(triggerId, patientEncounterId, triggerSourceDataId);

        return reviewLink?.TriggerEventReview;
    }

    public async Task<TriggerEventToReview> GetTriggerEventToReviewAsync(
        string triggerId,
        string patientEncounterId,
        string triggerSourceDataId)
    {
        if (string.IsNullOrEmpty(triggerId))
        {
            throw new ArgumentException("value cannot be empty", nameof(triggerId));
        }

        if (string.IsNullOrEmpty(patientEncounterId))
        {
            throw new ArgumentException("value cannot be empty", nameof(patientEncounterId));
        }

        if (string.IsNullOrEmpty(triggerSourceDataId))
        {
            throw new ArgumentException("value cannot be empty", nameof(triggerSourceDataId));
        }

        TriggerEventToReview reviewLink = await this.context.TriggerEventToReviews.Where(
                link => link.TriggerId == triggerId
                        && link.PatientEncounterId == patientEncounterId
                        && link.TriggerSourceDataId == triggerSourceDataId)
            .Include(t => t.TriggerEventReview).ThenInclude(t => t.AssociatedProviders).ThenInclude(p => p.Provider)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedUnits)
            .Include(link => link.TriggerEventReview).ThenInclude(ter => ter.AssociatedLocations).ThenInclude(al => al.CustomLocation)
            .Include(d => d.TriggerEventReview).ThenInclude(d => d.AssociatedServiceLines)
            .Include(t => t.TriggerEventReview).ThenInclude(d => d.AssociatedDevices).ThenInclude(d => d.Device)
            .Include(t => t.TriggerEventReview).ThenInclude(d => d.ReviewToRootCauseOptions).ThenInclude(rrco => rrco.Option)
            .Include(tetr => tetr.SurveyEvent).ThenInclude(se => se.Incident)
            .OrderBy(t => t.TriggerReviewId)
            .Where(review => !review.TriggerEventReview.IsDeleted)
            .LastOrDefaultAsync();

        return reviewLink;
    }

    public async Task<TriggerEventReview> GetTriggerEventReviewAsync(int reviewId)
    {

        var triggerReview = await context.TriggerEventReviews.Where(r => r.ReviewId == reviewId)
            .Include(r => r.AdverseEventCategoryReference)
            .Include(r => r.AdverseEventSubCategoryReference)
            .Include(r => r.AssociatedProviders)
            .ThenInclude(p => p.Provider)
            .Include(r => r.AssociatedUnits)
            .Include(r => r.AssociatedLocations).ThenInclude(al => al.CustomLocation)
            .Include(r => r.AssociatedServiceLines)
            .Include(r => r.AssociatedDevices)
            .ThenInclude(d => d.Device)
            .Include(r => r.ReviewToRootCauseOptions)
            .ThenInclude(rrco => rrco.Option)
            .OrderByDescending(comment => comment.ReviewId).FirstOrDefaultAsync();

        if (triggerReview == null) return null;

        var reviewLink = await context.TriggerEventToReviews.FirstAsync(link => link.TriggerReviewId == reviewId);
        triggerReview.BelongsToIncident = reviewLink.TriggerId == "9999";
        return triggerReview;
    }

    public async Task<DateTime> UpdateTriggerReviewDts(int reviewId)
    {
        TriggerEventReview review = await GetTriggerEventReviewAsync(reviewId);

        if (review == null)
        {
            throw new Exception("Review cannot be found");
        }

        var date = DateTime.Now;

        review.ReviewDts = date;

        await context.SaveChangesAsync();

        return date;
    }

    public async Task<TriggerEventToReview> GetTriggerReviewLinkFromTriggerEventReviewIdAsync(int reviewId)
    {
        return await context.TriggerEventToReviews.FirstOrDefaultAsync(link => link.TriggerReviewId == reviewId);
    }

    public async Task<bool> HasAdverseEvent(string triggerId, string patientEncounterId, string triggerSourceDataId)
    {
        if (string.IsNullOrEmpty(triggerId))
        {
            throw new ArgumentException("value cannot be empty", nameof(triggerId));
        }

        if (string.IsNullOrEmpty(patientEncounterId))
        {
            throw new ArgumentException("value cannot be empty", nameof(patientEncounterId));
        }

        if (string.IsNullOrEmpty(triggerSourceDataId))
        {
            throw new ArgumentException("value cannot be empty", nameof(triggerSourceDataId));
        }

        var review = await this.GetTriggerEventReviewAsync(triggerId, patientEncounterId, triggerSourceDataId);

        return review != null && review.IsAdverseEvent == AdverseEventType.EventDetected;
    }

    public async Task<bool> SaveTriggerReview(List<TriggerEventToReview> triggerEventToReviewLinks, TriggerEventReview review)
    {
        bool isReviewAlreadySaved = await this.IsReviewSavedForAnyTriggerEvent(triggerEventToReviewLinks);

        if (isReviewAlreadySaved)
        {
            var error = new ArgumentException(nameof(review));
            throw error;
        }

        this.context.DetachAllEntities();
        review.ReviewDts = DateTime.Now;
        review.AdverseEventSubCategoryReference = null;
        review.AdverseEventCategoryReference = null;
        foreach (var rca in review.ReviewToRootCauseOptions)
        {
            rca.Option = null;
        }
        foreach (var reviewAssociatedDevice in review.AssociatedDevices)
        {
            reviewAssociatedDevice.Device = await this.GetDevice(reviewAssociatedDevice.Device.DeviceId);

        }

        foreach (var reviewAssociatedProvider in review.AssociatedProviders)
        {
            reviewAssociatedProvider.Provider = await this.GetProvider(
                reviewAssociatedProvider.ProviderId,
                reviewAssociatedProvider.RowSourceDsc);
        }

        this.context.TriggerEventReviews.Add(review);
        await this.context.SaveChangesAsync();

        var savedReviewId = review.ReviewId;

        foreach (var triggerEventToReviewLink in triggerEventToReviewLinks)
        {
            triggerEventToReviewLink.TriggerReviewId = savedReviewId;

            this.context.TriggerEventToReviews.Add(triggerEventToReviewLink);
        }

        await this.context.SaveChangesAsync();

        return true;
    }

    public async Task<Provider> GetProvider(string providerId, string rowSourceDsc)
    {
        return await this.context.Providers.FirstOrDefaultAsync(p => p.ProviderId == providerId && p.RowSourceDsc == rowSourceDsc);
    }

    public async Task<RootCauseAnalysisOption> GetRootCauseAnalysisOptionAsync(Guid rootCauseOptionId)
    {
        return await this.context.RootCauseAnalysisOptions.FirstOrDefaultAsync(rc => rc.Id == rootCauseOptionId);
    }

    public async Task<bool> UpdateTriggerEventReviewLinks(
        List<TriggerEventToReview> triggerEventToReviewsLinks,
        int reviewId)
    {
        List<TriggerEventToReview> oldTriggerEventToReviews =
            await this.context.TriggerEventToReviews.Where(p => p.TriggerReviewId == reviewId).ToListAsync();

        var hasSave = false;
        foreach (TriggerEventToReview newReview in triggerEventToReviewsLinks)
        {
            var newElement = oldTriggerEventToReviews.Find(
                e => e.TriggerId == newReview.TriggerId
                     && e.PatientEncounterId == newReview.PatientEncounterId
                     && e.PatientEncounterSourceDsc == newReview.PatientEncounterSourceDsc
                     && e.TriggerSourceDataId == newReview.TriggerSourceDataId);

            if (newElement == null)
            {
                hasSave = true;
                newReview.TriggerReviewId = reviewId;
                this.context.TriggerEventToReviews.Add(newReview);
            }
        }

        if (hasSave)
        {
            await this.context.SaveChangesAsync();
        }


        hasSave = false;

        if (oldTriggerEventToReviews.Count > 0)
        {
            foreach (TriggerEventToReview review in oldTriggerEventToReviews)
            {
                var element = triggerEventToReviewsLinks.Find(
                    e => e.TriggerId == review.TriggerId
                         && e.PatientEncounterId == review.PatientEncounterId
                         && e.PatientEncounterSourceDsc == review.PatientEncounterSourceDsc
                         && e.TriggerSourceDataId == review.TriggerSourceDataId);
                if (element == null)
                {
                    this.context.TriggerEventToReviews.Remove(review);
                    hasSave = true;
                }
            }

            if (hasSave)
            {
                await this.context.SaveChangesAsync();
            }
        }

        return true;
    }

    public async Task<bool> UpdateTriggerReview(List<TriggerEventToReview> triggerEventToReviewLinks, TriggerEventReview review)
    {
        Guard.Against.Null(review, nameof(review));

        this.context.DetachAllEntities();

        review.AdverseEventSubCategoryReference = null;
        review.AdverseEventCategoryReference = null;

        List<AssociatedDevices> newDevices = review.AssociatedDevices;
        List<AssociatedUnit> newUnits = review.AssociatedUnits;
        List<AssociatedServiceLine> newServiceLines = review.AssociatedServiceLines;
        List<AssociatedProvider> newProviders = review.AssociatedProviders;
        List<ReviewToRootCauseOption> newRootCauseOptions = review.ReviewToRootCauseOptions;

        await this.RemoveOldAssociated(review.ReviewId, review.AdverseEventLocation, newDevices, newProviders, newUnits, newServiceLines);

        await this.RemoveOldRootCause(review.ReviewId, newRootCauseOptions);

        foreach (var reviewAssociatedDevice in review.AssociatedDevices)
        {
            reviewAssociatedDevice.TriggerReviewId = review.ReviewId;
            reviewAssociatedDevice.Device = await this.GetDevice(reviewAssociatedDevice.Device.DeviceId);
        }

        var existingUnits = await this.context.AssociatedUnits
            .Where(u => u.TriggerReviewId == review.ReviewId)
            .Select(u => u.Location)
            .ToListAsync();

        review.AssociatedUnits = review.AssociatedUnits.Where(u => !existingUnits.Contains(u.Location)).ToList();
        foreach (var reviewAssociatedUnit in review.AssociatedUnits)
        {
            reviewAssociatedUnit.TriggerReviewId = review.ReviewId;
            var customLocation = await this.context.CustomLocations.FirstOrDefaultAsync(l =>
                l.Facility == review.AdverseEventLocation && l.Name == reviewAssociatedUnit.Location.Replace("(custom location)", string.Empty).TrimEnd());
            if (customLocation != null)
            {
                this.context.AssociatedLocations.Add(new AssociatedLocation()
                {
                    TriggerReviewId = review.ReviewId,
                    CustomLocationID = customLocation.Id,
                    Facility = customLocation.Facility
                });
                await this.context.SaveChangesAsync();
            }
        }

        foreach (var reviewAssociatedServiceLine in review.AssociatedServiceLines)
        {
            reviewAssociatedServiceLine.Id = 0;
            reviewAssociatedServiceLine.TriggerReviewId = review.ReviewId;
            reviewAssociatedServiceLine.ServiceLine = reviewAssociatedServiceLine.ServiceLine;
        }

        foreach (var reviewAssociatedProvider in review.AssociatedProviders)
        {
            reviewAssociatedProvider.Provider = await this.context.Providers.FirstOrDefaultAsync(p => p.ProviderId == reviewAssociatedProvider.ProviderId && p.RowSourceDsc == reviewAssociatedProvider.RowSourceDsc);
        }

#pragma warning disable CS0618
        if (review.AdverseEventSeverityReference == null || review.Severity != review.AdverseEventSeverityReference.Rating)
#pragma warning restore CS0618
        {
#pragma warning disable CS0618
            AdverseEventSeverityReference newSeverityReference = await this.context.SeverityReferences.FirstOrDefaultAsync(sr => sr.Rating == review.Severity);
#pragma warning restore CS0618

            review.AdverseEventSeverityReference = newSeverityReference;
        }

        if (review is { ScaleItem.ScaleType: { } })
        {
            review.ScaleItem.ScaleType.Items = null;
        }

        this.context.DetachAllEntities();

        this.context.Update(review);

        await this.context.SaveChangesAsync();

        return true;
    }

    public async Task<bool> DeleteTriggerEventReview(TriggerEventReview review)
    {
        Guard.Against.Null(review, nameof(review));

        this.context.DetachAllEntities();

        review.AdverseEventSubCategoryReference = null;
        review.AdverseEventCategoryReference = null;
        review.IsDeleted = true;

        this.context.Update(review);

        await this.context.SaveChangesAsync();

        return true;
    }

    public async Task<List<AdverseEventCategoryReference>> GetAdverseEventCategoriesAsync()
    {
        var categories = await this.context.AdverseCategories.Cacheable().Where(category => category.IsActive)
            .Include(cat => cat.AdverseEventSubCategories)
            .Include(cat => cat.ScaleType)
            .ThenInclude(st => st.Items)
            .ToListAsync();


        foreach (var adverseEventCategoryReference in categories)
        {
            adverseEventCategoryReference.AdverseEventSubCategories = adverseEventCategoryReference
                .AdverseEventSubCategories.Where(sub => sub.IsActive).ToList();
        }

        return categories;
    }

    public Task<List<AdverseEventSubCategoryReference>> GetAdverseEventSubCategoriesAsync()
    {
        return this.context.AdverseSubCategories
            .Where(sub => sub.IsActive)
            .ToListAsync();
    }

    public Task<List<TriggerStatusReference>> GetTriggerStatusReferencesAsync()
    {
        return this.context.StatusReferences
            .Cacheable()
            .Where(status => status.IsActive)
            .ToListAsync();
    }

    public Task<List<TriggerStatus>> GetTriggerStatusesAsync()
    {
        return this.context.TriggerStatus
            .Include(status => status.TriggerStatusReference)
            .ToListAsync();
    }

    public Task<List<TriggerStatus>> GetIncidentStatusesAsync(string triggerId = "9999")
    {
        return this.context.TriggerStatus
            .Where(t => t.TriggerId == triggerId)
            .Include(status => status.TriggerStatusReference)
            .ToListAsync();
    }

    public Task<TriggerStatus> GetTriggerStatus(int statusId)
    {
        return this.context.TriggerStatus
            .FirstOrDefaultAsync(status => status.Id == statusId);
    }

    public async Task<TriggerStatus> SaveTriggerStatusAsync(TriggerStatus status)
    {
        Guard.Against.Null(status, nameof(status));

        this.context.DetachAllEntities();

        this.context.TriggerStatus.Add(status);
        // If this is a required relationship, why check if null?
        // Say if status.TriggerStatusReferenceId is used and
        // null for the StatusReference, then that would
        // also be acceptable way to link the data.  If this goes to
        // save, the user will get a message about this.  This covers all bases.
        if (status.TriggerStatusReference != null)
        {
            this.context.StatusReferences.Attach(status.TriggerStatusReference);
        }
        await this.context.SaveChangesAsync();

        return status;
    }

    public async Task<List<SafetyUser>> GetSafetyUsers()
    {
        return await this.context.AuthorizeUsers
            .Where(safetyUser => !safetyUser.IsDeleted)
            .OrderBy(safetyUser => safetyUser.DisplayName)
            .ToListAsync();
    }

    public async Task<List<SafetyUser>> GetSafetyUserAssignees(GetTriggerReviewSafetyUserAssigneesInputDTO inputDto)
    {
        // Include the other data we need with groups: Users in the groups, the permissions associated with the groups, and the facility access for groups.
        List<Group> groups = await context.AuthorizeGroups
            .Include(x => x.GroupPermissions)
            .Include(x => x.GroupUsers)
            .ThenInclude(y => y.User)
            .Include(x => x.GroupFacilityAccesses)
            .Where(x => !x.IsDeleted).ToListAsync();

        // Obtain a list specifically of groups with facility access.
        var groupsWithFacilityAccess = groups.Where(x =>
            x.GroupFacilityAccesses.Select(y => y.Facility).Contains(inputDto.Facility)).ToList();

        var usersWithFacilityAccess = new List<SafetyUser>();
        var usersWithPermission = new List<SafetyUser>();

        foreach (var group in groupsWithFacilityAccess)
        {
            var usersInGroup = group.GroupUsers.Where(u => u.User != null).Select(x => x.User).ToList();

            usersWithFacilityAccess.AddRange(usersInGroup);
        }

        if (!inputDto.IsIncidentReview && !inputDto.IsAuditReview)
        {
            // Obtain a list of just groups with access to the canViewSurveil permission.
            var groupsWithCanViewSurveilPermission = groups.Where(x =>
                x.GroupPermissions.Select(y => y.PermissionId)
                    .Contains(Guid.Parse("b7f2c96e-1c28-403e-930a-76b2f8bb8def")));
            foreach (var group in groupsWithCanViewSurveilPermission)
            {
                var usersInGroup = group.GroupUsers.Where(u => u.User != null).Select(x => x.User).ToList();

                usersWithPermission.AddRange(usersInGroup);
            }
        }
        else if (!inputDto.IsIncidentReview && inputDto.IsAuditReview)
        {
            // Obtain a list of groups with access to the canReviewAudits permission
            var groupsWithCanViewAuditPermission = groups.Where(x =>
                x.GroupPermissions.Select(y => y.PermissionId)
                    .Contains(Guid.Parse("703DF1B9-80AD-41A5-AB4F-2FF16CAEA541")));
            foreach (var group in groupsWithCanViewAuditPermission)
            {
                var usersInGroup = group.GroupUsers.Where(u => u.User != null).Select(x => x.User).ToList();

                usersWithPermission.AddRange(usersInGroup);
            }
        }
        else
        {
            // Obtain a list of just groups with access to the canReviewIncidents permission.
            var groupsWithCanViewReviewIncidentsPermission = groups.Where(x =>
                x.GroupPermissions.Select(y => y.PermissionId)
                    .Contains(Guid.Parse("be93e485-fdcf-40ed-bba0-7765021569d8")));


            foreach (var group in groupsWithCanViewReviewIncidentsPermission)
            {
                var usersInGroup = group.GroupUsers.Where(u => u.User != null).Select(x => x.User).ToList();

                usersWithPermission.AddRange(usersInGroup);
            }
        }

        // We need to aggregate the permissions of the groups such that if a user belongs to one or more groups that fulfill the condition of facility access + permission, then those users are returned.


        var hasFacilityAccessEnabled = await this.applicationConfigurationRepository
            .GetFeatureFlagValueByNameAsync("facilityAccessControlFlg");

        List<SafetyUser> usersWithAccess;

        if (!hasFacilityAccessEnabled)
        {
            usersWithAccess = (await this.GetSafetyUsers())
                .IntersectBy(usersWithPermission.DistinctBy(y => y.UserName), z => z).Where(u => !u.IsDeleted)
                .OrderBy(u => u.DisplayName).ToList();
        }
        else
        {
            usersWithAccess = usersWithFacilityAccess.DistinctBy(x => x.UserName)
                .IntersectBy(usersWithPermission.DistinctBy(y => y.UserName), z => z).Where(u => !u.IsDeleted).OrderBy(u => u.DisplayName).ToList();
        }

        return usersWithAccess;
    }

    public void UpdateTriggerStatus(TriggerStatus status)
    {
        Guard.Against.Null(status, nameof(status));

        TriggerStatus existingStatus = this.context.TriggerStatus
            .FirstOrDefault(oldStatus => oldStatus.Id == status.Id);

        Guard.Against.Null(existingStatus, nameof(existingStatus));

        existingStatus.StatusId = status.StatusId;
        this.context.Update(existingStatus);
        // If this is a required relationship, why check if null?
        // Say if status.TriggerStatusReferenceId is used and
        // null for the StatusReference, then that would
        // also be acceptable way to link the data.  If this goes to
        // save, the user will get a message about this.  This covers all bases.
        if (status.TriggerStatusReference != null)
        {
            this.context.StatusReferences.Attach(status.TriggerStatusReference);
        }
        this.context.SaveChanges();
    }

    public async Task<List<TriggerEventAssignment>> GetTriggerEventAssignmentsAsync()
    {
        return await this.context.TriggerEventAssignments
            .OrderByDescending(assignment => assignment.AssignmentDts)
            .ToListAsync();
    }

    public async Task<List<TriggerEventAssignment>> GetSurveyEventAssignmentsAsync(string triggerId = "9999")
    {
        return await this.context.TriggerEventAssignments
            .Where(tea => tea.TriggerId == triggerId)
            .OrderByDescending(assignment => assignment.AssignmentDts)
            .ToListAsync();
    }

    public async Task<List<TriggerEventAssignment>> GetTriggerEventAssignmentsByIdAsync(string userId)
    {
        var result = new List<TriggerEventAssignment>();

        var eventAssignments = await this.context.TriggerEventAssignments.ToListAsync();

        foreach (var assignment in eventAssignments)
        {
            // avoid semi-expensive deserialize operation if not needed
            if (assignment.AssignedTo.StartsWith("[") || assignment.AssignedTo.StartsWith("{"))
            {
                string[] assignedToResult = JsonSerializer.Deserialize<string[]>(assignment.AssignedTo.ToLower());

                if (assignedToResult.Contains(userId.ToLower()))
                {
                    result.Add(assignment);
                }
            }
            else
            {
                var assignedToString = assignment.AssignedTo.ToLower();

                if (assignedToString == userId.ToLower())
                {
                    result.Add(assignment);
                }
            }
        }

        return result;
    }

    public async Task<TriggerEventAssignment> GetTriggerEventAssignmentAsync(string triggerId, string patientEncounterId, string triggerSourceDataId)
    {
        return await this.context.TriggerEventAssignments.FirstOrDefaultAsync(
            assignment => assignment.TriggerId == triggerId
                          && assignment.PatientEncounterId == patientEncounterId
                          && assignment.TriggerSourceDataId == triggerSourceDataId);
    }

    public async Task<TriggerEventAssignment> SaveTriggerEventAssignment(TriggerEventAssignment assignment)
    {
        Guard.Against.Null(assignment, nameof(assignment));

        this.context.DetachAllEntities();

        var deleteAssignment = await this.context.TriggerEventAssignments
            .FirstOrDefaultAsync(oldAssignment => oldAssignment.TriggerId == assignment.TriggerId
                                             && oldAssignment.PatientEncounterId == assignment.PatientEncounterId
                                             && oldAssignment.PatientEncounterSourceDSC == assignment.PatientEncounterSourceDSC
                                             && oldAssignment.TriggerSourceDataId == assignment.TriggerSourceDataId);

        if (deleteAssignment != null)
        {
            this.context.TriggerEventAssignments.Remove(deleteAssignment);
        }

        if (assignment.AssignedTo != "Unassigned")
        {
            this.context.TriggerEventAssignments.Add(assignment);
        }

        await this.context.SaveChangesAsync();

        return assignment;
    }

    public async Task<TriggerStatus> GetTriggerStatusAsync(string triggerId, string patientEncounterId, string triggerSourceDataId)
    {
        return await this.context.TriggerStatus
            .Include(s => s.TriggerStatusReference)
            .Where(status => status.TriggerId == triggerId
                             && status.PatientEncounterId == patientEncounterId
                             && status.TriggerSourceDataId == triggerSourceDataId)
            .OrderByDescending(t => t.Id)
            .FirstOrDefaultAsync();
    }

    public async Task<List<SeverityScaleType>> GetAdverseEventSeverityReferencesAsync()
    {
        return await this.context.SeverityScaleType
            .Include(type => type.Items)
            .ThenInclude(item => item.SeverityScaleItemCategory)
            .ToListAsync();
    }

    public async Task<List<TriggerEventToReview>> GetRelatedEventAssignmentsAsync(int triggerReviewId)
    {
        return await this.context.TriggerEventToReviews.Where(
                triggerEvent => triggerEvent.TriggerReviewId == triggerReviewId)
            .Include(t => t.SurveyEvent)
            .ThenInclude(s => s.Incident)
            .ToListAsync();
    }

    public async Task<SafetyUser> GetCreatedByUser(string safetyUserId)
    {
        return await this.context.AuthorizeUsers.Where(p => p.UserName == safetyUserId).FirstOrDefaultAsync();
    }



    private async Task<Device> GetDevice(int deviceId)
    {
        return await this.context.Devices.FirstOrDefaultAsync(device => device.DeviceId == deviceId);
    }

    private async Task<UnitReference> GetUnit(string unitId)
    {
        return await this.context.UnitReferences.FirstOrDefaultAsync(unit => unit.UnitId == unitId);
    }

    private async Task<bool> RemoveOldAssociated(int reviewId, string facility, List<AssociatedDevices> newDevices, List<AssociatedProvider> newProviders, List<AssociatedUnit> newUnits, List<AssociatedServiceLine> newServiceLines)
    {
        this.context.DetachAllEntities();

        List<int> newProviderList = newProviders.Select(i => i.RelatedProviderId).ToList();

        List<int> newDeviceList = newDevices.Select(i => i.RelatedDeviceId).ToList();

        List<string> newUnitList = newUnits.Select(i => i.Location).ToList();

        List<string> newServiceLineList = newServiceLines.Select(i => i.ServiceLine).ToList();

        List<AssociatedProvider> associatedProvidersToDelete = this.context.AssociatedProviders
            .Where(ap => ap.TriggerReviewId == reviewId && !newProviderList.Contains(ap.RelatedProviderId)).ToList();

        List<AssociatedDevices> associatedDevicesToDelete = this.context.AssociatedDevices
            .Where(device => device.TriggerReviewId == reviewId && !newDeviceList.Contains(device.RelatedDeviceId)).ToList();

        List<AssociatedUnit> associatedUnitsToDelete = this.context.AssociatedUnits
            .Where(unit => unit.TriggerReviewId == reviewId && !newUnitList.Contains(unit.Location)).ToList();

        List<AssociatedServiceLine> associatedServiceLinesToDelete = this.context.AssociatedServiceLines
            .Where(sl => sl.TriggerReviewId == reviewId).ToList();

        foreach (var associatedProvider in associatedProvidersToDelete)
        {
            this.context.AssociatedProviders.Remove(associatedProvider);
        }

        await this.context.SaveChangesAsync();

        foreach (AssociatedDevices associatedDevice in associatedDevicesToDelete)
        {
            this.context.AssociatedDevices.Remove(associatedDevice);
        }

        await this.context.SaveChangesAsync();

        var associatedLocations = await this.context.AssociatedLocations
            .Where(l => l.TriggerReviewId == reviewId)
            .Include(al => al.CustomLocation)
            .ToListAsync();

        foreach (AssociatedUnit associatedUnit in associatedUnitsToDelete)
        {
            this.context.AssociatedUnits.Remove(associatedUnit);
            var associatedLocation = associatedLocations.FirstOrDefault(al =>
                al.CustomLocation.Name == associatedUnit.Location.Replace("(custom location)", string.Empty).TrimEnd());
            if (associatedLocation != null)
            {
                this.context.AssociatedLocations.Remove(associatedLocation);
            }
        }

        await this.context.SaveChangesAsync();

        foreach (AssociatedServiceLine associatedServiceLine in associatedServiceLinesToDelete)
        {
            this.context.AssociatedServiceLines.Remove(associatedServiceLine);
        }

        await this.context.SaveChangesAsync();

        return true;
    }

    private async Task<bool> RemoveOldRootCause(int reviewId, List<ReviewToRootCauseOption> newRootCauseOptions)
    {
        this.context.DetachAllEntities();

        //List<Guid> newRootCauseList = newRootCauseOptions.Select(i => i.RootCauseOptionId).ToList();

        List<ReviewToRootCauseOption> rootCauseOptionsToDelete = this.context.ReviewToRootCauseOptions.Where(
                rootcause => rootcause.TriggerReviewId == reviewId)
            .ToList();

        foreach (var rootCauseOption in rootCauseOptionsToDelete)
        {
            this.context.ReviewToRootCauseOptions.Remove(rootCauseOption);
        }

        await this.context.SaveChangesAsync();

        return true;
    }

    private async Task<bool> IsReviewSavedForAnyTriggerEvent(IEnumerable<TriggerEventToReview> triggerToReviewLinks)
    {
        bool isReviewAlreadySaved = false;

        foreach (var triggerEventToReviewLink in triggerToReviewLinks)
        {
            var isSaved = await this.IsReviewAlreadySaved(
                triggerEventToReviewLink.TriggerId,
                triggerEventToReviewLink.PatientEncounterId,
                triggerEventToReviewLink.TriggerSourceDataId,
                triggerEventToReviewLink.TriggerReviewId);

            if (isSaved)
            {
                isReviewAlreadySaved = true;
            }
        }

        return isReviewAlreadySaved;
    }

    private async Task<bool> IsReviewAlreadySaved(string triggerId, string patientEncounterId, string triggerSourceDateId, int triggerReviewId)
    {
        var checkAlreadySaved = await this.context.TriggerEventToReviews.Where(
                link => link.TriggerId == triggerId
                        && link.PatientEncounterId == patientEncounterId
                        && link.TriggerSourceDataId == triggerSourceDateId
                        && link.TriggerReviewId == triggerReviewId)
            .FirstOrDefaultAsync();

        if (checkAlreadySaved == null)
        {
            return false;
        }

        bool isDeleted = await this.IsReviewDeleted(checkAlreadySaved.TriggerReviewId);

        if (isDeleted)
        {
            return false;
        }

        return true;
    }

    private async Task<bool> IsReviewDeleted(int reviewId)
    {
        var savedReview = await this.context.TriggerEventReviews.SingleOrDefaultAsync(review => review.ReviewId == reviewId);

        return savedReview.IsDeleted;
    }

    public async Task<int> GetEventAssignmentReviewIdAsync(TriggerEventAssignment triggerEventAssignment)
    {
        var triggerEventAssignmentReturn = await this.context.TriggerEventToReviews
            .Where(triggerEvent => triggerEvent.TriggerId == triggerEventAssignment.TriggerId
                                   && triggerEvent.PatientEncounterId == triggerEventAssignment.PatientEncounterId
                                   && triggerEvent.TriggerSourceDataId ==
                                   triggerEventAssignment.TriggerSourceDataId)
            .OrderBy(t => t.TriggerReviewId)
            .LastOrDefaultAsync();

        if (triggerEventAssignmentReturn != null)
        {
            return triggerEventAssignmentReturn.TriggerReviewId;
        }
        return 0;
    }

    public async Task<List<TriggerEventReview>> GetAllPositiveTriggerEventReviewsWithSeverities(string facility)
    {
        return await this.context.TriggerEventReviews
            .Where(review =>
                !review.IsDeleted &&
                review.IsAdverseEvent == AdverseEventType.EventDetected &&
                review.AdverseEventLocation == facility &&
                review.ScaleItem != null)
            .Include(review => review.AdverseEventCategoryReference)
            .Include(review => review.AdverseEventSubCategoryReference)
            .Include(review => review.AdverseEventSeverityReference)
            .AsNoTracking()
            .ToListAsync();
    }
}
