namespace SafetySurveillanceWebApi.Repositories.Audit.Restraints;

using Microsoft.EntityFrameworkCore;

using SafetySurveillanceWebApi.Data;
using SafetySurveillanceWebApi.Models;
using SafetySurveillanceWebApi.Models.Audit.HeparinAudit;
using SafetySurveillanceWebApi.Models.Authorize;
using SafetySurveillanceWebApi.Models.Constants;
using SafetySurveillanceWebApi.Models.Dtos.Round;
using SafetySurveillanceWebApi.Models.EntitiesExtended;

public class HeparinAuditRepository : IAuditRepository
{
    public ISafetySurveillanceApplicationContext context { get; }

    public HeparinAuditRepository(ISafetySurveillanceApplicationContext context)
    {
        this.context = context;
    }

    public async Task<List<CommonEncounterWithSurvey>> GetActiveEncounters(RoundPatientListFilterBody filters, DateRange dateRange, bool assignedToMeOnly)
    {
        List<HeparinAudit> heparinAudits;
        if (assignedToMeOnly)
        {
            heparinAudits = await this.context.HeparinAudits
                .Where(heparinAudit => heparinAudit.DataDts >= dateRange.StartDate && heparinAudit.DataDts <= dateRange.EndDate)
                .Include(audits => audits.Encounter)
                .ThenInclude(encounter => encounter.CommonAdts)
                .ToListAsync();
        }
        else
        {
           heparinAudits = await this.context.HeparinAudits
                .Where(heparinAudit => heparinAudit.DataDts >= dateRange.StartDate && heparinAudit.DataDts <= dateRange.EndDate)
                .Include(audits => audits.Encounter)
                .ThenInclude(encounter => encounter.CommonAdts)
                .Where(ha => filters.Facilities.Contains(ha.Encounter.Location))
                .ToListAsync();
        }

        // GroupBy() is performed client side, and the resulting object when grouping by MedicationOrderId is a HeparinAudit
        return heparinAudits
            .GroupBy(ha => ha.MedicationOrderId) // should we be sorting on a specific column: DispensedDts, BagStartDts, etc?
            .Select(g => g.First()) // take the first HeparinAudit returned to pull the values below
            .Select(ha => new CommonEncounterWithSurvey
            {
                Encounter = ha.Encounter,
                LatestAdts = ha.Encounter.CommonAdts,
                HeparinAudit = ha
            })
            .ToList();
    }

    public async Task<DateTime?> GetActiveEncountersLastLoadDate(RoundPatientListFilterBody filters, bool assignedToMeOnly)
    {
        return await this.GetActiveFilteredEncounters(filters, assignedToMeOnly)
            .MaxAsync(e => (DateTime?)e.Encounter.LastLoadDate);
    }

    public Task<CommonEncounterWithLatestAdts> GetEncounterById(string encounterId, string encounterSource)
    {
        throw new NotImplementedException();
    }

    public Task<List<Form>> GetAvailableAuditFormsAsync()
    {
        throw new NotImplementedException();
    }

    public async Task<List<Form>> GetAvailableAuditFormsWithChildrenAsync(int version = 1)
    {
        List<Form> availableForms = await this.context.Forms
            .Include(c => c.Sections)
            .ThenInclude(c => c.Questions)
            .Where(form => form.Type == FormType.RoundingTool && form.IsActive)
            .ToListAsync();

        return availableForms;
    }

    public Task<Form> GetFormAsync(Guid formId)
    {
        throw new NotImplementedException();
    }

    public Task<List<SafetyUser>> GetAuditAssignees()
    {
        throw new NotImplementedException();
    }

    public async Task<CommonEncounterWithSurvey> GetPatientEncounterById(string encounterId, 
        string encounterSource, 
        RoundPatientListFilterBody filters, 
        bool assignToMeOnly = false)
    {
        var encounterQuery = await this.context.CommonEncounters
            .Where(e => filters.Facilities.Contains(e.Location) || assignToMeOnly)
            .Where(e => e.EncounterId == encounterId)
            .Where(e => (string.IsNullOrEmpty(encounterSource) || e.EncounterSource == encounterSource))
            .FirstOrDefaultAsync();

        if (encounterQuery == null)
        {
            return null;
        }

        var adtsQuery = await this.context.CommonAdts
            .Where(adts => adts.LatestDepartmentFlg == 1)
            .Where(adts => adts.EncounterId == encounterQuery.EncounterId)
            .Where(adts => (string.IsNullOrEmpty(encounterSource) || adts.EncounterSource == encounterSource))
            .FirstOrDefaultAsync();

        var heparinAuditQuery = await this.context.HeparinAudits.Where(
                heparinAudit =>
                    heparinAudit.EncounterId == encounterQuery.EncounterId
                    && heparinAudit.EncounterSource == encounterQuery.EncounterSource)
            .FirstOrDefaultAsync();

        return new CommonEncounterWithSurvey()
        {
            Encounter = encounterQuery,
            LatestAdts = adtsQuery,
            HeparinAudit = heparinAuditQuery
        };
    }

    private IQueryable<HeparinAudit> GetActiveFilteredEncounters(RoundPatientListFilterBody filters, bool assignedToMeOnly)
    {
        if (assignedToMeOnly)
        {
            return this.context.HeparinAudits
                .Include(ma => ma.Encounter);
        }
        return this.context.HeparinAudits
            .Include(ma => ma.Encounter)
            .Where(e => filters.Facilities.Contains(e.Encounter.Location));
    }

    public IQueryable<CommonEncounterWithSurvey> GetActiveEncountersForAudit(RoundPatientListFilterBody filters, DateRange dateRange)
    {
        throw new NotImplementedException();
    }
}
