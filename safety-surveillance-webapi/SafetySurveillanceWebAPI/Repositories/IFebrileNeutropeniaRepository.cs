using SafetySurveillanceWebApi.Models.Dtos.Round;

namespace SafetySurveillanceWebApi.Repositories;

using System.Data;
using SafetySurveillanceWebApi.Models;
using SafetySurveillanceWebApi.Models.FebrileNeutropenia;

public interface IFebrileNeutropeniaRepository: IGetAllBundleElements
{
    Task<List<string>> GetBundleTypes();

    Task<List<FebrileNeutropeniaBundleElement>> GetBundleElementsById(DateRange dateRange, List<string> facilities, string elementId);

    Task<List<FebrileNeutropeniaBundle>> GetMeasures();

    Task<List<FebrileNeutropeniaBundle>> GetActiveBundles();

    Task<List<FebrileNeutropeniaBundleCompliance>> GetBundleCompliancesByType(DateRange dateRange, List<string> bodyFacilities, string bundleBundleType);

    Task<int> GetConfirmedHacCountsPerMonthAsync(int currentMonth, int currentMonthYear, List<string> facilities);

    Task<List<FebrileNeutropeniaBundle>> GetBundlesById(string bundleType, string elementId);

    Task<List<FebrileNeutropeniaBundleElement>> GetBundleElements(
        string bundleType,
        string elementId,
        List<string> facilities, DateRange dateRange);

    Task<List<FebrileNeutropeniaBundle>> GetBundles(string bundleType);

    Task<List<FebrileNeutropeniaBundleCompliance>> GetBundleCompliance(DateRange dateRange, string bundleId, List<string> facilities);

    Task<List<FebrileNeutropeniaAdmissions>> GetFebrileNeutropeniaAdmissions(DateRange dateRange, List<string> facilities);

    Task<List<FebrileNeutropeniaPatientList>> GetHacPatientListWithDateRangeAsync(DateRange dateRange, List<string> bodyFacilities);

    Task<List<FebrileNeutropeniaBundleCompliance>> GetBundleComplianceByEncounterIdAsync(List<string> ids);

    Task<DateTime> GetLastLoadDate();
    Task<List<FebrileNeutropeniaCensusDays>> GetReportingViewCensusDays(DateRange dateRange, List<string> facilities);

    Task<List<string>> GetInitialOrderingProviders();

    Task<List<string>> GetAdmittingProviders();

    Task<List<string>> GetClinics();

    Task<object> GetBundleComplianceReferences();

    Task<List<FebrileNeutropeniaPopulationAudit>> GetPopulationAudits(RoundPatientListFilterBody filters, bool assignedToMeOnly);

    Task<Dictionary<(string, string), FebrileNeutropeniaPopulationAudit>> GetPopulationAuditsOld(List<(string, string)> encounterIds);

    DataTable GetAllDetail(DateRange dateRange);
}
