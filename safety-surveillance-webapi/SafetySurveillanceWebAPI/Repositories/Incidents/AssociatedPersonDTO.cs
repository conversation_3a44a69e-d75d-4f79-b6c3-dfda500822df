namespace SafetySurveillanceWebApi.Repositories.Incident;

public class AssociatedPersonDTO
{
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Mrn { get; set; }
    public string VisitNumber { get; set; }
    public DateTimeOffset? AdmitDateTime { get; set; }
    public string EncounterId { get; set; }
    public string EncounterSource { get; set; }
    public int PersonTypeId { get; set; }
    public bool IsManual { get; set; }
    public string EntityName { get; set; }
}