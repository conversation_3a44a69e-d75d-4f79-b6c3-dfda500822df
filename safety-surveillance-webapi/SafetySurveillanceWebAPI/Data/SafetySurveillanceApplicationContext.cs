using Microsoft.Extensions.Logging;
using SafetySurveillance.Application.Features.ConfigurableAudits.Queries.GetAuditPatientDetail;
using SafetySurveillanceWebApi.Features.ConfigurableAudit;
using SafetySurveillanceWebApi.Features.Formly;
using SafetySurveillanceWebApi.Features.IncidentLinking;
using SafetySurveillanceWebApi.FileAttachments;
using SafetySurveillanceWebApi.Models.ActionPlanning;
using SafetySurveillanceWebApi.Models.Analytics;
using SafetySurveillanceWebApi.Models.Database.PSMSurvey;
using SafetySurveillanceWebApi.Models.Database.Incidents;
using SafetySurveillanceWebApi.Models.Database.Survey;

namespace SafetySurveillanceWebApi.Data;

using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

using PSM.Common.Service;

using SafetySurveillanceWebApi.Configuration;
using SafetySurveillanceWebApi.Models;
using SafetySurveillanceWebApi.Models.Audit.Example;
using SafetySurveillanceWebApi.Models.Audit.HeparinAudit;
using SafetySurveillanceWebApi.Models.Audit.Mortality;
using SafetySurveillanceWebApi.Models.Audit.Restraints;
using SafetySurveillanceWebApi.Models.Authorize;
using SafetySurveillanceWebApi.Models.ClientAdmin.CustomLocations;
using SafetySurveillanceWebApi.Models.ClientAdmin.SentinelEventEmailAddresses;
using SafetySurveillanceWebApi.Models.Common;
using SafetySurveillanceWebApi.Models.Constants;
using SafetySurveillanceWebApi.Models.FebrileNeutropenia;
using SafetySurveillanceWebApi.Models.Improvement;
using SafetySurveillanceWebApi.Models.Incidents;
using SafetySurveillanceWebApi.Models.Notification;
using SafetySurveillanceWebApi.Models.PressureInjury;
using SafetySurveillanceWebApi.Models.PublicHealth;
using SafetySurveillanceWebApi.Models.RootCauseAnalysis;
using SafetySurveillanceWebApi.Models.Survey;

public class SafetySurveillanceApplicationContext : DbContext, ISafetySurveillanceApplicationContext
{
    private readonly ILogger logger;
    private readonly IAppConfiguration appConfiguration;
    private readonly IEncryptionService encryptionService;

    public SafetySurveillanceApplicationContext(DbContextOptions<SafetySurveillanceApplicationContext> options,
        ILogger<SafetySurveillanceApplicationContext> logger,
        IAppConfiguration appConfiguration,
        IEncryptionService encryptionService)
        : base(options)
    {
        this.logger = logger;
        this.appConfiguration = appConfiguration;
        this.encryptionService = encryptionService;
    }

    public DbSet<TriggerEventToReview> TriggerEventToReviews { get; set; }

    public DbSet<TriggerEventReview> TriggerEventReviews { get; set; }

    public DbSet<PublicHealthToReview> PublicHealthToReviews { get; set; }

    public DbSet<PublicHealthEventReview> PublicHealthEventReviews { get; set; }

    public DbSet<HealthSystem> HealthSystems { get; set; }

    public DbSet<ExportedFileInfo> ExportedFileInfos { get; set; }

    public DbSet<ReferenceGender> ReferenceGenders { get; set; }

    public DbSet<ReferenceLocationUnit> ReferenceLocationUnits { get; set; }

    public DbSet<ReferenceLocationServiceLine> ReferenceLocationServiceLines { get; set; }

    public DbSet<CommonReferencePatientClass> ReferenceLocationPatientClasses { get; set; }

    public DbSet<CommonEncounter> CommonEncounters { get; set; }

    public DbSet<CommonBradenScore> CommonBradenScores { get; set; }

    public DbSet<AuditPressureInjury> AuditPressureInjuryEncounters { get; set; }

    public DbSet<AuditClabsi> AuditClabsiEncounters { get; set; }

    public DbSet<AuditCauti> AuditCautiEncounters { get; set; }

    public DbSet<CommonAdt> CommonAdts { get; set; }

    public DbSet<CommonCensusDays> CommonCensusDays { get; set; }

    public DbSet<AdverseEventCategoryReference> AdverseCategories { get; set; }

    public DbSet<AdverseEventSubCategoryReference> AdverseSubCategories { get; set; }

    public DbSet<TriggerStatusReference> StatusReferences { get; set; }

    public DbSet<PublicHealthTriggerStatusReference> PublicHealthStatusReferences { get; set; }

    public DbSet<TriggerStatus> TriggerStatus { get; set; }

    public DbSet<HistoricalCommonEncounter> HistoricalPatientEncounters { get; set; }

    public DbSet<HistoricalTriggerDetail> HistoricalTriggerDetails { get; set; }

    public DbSet<PatientWatch> PatientWatches { get; set; }

    public DbSet<TriggerEventAssignment> TriggerEventAssignments { get; set; }

    public DbSet<TriggerEventComment> TriggerEventComments { get; set; }
    public DbSet<TriggerEventInvestigationDetail> TriggerEventInvestigationDetails { get; set; }

    public DbSet<AuditLog> AuditLogs { get; set; }

    public DbSet<AdverseEventSeverityReference> SeverityReferences { get; set; }

    public DbSet<AdverseEventDeepDiveReference> DeepDiveReferences { get; set; }

    public DbSet<AuditToolReference> AuditToolReferences { get; set; }

    public DbSet<FebrileNeutropeniaBundleElementComplianceReference> FebrileNeutropeniaBundleElementComplianceReferences
    {
        get;
        set;
    }

    public DbSet<ClabsiBundleElementComplianceReference> ClabsiBundleElementComplianceReferences { get; set; }

    public DbSet<FeatureFlag> FeatureFlags { get; set; }

    public DbSet<ImprovementModule> ImprovementModules { get; set; }

    public DbSet<SavedFilter> SavedFilters { get; set; }

    public DbSet<DbAppConfig> AppConfigs { get; set; }

    public DbSet<TriggerDetail> TriggerEvents { get; set; }

    public DbSet<TriggerReference> TriggerReferences { get; set; }

    public DbSet<UnitReference> UnitReferences { get; set; }

    public DbSet<BathingMethodReference> BathingMethodReferences { get; set; }

    public DbSet<PatientAllergy> PatientAllergies { get; set; }

    public DbSet<PatientActiveMedicationOrder> PatientMedicationOrders { get; set; }

    public DbSet<PatientSurgeryProcedures> PatientSurgeryProcedures { get; set; }

    public DbSet<PatientLab> PatientLabs { get; set; }

    public DbSet<PatientRadiologyOrder> PatientRadiologyOrders { get; set; }

    public DbSet<Provider> Providers { get; set; }

    public DbSet<AssociatedProvider> AssociatedProviders { get; set; }

    public DbSet<ReviewToRootCauseOption> ReviewToRootCauseOptions { get; set; }

    public DbSet<Device> Devices { get; set; }

    public DbSet<AssociatedDevices> AssociatedDevices { get; set; }

    public DbSet<AssociatedUnit> AssociatedUnits { get; set; }

    public DbSet<AssociatedLocation> AssociatedLocations { get; set; }

    public DbSet<AssociatedServiceLine> AssociatedServiceLines { get; set; }

    public DbSet<TriggerFlag> TriggerFlags { get; set; }

    public DbSet<SummaryDefinition> SummaryDefinitions { get; set; }

    public DbSet<ClabsiSummaryEncounters> ClabsiSummaryEncounters { get; set; }

    public DbSet<PressureInjurySummaryEncounters> PressureInjurySummaryEncounters { get; set; }
    public DbSet<FNSummaryEncounters> FNSummaryEncounters { get; set; }

    public DbSet<ClabsiOrganismReference> ClabsiOrganismReferences { get; set; }

    public DbSet<CautiOrganismReference> CautiOrganismReferences { get; set; }

    public DbSet<CautiBundle> CautiBundles { get; set; }

    public DbSet<ClabsiBundle> ClabsiBundles { get; set; }

    public DbSet<InsertionBundle> InsertionBundles { get; set; }

    public DbSet<MaintenanceBundle> MaintenanceBundles { get; set; }

    public DbSet<ClabsiBundleElement> ClabsiBundleElements { get; set; }

    public DbSet<PreInfectionBundleElementCompliance> PreInfectionBundleElementCompliances { get; set; }

    public DbSet<SkinIntegrityEncounter> SkinIntegrityList { get; set; }

    public DbSet<PreInjuryBundleElementCompliance> PreInjuryBundleElementCompliances { get; set; }

    public DbSet<CautiPredictionOutput> CautiPredictionOutputs { get; set; }

    public DbSet<CautiEventSummary> CautiEventSummaries { get; set; }

    public DbSet<CautiReferenceRiskCategory> CautiReferenceRiskCategories { get; set; }

    public DbSet<CautiUtilizationMeasure> CautiUtilizationMeasures { get; set; }

    public DbSet<ReferenceImprovementMeasure> ImprovementMeasures { get; set; }

    public DbSet<ImprovementMeasureData> ImprovementMeasureData { get; set; }

    public DbSet<ImprovementIntervention> ImprovementInterventions { get; set; }

    public DbSet<ClabsiUtilizationMeasure> ClabsiUtilizationMeasures { get; set; }

    public DbSet<PressureInjuryUtilizationMeasure> PressureInjuryUtilizationMeasures { get; set; }

    public DbSet<FebrileNeutropeniaUtilizationMeasure> FebrileNeutropeniaUtilizationMeasures { get; set; }

    public DbSet<CautiBundleElement> CautiBundleElements { get; set; }

    public DbSet<CautiSummaryCensusDays> CautiSummaryCensusDays { get; set; }

    public DbSet<CautiDeviceDay> CautiDeviceDays { get; set; }

    public DbSet<ClabsiDeviceDay> ClabsiDeviceDays { get; set; }

    public DbSet<UserPreferences> UserPreferences { get; set; }

    public DbSet<CautiPatientList> CautiPatientList { get; set; }

    public DbSet<ClabsiPatientList> ClabsiPatientList { get; set; }

    public DbSet<PressureInjuryPatientList> PressureInjuryPatientList { get; set; }

    public DbSet<FebrileNeutropeniaPatientList> FebrileNeutropeniaPatientList { get; set; }

    public DbSet<CautiBundleCompliance> CautiBundleCompliance { get; set; }

    public DbSet<ClabsiBundleCompliance> ClabsiBundleCompliance { get; set; }

    public DbSet<SurveyEvent> SurveyEvents { get; set; }

    public DbSet<Response> Responses { get; set; }

    public DbSet<SurveyEventComment> SurveyEventComments { get; set; }

    public DbSet<SurveyEventAssignment> SurveyEventAssignments { get; set; }

    public DbSet<Form> Forms { get; set; }

    public DbSet<Section> Sections { get; set; }

    public DbSet<Question> Questions { get; set; }

    public DbSet<QuestionType> QuestionTypes { get; set; }

    public DbSet<QuestionOption> QuestionOptions { get; set; }

    public DbSet<DecisionValue> DecisionValues { get; set; }

    public DbSet<SurveyStatusReference> SurveyStatusReference { get; set; }

    public DbSet<ClabsiSummaryCensusDays> ClabsiSummaryCensusDays { get; set; }

    public DbSet<EventSummary> EventSummaries { get; set; }

    public DbSet<EventCategory> EventCategories { get; set; }

    public DbSet<PressureInjuryCensusDays> PressureInjuryCensusDays { get; set; }

    public DbSet<PressureInjuryPressureInjuryEvent> PressureInjuryEvent { get; set; }

    public DbSet<PressureInjuryBundle> PressureInjuryBundles { get; set; }

    public DbSet<PressureInjuryBundleCompliance> PressureInjuryBundleCompliance { get; set; }

    public DbSet<PressureInjuryBundleElement> PressureInjuryBundleElements { get; set; }

    public DbSet<ReferenceBundleElementCompliance> PresureInjuryReferenceBundleElementCompliance { get; set; }

    public DbSet<FebrileNeutropeniaBundle> FebrileNeutropeniaBundles { get; set; }

    public DbSet<FebrileNeutropeniaBundleElement> FebrileNeutropeniaBundleElements { get; set; }

    public DbSet<FebrileNeutropeniaBundleCompliance> FebrileNeutropeniaBundleCompliances { get; set; }

    public DbSet<FebrileNeutropeniaCensusDays> FebrileNeutropeniaCensusDays { get; set; }

    public DbSet<FebrileNeutropeniaAdmissions> FebrileNeutropeniaAdmissions { get; set; }

    public DbSet<FebrileNeutropeniaPopulationAudit> FebrileNeutropeniaPopulationAudits { get; set; }

    public DbSet<MortalityAudit> MortalityAudits { get; set; }

    public DbSet<ReadmissionAudit> ReadmissionAudits { get; set; }

    public DbSet<ClabsiSummaryDevice> ClabsiSummaryDevice { get; set; }

    public DbSet<CautiSummaryDevice> CautiSummaryDevice { get; set; }

    public DbSet<DocumentIntervention> DocumentInterventions { get; set; }

    public DbSet<CautiEventSummaryBundleElement> CautiEventSummaryBundleElements { get; set; }

    public DbSet<CautiEventSummaryBundle> CautiEventSummaryBundles { get; set; }

    public DbSet<CautiEventSummaryRemoval> CautiEventSummaryRemovals { get; set; }

    public DbSet<CautiEventSummaryConfirmedCauti> CautiEventSummaryConfirmedCautis { get; set; }

    public DbSet<CautiEventSummaryInsertion> CautiEventSummaryInsertions { get; set; }

    public DbSet<CautiEventSummaryPositiveUrineCulture> CautiEventSummaryPositiveUrineCultures { get; set; }

    public DbSet<SafetyUser> AuthorizeUsers { get; set; }

    public DbSet<UserPermission> AuthorizeUserPermissions { get; set; }

    public DbSet<Permission> AuthorizePermissions { get; set; }

    public DbSet<GroupUser> AuthorizeGroupUsers { get; set; }

    public DbSet<Group> AuthorizeGroups { get; set; }

    public DbSet<GroupPermissions> AuthorizeGroupPermissions { get; set; }

    public DbSet<IncidentLocationLead> AuthorizeUserLocations { get; set; }

    public DbSet<IncidentCategoryLead> AuthorizeUserCategories { get; set; }

    public DbSet<EventLog> AuthorizeEventLog { get; set; }

    public DbSet<Notification> Notifications { get; set; }

    public DbSet<NotificationType> NotificationTypes { get; set; }

    public DbSet<NotificationCategory> NotificationCategories { get; set; }

    public DbSet<NotificationTypePreferenceOption> NotificationTypePreferenceOptions { get; set; }

    public DbSet<NotificationTriggerTypePreferenceOption> NotificationTriggerTypePreferenceOptions { get; set; }

    public DbSet<IncidentReportTypePreferenceOption> IncidentReportTypePreferenceOptions { get; set; }

    public DbSet<NotificationHarmTypePreferenceOption> NotificationHarmTypePreferenceOptions { get; set; }

    public DbSet<NotificationAssignedToMePreferenceOption> NotificationAssignedToMePreferenceOptions { get; set; }

    public DbSet<RootCauseAnalysisCategory> RootCauseAnalysisCategories { get; set; }

    public DbSet<RootCauseAnalysisOption> RootCauseAnalysisOptions { get; set; }

    public DbSet<GroupsFacilityAccess> GroupsFacilityAccesses { get; set; }

    public DbSet<Incident> IncidentReports { get; set; }

    public DbSet<Link> IncidentLinks { get; set; }

    public DbSet<RestraintsAudit> RestraintsAudits { get; set; }

    public DbSet<HeparinAudit> HeparinAudits { get; set; }

    public DbSet<ExampleAudit> ExampleAudits { get; set; }

    public DbSet<PersonTypeReference> PersonTypeReferences { get; set; }

    public DbSet<AssociatedPerson> AssociatedPersons { get; set; }

    public DbSet<CommonReferenceRace> CommonReferenceRace { get; set; }

    public DbSet<CommonReferenceEthnicity> CommonReferenceEthnicities { get; set; }

    public DbSet<GroupFacilityAccessServiceLines> GroupFacilityAccessServiceLines { get; set; }

    public DbSet<GroupFacilityAccessUnits> GroupFacilityAccessUnits { get; set; }

    public DbSet<SeverityScaleType> SeverityScaleType { get; set; }

    public DbSet<SeverityScaleItem> SeverityScaleItems { get; set; }

    public DbSet<SeverityScaleItemCategory> SeverityScaleItemCategories { get; set; }

    public DbSet<CustomLocation> CustomLocations { get; set; }

    public DbSet<LocationResponse> LocationResponses { get; set; }

    public DbSet<SentinelEventEmailAddress> SentinelEventEmailAddresses { get; set; }

    public DbSet<DailyTriggerCountSnapshot> DailyTriggerCountSnapshots { get; set; }

    public DbSet<SubmittedIncident> SubmittedIncidents { get; set; }

    public DbSet<ReviewAttachment> ReviewAttachments { get; set; }

    public DbSet<File> Blobs { get; set; }

    public DbSet<IncidentAttachmentHistory> IncidentAttachmentHistories { get; set; }

    public DbSet<ReportedIncident> ReportedIncidents { get; set; }

    public DbSet<IncidentQuestionnaire> IncidentQuestionnaires { get; set; }

    public DbSet<TruncatedIncidentQuestionnaire> TruncatedIncidentQuestionnaires { get; set; }

    public DbSet<ActionPlanning> ActionPlanning { get; set; }

    public DbSet<ActionPlanningIntervention> ActionPlanningInterventions { get; set; }

    public DbSet<ActionPlanningIncidentCategorization> ActionPlanningIncidentCategorizations { get; set; }

    public DbSet<ActionPlanningAssociatedIntervention> ActionPlanningAssociatedInterventions { get; set; }

    public DbSet<ActionPlanningAssociatedIncidentCategorization> ActionPlanningAssociatedIncidentCategorizations { get; set; }

    public DbSet<ActionPlanningResponsibleIndividual> ActionPlanningResponsibleIndividuals { get; set; }

    public DbSet<ActionPlanningResponsibleDepartment> ActionPlanningResponsibleDepartments { get; set; }

    public DbSet<ActionPlanningSentDetails> ActionPlanningSentDetails { get; set; }

    public DbSet<IncidentLinkHistoryItem> IncidentLinkHistory { get; set; }

    public DbSet<AuditTool> AuditTools { get; set; }

    public DbSet<AuditToolColumn> AuditToolColumns { get; set; }

    public DbSet<AuditToolFilter> AuditToolFilters { get; set; }

    public DbSet<AuditToolExport> AuditToolExportColumns { get; set; }

    public DbSet<FormDefinition> FormlyDefinitions { get; set; }
    public DbSet<AdverseEventCategoryForm> AdverseEventCategoryForms { get; set; }
    public DbSet<TriggerReviewFormData> TriggerReviewSubForms { get; set; }
    public DbSet<FormData> FormlyForms { get; set; }
    public DbSet<FormDataAudit> FormDataAudits { get; set; }

    public DbSet<IncidentAssignee> IncidentAssignees { get; set; }

    public void DetachAllEntities()
    {
        var changedEntriesCopy = this.ChangeTracker.Entries().Where(e => e.Entity != null).ToList();
        foreach (var entity in changedEntriesCopy)
        {
            this.Entry(entity.Entity).State = EntityState.Detached;
        }
    }

    public async Task<int> SaveChangesAsync()
    {
        DetachReadOnlyEntities();
        return await base.SaveChangesAsync();
    }

    public new int SaveChanges()
    {
        DetachReadOnlyEntities();
        return base.SaveChanges();
    }

    private void DetachReadOnlyEntities()
    {
        var errors = ChangeTracker
            .Entries()
            .Where(e => e.Metadata.IsReadOnly() && e.State != EntityState.Unchanged)
            .Select(e => e.Metadata.Name)
            .Distinct()
            .ToList();

        if (errors.Any())
        {
            this.logger.LogCritical("Attempted to save read-only entities {entities}", string.Join(", ", string.Join(",", errors)));
        }

        foreach (var entityEntry in ChangeTracker.Entries().Where(e => e.Metadata.IsReadOnly()))
        {
            entityEntry.State = EntityState.Detached;
        }
    }

    public new EntityEntry Update(object input)
    {
        return base.Update(input);
    }

    public new EntityEntry Remove(object input)
    {
        return base.Remove(input);
    }

    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.Entity<CommonEncounter>().HasKey(e => new { e.EncounterId, e.EncounterSource });
        modelBuilder.Entity<CommonEncounter>().HasMany(ce => ce.CommonCensusDays).WithOne()
            .HasForeignKey(ce => new { ce.EncounterId, ce.EncounterSource });
        modelBuilder.Entity<CommonEncounter>()
            .HasMany(ce => ce.SurveyEvents)
            .WithOne(se => se.Encounter)
            .HasForeignKey(se => new { se.EncounterId, se.EncounterSource });

        modelBuilder.Entity<CommonBradenScore>().HasKey(e => new { e.EncounterId, e.EncounterSource });
        modelBuilder.Entity<CommonAdt>().HasKey(adt => new { adt.EncounterId, adt.EncounterSource });
        modelBuilder.Entity<CommonCensusDays>().HasKey(ccd => new { ccd.EncounterId, ccd.EncounterSource, ccd.CensusDts });


        modelBuilder.Entity<Provider>().HasKey(p => new { p.ProviderId, p.RowSourceDsc });
        modelBuilder.Entity<AssociatedProvider>().HasOne(ap => ap.Provider);
        modelBuilder.Entity<AssociatedDevices>().HasOne(d => d.Device);
        modelBuilder.Entity<TriggerStatus>().HasOne(status => status.TriggerStatusReference);

        modelBuilder.Entity<TriggerDetail>().HasKey(tr => new { tr.TriggerId, tr.TriggerSourceDataId, tr.PatientEncounterId, tr.PatientEncounterSourceDsc });
        modelBuilder.Entity<TriggerDetail>().HasOne(td => td.Encounter).WithMany().HasForeignKey(td => new { td.PatientEncounterId, td.PatientEncounterSourceDsc });
        modelBuilder.Entity<TriggerDetail>().HasOne(tr => tr.TriggerReference);
        modelBuilder.Entity<TriggerDetail>().HasMany(td => td.TriggerFlags).WithOne().HasForeignKey(td => new { td.TriggerId, td.TriggerSourceDataId, td.PatientEncounterId, td.PatientEncounterSourceDsc });
        modelBuilder.Entity<HistoricalCommonEncounter>().HasKey(htd => new { htd.EncounterId, htd.EncounterSource });
        modelBuilder.Entity<HistoricalCommonEncounter>()
            .HasMany(ce => ce.SurveyEvents)
            .WithOne(se => se.HistoricalEncounter)
            .HasForeignKey(se => new { se.EncounterId, se.EncounterSource });
        modelBuilder.Entity<HistoricalTriggerDetail>().HasKey(htd => new { htd.TriggerId, htd.PatientEncounterId, htd.TriggerSourceDataId, htd.PatientEncounterSourceDsc });
        modelBuilder.Entity<HistoricalTriggerDetail>().HasOne(td => td.Encounter).WithMany().HasForeignKey(htd => new { htd.PatientEncounterId, htd.PatientEncounterSourceDsc });
        modelBuilder.Entity<HistoricalTriggerDetail>().HasMany(htd => htd.TriggerFlags).WithOne().HasForeignKey(htd => new { htd.TriggerId, htd.TriggerSourceDataId, htd.PatientEncounterId, htd.PatientEncounterSourceDsc });
        modelBuilder.Entity<TriggerEventReview>().HasMany(ter => ter.AssociatedProviders);
        modelBuilder.Entity<TriggerEventReview>().HasMany(ter => ter.AssociatedDevices);
        modelBuilder.Entity<TriggerEventReview>().HasMany(ter => ter.ReviewToRootCauseOptions);
        modelBuilder.Entity<PublicHealthEventReview>().HasMany(pher => pher.AssociatedProviders);
        modelBuilder.Entity<TriggerFlag>().HasKey(tf => new { tf.TriggerId, tf.TriggerSourceDataId, tf.PatientEncounterId, tf.PatientEncounterSourceDsc, tf.FlagDsc });
        modelBuilder.Entity<UnitReference>().HasKey(ur => ur.UnitId);
        modelBuilder.Entity<BathingMethodReference>().HasKey(ur => ur.BathingMethodDsc);
        modelBuilder.Entity<PatientAllergy>().HasKey(t => new { t.AllergyId, t.PatientId, t.HospitalAccountSourceDsc });
        modelBuilder.Entity<PatientActiveMedicationOrder>().HasKey(t => new { t.MedicationOrderId, t.PatientEncounterId, t.PatientEncounterSourceDsc });
        modelBuilder.Entity<PatientSurgeryProcedures>().HasKey(t => new { t.LogId, t.PatientEncounterId, t.PatientEncounterSourceDsc });
        modelBuilder.Entity<PatientLab>().HasKey(t => new { t.LabResultId, t.PatientEncounterId, t.PatientEncounterSourceDsc });
        modelBuilder.Entity<PatientRadiologyOrder>().HasKey(t => new { t.OrderProcedureId, t.PatientEncounterId, t.PatientEncounterSourceDsc });
        modelBuilder.Entity<FeatureFlag>().HasKey(ff => ff.Id);
        modelBuilder.Entity<ImprovementModule>().HasKey(ff => ff.Id);
        modelBuilder.Entity<ClabsiDeviceDay>().HasKey(d => new { d.EncounterId, d.EncounterSourceDsc, d.LineDrainAirwayId, d.CensusDateTime });
        modelBuilder.Entity<MaintenanceBundle>().HasKey(mb => mb.ElementId);
        modelBuilder.Entity<InsertionBundle>().HasKey(ib => ib.ElementId);
        modelBuilder.Entity<CautiBundle>().HasKey(b => new { b.BundleType, b.ElementId });
        modelBuilder.Entity<PreInfectionBundleElementCompliance>().HasKey(
            c => new { c.EncounterId, c.EncounterSourceDsc, c.BundleName, c.ElementName });

        modelBuilder.Entity<PreInjuryBundleElementCompliance>().HasKey(
            c => new { c.EncounterId, c.EncounterSourceDsc, c.BundleName, c.ElementName });

        modelBuilder.Entity<FebrileNeutropeniaBundleElementComplianceReference>()
            .HasKey(b => new { b.BundleName, b.ElementName });
        modelBuilder.Entity<ClabsiBundleElementComplianceReference>()
            .HasKey(b => new { b.BundleName, b.ElementName, b.ElementComplianceDesc });

        modelBuilder.Entity<FebrileNeutropeniaPatientList>()
            .HasMany(p => p.BundleElements)
            .WithOne()
            .HasForeignKey(c => new { c.EncounterId, c.DataDateTime, c.EncounterSourceDsc });

        modelBuilder.Entity<ReferenceBundleElementCompliance>()
            .HasKey(p => new { p.ElementName, p.ElementComplianceDescription });
        modelBuilder.Entity<CautiBundleElement>().HasKey(
            be => new
            {
                be.LineDrainAirwayId,
                be.BundleId,
                be.ElementId,
                be.DataDts,
                be.IntervalDsc,
                be.EncounterId,
                be.EncounterSourceDescription
            });
        modelBuilder.Entity<CautiSummaryCensusDays>()
            .HasKey(cd => new { CensusDateTime = cd.CensusDts, cd.EncounterId, cd.EncounterSourceDsc });
        modelBuilder.Entity<CautiDeviceDay>().HasKey(
            dd => new { dd.EncounterId, dd.EncounterSourceDsc, dd.CensusDateTime, dd.LineDrainAirwayId });

        modelBuilder.Entity<ClabsiSummaryCensusDays>().HasKey(cd => new { cd.EncounterId, cd.EncounterSourceDsc, cd.CensusDts });
        modelBuilder.Entity<ClabsiSummaryCensusDays>().HasMany(c => c.PreInfectionBundleElementCompliances)
            .WithOne().HasForeignKey(c => new { c.EncounterId, c.EncounterSourceDsc, c.EventDateTime });

        modelBuilder.Entity<PressureInjuryCensusDays>().HasKey(cd => new { cd.EncounterId, cd.EncounterSourceDescription, cd.CensusDateTime });
        modelBuilder.Entity<PressureInjuryCensusDays>()
            .HasMany(c => c.PreHacBundleElementCompliances)
            .WithOne()
            .HasForeignKey(c => new { c.EncounterId, c.EncounterSourceDsc, c.EventDateTime });

        modelBuilder.Entity<FebrileNeutropeniaCensusDays>().HasKey(cd => new { CensusDts = cd.CensusDateTime, cd.EncounterId });
        modelBuilder.Entity<CautiPatientList>()
            .HasKey(pl => new { pl.LineDrainAirwayId, pl.EncounterId, pl.CensusDts, pl.EncounterSourceDsc });

        modelBuilder.Entity<ClabsiPatientList>()
            .HasKey(pl => new { pl.LineDrainAirwayId, pl.EncounterId, pl.CensusDts, pl.EncounterSourceDsc });

        modelBuilder.Entity<PressureInjuryPatientList>()
            .HasKey(pl => new { pl.EncounterId, pl.CensusDts, pl.EncounterSourceDsc });
        modelBuilder.Entity<PressureInjuryPatientList>()
            .HasMany(p => p.BundleElements)
            .WithOne(p => p.PressureInjuryPatientList)
            .HasForeignKey(c => new { c.EncounterId, c.DataDateTime, c.EncounterSourceDsc });

        modelBuilder.Entity<FebrileNeutropeniaPatientList>()
            .HasKey(pl => new { pl.EncounterId, pl.CensusDts, pl.EncounterSourceDsc });

        modelBuilder.Entity<CautiBundleCompliance>()
            .HasKey(pl => new { pl.LineDrainAirwayId, pl.BundleId, pl.DataDts, pl.IntervalDsc, pl.EncounterId, pl.EncounterSourceDsc });
        modelBuilder.Entity<ClabsiBundle>().HasKey(b => new { b.BundleType, b.ElementId });
        modelBuilder.Entity<ClabsiBundleElement>().HasKey(
            tf => new { tf.EncounterId, tf.LineDrainAirwayId, tf.ElementId, tf.DataDts, tf.IntervalDsc });
        modelBuilder.Entity<ClabsiBundleCompliance>().HasKey(
            pl => new
            {
                pl.LineDrainAirwayId,
                pl.BundleId,
                pl.EncounterId,
                pl.DataDts,
                pl.IntervalDsc
            });
        modelBuilder.Entity<ClabsiBundleCompliance>().HasOne(c => c.Encounter);
        modelBuilder.Entity<ClabsiSummaryDevice>()
            .HasKey(pl => new { pl.EncounterId, pl.EncounterSource, pl.LineDrainAirWayId });
        modelBuilder.Entity<AuditClabsi>()
            .HasKey(c => new { c.EncounterId, c.EncounterSource, c.LineDrainAirWayId });
        modelBuilder.Entity<AuditClabsi>()
            .HasOne(ac => ac.Encounter)
            .WithMany()
            .HasForeignKey(ac => new { ac.EncounterId, ac.EncounterSource });
        modelBuilder.Entity<AuditCauti>()
            .HasKey(c => new { c.EncounterId, c.EncounterSource, c.LineDrainAirWayId });
        modelBuilder.Entity<AuditCauti>()
            .HasOne(ac => ac.Encounter)
            .WithMany()
            .HasForeignKey(ac => new { ac.EncounterId, ac.EncounterSource });

        modelBuilder.Entity<CautiSummaryDevice>()
            .HasKey(pl => new { pl.EncounterId, pl.EncounterSource, pl.LineDrainAirWayId });

        modelBuilder.Entity<SkinIntegrityEncounter>()
            .HasKey(c => new { c.EncounterId, c.EncounterSource, c.WoundId });

        modelBuilder.Entity<SkinIntegrityEncounter>().HasOne<CommonEncounter>(s => s.Encounter).WithMany(c => c.SkinIntegrityEncounters)
            .HasForeignKey(c => new { c.EncounterId, c.EncounterSource });

        modelBuilder.Entity<SurveyEvent>()
            .HasOne(s => s.Encounter)
            .WithMany(ce => ce.SurveyEvents)
            .HasForeignKey(s => new { s.EncounterId, s.EncounterSource });

        modelBuilder.Entity<SurveyEvent>()
            .HasOne(s => s.HistoricalEncounter)
            .WithMany(ce => ce.SurveyEvents)
            .HasForeignKey(s => new { s.EncounterId, s.EncounterSource });

        modelBuilder.Entity<SurveyEvent>().HasOne<Incident>(s => s.Incident);

        modelBuilder.Entity<SurveyEvent>()
            .HasOne(s => s.Form)
            .WithMany()
            .HasForeignKey(c => c.FormId);

        modelBuilder.Entity<SurveyEvent>()
            .HasMany(s => s.Comments).WithOne()
            .HasForeignKey(s => new { s.SurveyEventId });

        modelBuilder.Entity<SurveyEvent>()
            .HasMany(s => s.IncidentReviewerComments)
            .WithOne()
            .HasForeignKey(c => c.SurveyEventId);

        modelBuilder.Entity<SurveyEvent>()
            .Navigation(c => c.IncidentReviewerComments).AutoInclude();
        modelBuilder.Entity<SurveyEvent>()
            .Navigation(c => c.Comments).AutoInclude();

        modelBuilder.Entity<SurveyEvent>()
            .HasOne(s => s.ParentSurveyEvent)
            .WithMany(ps => ps.ChildSurveyEvents)
            .HasForeignKey(s => s.ParentSurveyEventId);

        modelBuilder.Entity<SurveyEvent>()
            .HasMany(s => s.Assignments)
            .WithOne(a => a.SurveyEvent)
            .HasForeignKey(a => a.SurveyEventId);

        modelBuilder.Entity<SurveyEvent>()
            .HasOne(c => c.TriggerEventToReview)
            .WithOne(c => c.SurveyEvent)
            .HasForeignKey<TriggerEventToReview>(c => c.SurveyEventId)
            .HasPrincipalKey<SurveyEvent>(c => c.SurveyEventId);

        modelBuilder.Entity<SafetyUser>()
            .HasMany<SurveyEvent>()
            .WithOne(c => c.CreatedByUser)
            .HasForeignKey(c => c.CreatedBy);

        modelBuilder.Entity<SafetyUser>()
            .HasMany<SurveyEvent>()
            .WithOne(c => c.ModifiedByUser)
            .HasForeignKey(c => c.ModifiedBy);

        modelBuilder.Entity<EventCategory>();
        modelBuilder.Entity<EventCategory>().HasKey(ec => ec.EventCategoryId);
        modelBuilder.Entity<EventSummary>().HasOne(es => es.EventCategory);
        modelBuilder.Entity<EventSummary>().HasKey(
            e => new
            {
                e.EventCategoryId,
                e.EventSourceDataId,
                e.EventSourceDataTypeDsc,
                e.EventEncounterId,
                e.EventEncounterSourceDsc
            });

        modelBuilder.Entity<PublicHealthToReview>()
            .HasOne(t => t.Trigger)
            .WithMany()
            .HasForeignKey(p => new { p.TriggerId, p.PatientEncounterId, p.PatientEncounterSourceDsc, p.TriggerSourceDataId });

        modelBuilder.Entity<Form>()
            .HasMany(form => form.ChildForms).WithOne(form => form.ParentForm)
            .HasForeignKey(k => new { k.ParentFormId });

        modelBuilder.Entity<Question>().HasQueryFilter(c => c.IsActive);

        modelBuilder.Entity<Question>().HasMany(question => question.SubQuestions)
            .WithOne(question => question.ParentQuestion).HasForeignKey(k => new { k.ParentQuestionId });

        modelBuilder.Entity<Question>().HasMany(question => question.DecisionValues)
            .WithOne(question => question.EnableQuestion).HasForeignKey(k => new { k.EnableQuestionId });

            modelBuilder.Entity<Question>()
                .HasMany<QuestionHelpData>(q => q.QuestionHelpData)
                .WithOne(qhd => qhd.Question)
                .HasForeignKey(qhd => qhd.QuestionID);

            modelBuilder.Entity<QuestionOption>().HasMany(option => option.SubQuestions)
                .WithOne(option => option.ParentOption).HasForeignKey(k => new { k.ParentOptionId });

        modelBuilder.Entity<PressureInjuryBundleCompliance>()
            .HasKey(pl => new { pl.BundleId, pl.DataDateTime, pl.IntervalDescription, pl.EncounterId, pl.EncounterSourceDescription });

        modelBuilder.Entity<PressureInjuryBundle>().HasKey(b => new { b.BundleType, b.ElementId });

        modelBuilder.Entity<FebrileNeutropeniaBundle>().HasKey(b => new { b.BundleType, b.ElementId });

        modelBuilder.Entity<PressureInjuryBundleElement>().HasKey(
            tf => new { tf.BundleId, tf.ElementId, tf.DataDateTime, tf.IntervalDescription, tf.EncounterId, tf.EncounterSourceDsc });
        modelBuilder.Entity<FebrileNeutropeniaBundleElement>().HasKey(
            tf => new { tf.BundleId, tf.ElementId, tf.DataDateTime, tf.IntervalDescription, tf.EncounterId, tf.EncounterSourceDsc });
        modelBuilder.Entity<FebrileNeutropeniaBundleCompliance>()
            .HasKey(pl => new { pl.BundleId, pl.DataDateTime, pl.IntervalDescription, pl.EncounterId, pl.EncounterSourceDescription });
        modelBuilder.Entity<FebrileNeutropeniaAdmissions>()
            .HasKey(p => new { p.EncounterId, p.EncounterSourceDescription });
        modelBuilder.Entity<PressureInjuryPressureInjuryEvent>().HasKey(
            pi => new { pi.EncounterId, pi.EncounterSourceDescription, pi.EventDateTime, pi.SourceEventId });

        modelBuilder.Entity<FebrileNeutropeniaPopulationAudit>().HasKey(pa => new { pa.EncounterId, pa.EncounterSourceDsc });

        modelBuilder.Entity<MortalityAudit>().HasKey(pa => new { pa.EncounterId, pa.EncounterSource });
        modelBuilder.Entity<ReadmissionAudit>().HasKey(pa => new { pa.EncounterId, pa.EncounterSource });

        modelBuilder.Entity<CautiPredictionOutput>().HasKey(
            po => new { po.CensusDts, po.EncounterId, po.EncounterSourceDsc });
        modelBuilder.Entity<CautiPredictionOutput>().HasOne<CautiSummaryCensusDays>(t => t.SummaryCensusDays).WithMany()
            .HasForeignKey(c => new { c.CensusDts, c.EncounterId, c.EncounterSourceDsc});
        modelBuilder.Entity<CautiEventSummary>().HasKey(es => new { es.EncounterId, es.EncounterSourceDsc, es.EventCategoryId, es.EventSourceDataId });
        modelBuilder.Entity<CautiReferenceRiskCategory>().HasKey(c => c.RiskDsc);
        modelBuilder.Entity<CautiReferenceRiskCategory>().HasMany(c => c.CautiPredictionOutputs).WithOne()
            .HasForeignKey(c => c.RiskDescription);

        modelBuilder.Entity<Comment>().ToTable("Comment", "SafetySurveillance")
            .HasDiscriminator<int>("CommentType")
            .HasValue<DocumentIntervention>((int)CommentTypeEnum.DocumentIntervention);

        modelBuilder.Entity<CautiEventSummary>().HasDiscriminator<int>("EventCategoryID")
            .HasValue<CautiEventSummaryBundle>((int)EventCategoryEnum.Bundle)
            .HasValue<CautiEventSummaryBundleElement>((int)EventCategoryEnum.BundleElement)
            .HasValue<CautiEventSummaryConfirmedCauti>((int)EventCategoryEnum.ConfirmedCauti)
            .HasValue<CautiEventSummaryInsertion>((int)EventCategoryEnum.Insertion)
            .HasValue<CautiEventSummaryRemoval>((int)EventCategoryEnum.Removal)
            .HasValue<CautiEventSummaryPositiveUrineCulture>((int)EventCategoryEnum.PositiveUrineCulture);

        // modelBuilder.Entity<Comment>().Property(c => c.UserName).HasConversion(t => t, f => f.ToLower());

        modelBuilder.Entity<SafetyUser>().HasMany<Comment>().WithOne(c => c.SafetyUser)
            .HasForeignKey(c => c.UserName);

        modelBuilder.Entity<SafetyUser>().HasMany<TriggerEventComment>().WithOne(c => c.SafetyUser)
            .HasForeignKey(c => c.UserName);

        modelBuilder.Entity<SafetyUser>().HasMany<SurveyEventComment>().WithOne(c => c.SafetyUser)
            .HasForeignKey(c => c.User);

        modelBuilder.Entity<SafetyUser>().HasMany<TriggerEventInvestigationDetail>().WithOne(c => c.SafetyUser)
            .HasForeignKey(c => c.UserName);

        modelBuilder.Entity<SafetyUser>().HasMany<AuditLog>().WithOne(c => c.SafetyUser)
            .HasForeignKey(c => c.UserName);

        modelBuilder.Entity<SafetyUser>().HasMany<IncidentAttachmentHistory>().WithOne(c => c.SafetyUser)
            .HasForeignKey(c => c.UserName);

        modelBuilder.Entity<IncidentLocationLead>()
            .HasOne<SafetyUser>(l => l.SafetyUser)
            .WithMany(u => u.UserLocationLeads)
            .HasForeignKey(l => l.UserName);

        modelBuilder.Entity<IncidentCategoryLead>()
            .HasOne<SafetyUser>(l => l.SafetyUser)
            .WithMany(u => u.UserCategoryLeads)
            .HasForeignKey(l => l.UserName);

        modelBuilder
            .Entity<IncidentCategoryLead>()
            .HasOne(uc => uc.Category);

        modelBuilder.Entity<CautiPredictionOutput>()
            .HasMany(c => c.Comments)
            .WithOne()
            .HasForeignKey(c => new { c.EncounterId, c.EncounterSourceDsc })
            .HasPrincipalKey(c => new { c.EncounterId, c.EncounterSourceDsc });

        modelBuilder.Entity<CautiPredictionOutput>().HasMany<CautiEventSummaryBundle>(c => c.CautiEventSummaries).WithOne()
            .HasForeignKey(c => new { c.EncounterId, c.EncounterSourceDsc })
            .HasPrincipalKey(c => new { c.EncounterId, c.EncounterSourceDsc });

        modelBuilder.Entity<CautiEventSummaryBundle>().HasMany<CautiEventSummaryBundleElement>(c => c.BundleElements).WithOne()
            .HasForeignKey(c => c.PrereqEventSourceDataId).HasPrincipalKey(c => c.EventSourceDataId);

        modelBuilder.Entity<Notification>().HasOne(c => c.NotificationType).WithMany();
        modelBuilder.Entity<Notification>().Property(c => c.CreatedDateTime).HasConversion(c => c, c => c.ToUniversalTime());

        modelBuilder.Entity<NotificationCategory>().HasKey(category => category.CategoryId);
        modelBuilder.Entity<NotificationCategory>().HasMany(c => c.NotificationTypes);

        modelBuilder.Entity<NotificationType>().HasKey(type => type.TypeId);

        modelBuilder.Entity<NotificationTriggerTypePreferenceOption>()
            .HasOne(reference => reference.TriggerReference);

        modelBuilder.Entity<NotificationTypePreferenceOption>().HasKey(option => option.OptionId);
        modelBuilder.Entity<NotificationTriggerTypePreferenceOption>().HasKey(tp => tp.Id);

        modelBuilder.Entity<NotificationHarmTypePreferenceOption>().HasKey(ht => ht.Id);

        modelBuilder.Entity<NotificationAssignedToMePreferenceOption>().HasKey(atm => atm.Id);

        modelBuilder.Entity<Group>().HasKey(group => group.GroupId);
        modelBuilder.Entity<Group>().HasQueryFilter(group => !group.IsDeleted);
        modelBuilder.Entity<Group>().HasMany(group => group.GroupPermissions);
        modelBuilder.Entity<Group>().HasMany(group => group.GroupFacilityAccesses);
        modelBuilder.Entity<Group>().HasOne(g => g.ParentGroup).WithMany().HasForeignKey(c => c.ParentGroupId);

        modelBuilder.Entity<GroupsFacilityAccess>().HasKey(fa => fa.Id);
        modelBuilder.Entity<GroupFacilityAccessServiceLines>().HasKey(sl => sl.Id);
        modelBuilder.Entity<GroupFacilityAccessUnits>().HasKey(unit => unit.Id);
        modelBuilder.Entity<GroupsFacilityAccess>().HasMany(fa => fa.ServiceLines).WithOne().OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<GroupsFacilityAccess>().HasMany(fa => fa.Units).WithOne().OnDelete(DeleteBehavior.Cascade);


        modelBuilder.Entity<FebrileNeutropeniaPopulationAudit>().HasOne(p => p.Encounter)
            .WithMany()
            .HasForeignKey(c => new { c.EncounterId, c.EncounterSourceDsc });


        modelBuilder.Entity<MortalityAudit>().HasOne(p => p.Encounter)
            .WithMany()
            .HasForeignKey(c => new { c.EncounterId, c.EncounterSource });

        modelBuilder.Entity<ReadmissionAudit>().HasOne(p => p.Encounter)
            .WithMany()
            .HasForeignKey(c => new { c.EncounterId, c.EncounterSource });

        modelBuilder.Entity<RootCauseAnalysisCategory>().HasKey(cat => cat.CategoryId);
        modelBuilder.Entity<RootCauseAnalysisCategory>().HasMany(cat => cat.Options).WithOne(cat => cat.Category)
            .HasForeignKey(option => option.CategoryId);

        modelBuilder.Entity<ReviewToRootCauseOption>().HasKey(rrco => rrco.Id);
        modelBuilder.Entity<ReviewToRootCauseOption>().HasOne(t => t.Option);

        modelBuilder.Entity<TriggerEventReview>().Property(c => c.ReviewDts)
            .HasConversion(c => c.Value.ToLocalTime(), c => c.ToLocalTime());

        modelBuilder.Entity<TriggerEventReview>().HasOne(t => t.ScaleItem).WithMany();
        modelBuilder.Entity<TriggerEventReview>().Navigation(c => c.ScaleItem).AutoInclude();

        modelBuilder.Entity<SubmittedIncident>().HasOne(t => t.ScaleItem).WithMany();
        modelBuilder.Entity<SubmittedIncident>().Navigation(t => t.ScaleItem).AutoInclude();

        modelBuilder.Entity<IncidentAssignee>().HasKey(c => new { c.IncidentId, c.Username });

        modelBuilder.Entity<SubmittedIncident>()
            .HasMany(i => i.Assignees)
            .WithOne()
            .HasForeignKey(a => a.IncidentId);

        modelBuilder.Entity<SeverityScaleType>()
            .HasMany(s => s.Items)
            .WithOne(si => si.ScaleType);

        modelBuilder.Entity<SeverityScaleItem>().Navigation(c => c.ScaleType).AutoInclude();

        modelBuilder.Entity<AdverseEventCategoryReference>().HasOne(a => a.ScaleType).WithMany();

        modelBuilder.Entity<ProviderMultiSelectResponse>().HasOne(c => c.Provider).WithMany()
            .HasForeignKey(c => new { c.ProviderId, c.RowSourceDsc });

        modelBuilder.Entity<AuditPressureInjury>().HasKey(c => new {c.EncounterId, c.EncounterSource});

        modelBuilder.Entity<AuditPressureInjury>().HasOne(a => a.Encounter).WithOne()
            .HasForeignKey<AuditPressureInjury>(a => new {a.EncounterId, a.EncounterSource});

        modelBuilder.Entity<AuditPressureInjury>()
            .HasMany(a => a.BradenScore)
            .WithOne()
            .HasForeignKey(a => new { a.EncounterId, a.EncounterSource });

        modelBuilder.Entity<CommonEncounter>().HasOne(c => c.CommonAdts).WithMany()
            .HasForeignKey(c => new {c.EncounterId, c.EncounterSource});

        modelBuilder.Entity<CommonAdt>().HasQueryFilter(c => c.LatestDepartmentFlg == 1);

        modelBuilder.Entity<ReferenceImprovementMeasure>().HasKey(c => new {c.MeasureId});

        modelBuilder.Entity<ImprovementIntervention>().HasKey(c => new {c.MeasureId, c.InterventionName});

        modelBuilder.Entity<ReferenceImprovementMeasure>().HasMany(c => c.ImprovementInterventions).WithOne()
            .HasForeignKey(c => c.MeasureId);

        modelBuilder.Entity<ImprovementMeasureData>().HasKey(c => new
            { c.MeasureDataSource, c.MeasureId, c.ReportDate, c.Location, c.Unit, c.ServiceLine });

        modelBuilder.Entity<ImprovementMeasureData>().HasOne(c => c.ReferenceImprovementMeasure).WithMany()
            .HasForeignKey(c => c.MeasureId);

        modelBuilder.Entity<Response>().HasOne(c => c.ResponseOption).WithMany()
            .HasForeignKey(c => c.ResponseOptionId);

        modelBuilder.Entity<Incident>().HasOne<SurveyEvent>(c => c.SurveyEvent).WithOne(c => c.Incident)
            .HasForeignKey<Incident>(c => c.SurveyEventId);

        modelBuilder.Entity<Incident>().HasQueryFilter(c => !c.IsDeleted);
        modelBuilder.Entity<SurveyEvent>().HasQueryFilter(c => !c.IsDeleted);

        modelBuilder.Entity<AssociatedPerson>().HasOne(c => c.Response)
            .WithOne(c => c.AssociatedPersonResponse)
            .HasForeignKey<AssociatedPerson>(c => c.ResponseId);

        modelBuilder.Entity<AssociatedPerson>().HasOne<CommonEncounter>(c => c.Encounter).WithMany()
            .HasForeignKey(c => new { c.EncounterId, c.EncounterSource });

        modelBuilder.Entity<AssociatedPerson>()
            .HasOne<AssociatedAddress>(ap => ap.AssociatedAddress)
            .WithOne(c => c.AssociatedPerson)
            .HasForeignKey<AssociatedAddress>(c => c.AssociatedPersonId)
            .HasPrincipalKey<AssociatedPerson>(c => c.AssociatedPersonId)
            .OnDelete(DeleteBehavior.Cascade);

        modelBuilder.Entity<AssociatedAddress>().HasKey(associatedAddress => associatedAddress.Id);

        modelBuilder.Entity<SurveyEvent>().HasOne(s => s.CreatedByUser).WithMany().HasForeignKey(e => e.CreatedBy);
        modelBuilder.Entity<SurveyEvent>().HasOne(s => s.ModifiedByUser).WithMany().HasForeignKey(e => e.ModifiedBy);

        modelBuilder.Entity<RestraintsAudit>().HasKey(c => new { c.EncounterId, c.EncounterSource });
        modelBuilder.Entity<RestraintsAudit>().HasOne(c => c.Encounter).WithOne()
            .HasForeignKey<RestraintsAudit>(c => new { c.EncounterId, c.EncounterSource });

        modelBuilder.Entity<HeparinAudit>().HasKey(c => new { c.EncounterId, c.EncounterSource, c.MedicationOrderId, c.DataDts });
        modelBuilder.Entity<HeparinAudit>().HasOne(c => c.Encounter).WithMany()
            .HasForeignKey(c => new { c.EncounterId, c.EncounterSource });

        modelBuilder.Entity<ExampleAudit>().HasKey(c => new { c.EncounterId, c.EncounterSource });
        modelBuilder.Entity<ExampleAudit>().HasOne(c => c.Encounter).WithOne()
            .HasForeignKey<ExampleAudit>(c => new { c.EncounterId, c.EncounterSource });


        modelBuilder.Entity<IncidentReportTypePreferenceOption>().HasOne(c => c.AdverseEventCategory).WithMany()
            .HasForeignKey(c => c.CategoryId);

        modelBuilder.Entity<MortalityAudit>().HasOne(c => c.Encounter)
            .WithOne(c => c.MortalityAudit)
            .HasForeignKey<MortalityAudit>(c => new { c.EncounterId, c.EncounterSource });

        modelBuilder.Entity<ReadmissionAudit>().HasOne(c => c.Encounter)
            .WithOne(c => c.ReadmissionAudit)
            .HasForeignKey<ReadmissionAudit>(c => new { c.EncounterId, c.EncounterSource });

        modelBuilder.Entity<PressureInjurySummaryEncounters>()
            .HasKey(c => new { c.EncounterId, c.EncounterSourceDsc });
        modelBuilder.Entity<FNSummaryEncounters>()
            .HasKey(c => new { c.EncounterId, c.EncounterSourceDsc });

        modelBuilder.Entity<CustomLocation>().HasKey(e => new { e.Id, e.Facility });

        modelBuilder.Entity<LocationResponse>()
            .HasOne<CustomLocation>()
            .WithOne()
            .HasForeignKey<LocationResponse>(l => new { l.CustomLocationID, l.Facility });

        modelBuilder.Entity<AssociatedLocation>()
            .HasOne<CustomLocation>(al => al.CustomLocation)
            .WithMany(cl => cl.AssociatedLocations)
            .HasForeignKey(l => new { l.CustomLocationID, l.Facility });

        modelBuilder.Entity<SentinelEventEmailAddress>().HasKey(e => new { e.Id, e.Facility });

        modelBuilder.Entity<TriggerEventReview>().HasOne(c => c.AdverseEventCategoryReference).WithMany().HasForeignKey(c => c.AdverseEventCategoryId);

        modelBuilder.Entity<TriggerEventToReview>()
            .HasMany(c => c.TriggerEventComments)
            .WithOne(x => x.TriggerEventToReview)
            .HasPrincipalKey(x => x.TriggerSourceDataId)
            .HasForeignKey(x => x.TriggerSourceDataId);

        modelBuilder.Entity<DailyTriggerCountSnapshot>()
            .HasNoKey();

        modelBuilder.Entity<ReferenceLocationServiceLine>()
            .HasKey(t => new {t.ServiceLine, t.Location});

        modelBuilder.Entity<IncidentQuestionnaire>().HasOne(c => c.AssociatedPerson).WithOne()
            .HasForeignKey<AssociatedPerson>(c => c.ResponseId)
            .HasPrincipalKey<IncidentQuestionnaire>(c => c.AssociatedEntityResponseID);
        modelBuilder.Entity<IncidentQuestionnaire>().Navigation(c => c.AssociatedPerson).AutoInclude();
        modelBuilder.Entity<IncidentQuestionnaire>().Property(c => c.OccuredOn).HasConversion<string>(s => s.HasValue ? s.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") : null, d => DateTime.Parse(d.Replace("T", " ").Replace("Z", "")));


        modelBuilder.Entity<TruncatedIncidentQuestionnaire>().HasOne(c => c.AssociatedPerson).WithOne()
            .HasForeignKey<AssociatedPerson>(c => c.ResponseId)
            .HasPrincipalKey<TruncatedIncidentQuestionnaire>(c => c.AssociatedEntityResponseID);
        modelBuilder.Entity<TruncatedIncidentQuestionnaire>().Navigation(c => c.AssociatedPerson).AutoInclude();
        modelBuilder.Entity<TruncatedIncidentQuestionnaire>().Property(c => c.OccuredOn).HasConversion<string>(s => s.HasValue ? s.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") : null, d => DateTime.Parse(d.Replace("T", " ").Replace("Z", "")));


        modelBuilder.Entity<SubmittedIncident>().HasMany<InvestigationDetail>(c => c.InvestigationDetailsList).WithOne()
            .HasForeignKey(c => c.SurveyEventId).HasPrincipalKey(c => c.SurveyEventID);
        modelBuilder.Entity<SubmittedIncident>().HasMany<IncidentComment>(c => c.Comments).WithOne()
            .HasForeignKey(c => c.SurveyEventId).HasPrincipalKey(c => c.SurveyEventID);
        modelBuilder.Entity<SubmittedIncident>().HasMany<ReviewAssignmentLog>(c => c.ReviewAssignmentLogs).WithOne()
            .HasForeignKey(c => c.IncidentId);
        modelBuilder.Entity<SubmittedIncident>().HasOne<IncidentQuestionnaire>(c => c.ReportedIncidentDetails).WithOne()
            .HasForeignKey<SubmittedIncident>(c => c.IncidentId);
        modelBuilder.Entity<SubmittedIncident>().HasOne<TruncatedIncidentQuestionnaire>(c => c.TruncatedIncidentDetails).WithOne()
            .HasForeignKey<SubmittedIncident>(c => c.IncidentId);
        modelBuilder.Entity<SubmittedIncident>().HasMany<IncidentAssociatedLocation>(c => c.AssociatedLocations).WithOne()
            .HasForeignKey(c => c.IncidentId);
        modelBuilder.Entity<SubmittedIncident>().HasMany<IncidentAssociatedServiceLine>(c => c.AssociatedServiceLines).WithOne()
            .HasForeignKey(c => c.IncidentId);
        modelBuilder.Entity<SubmittedIncident>().HasMany<IncidentAssociatedProvider>(c => c.AssociatedProviders).WithOne()
            .HasForeignKey(c => c.IncidentId);
        modelBuilder.Entity<SubmittedIncident>().HasMany<IncidentAssociatedDevice>(c => c.AssociatedDevices).WithOne()
            .HasForeignKey(c => c.IncidentId);
        modelBuilder.Entity<SubmittedIncident>().HasMany<IncidentRootCauseAnalysisOption>(c => c.RootCauseAnalysisOptions).WithOne()
            .HasForeignKey(c => c.IncidentId);
        modelBuilder.Entity<SubmittedIncident>().HasMany<IncidentAttachment>(c => c.Attachments).WithOne()
            .HasForeignKey(c => c.IncidentId);
        modelBuilder.Entity<SubmittedIncident>().HasMany<IncidentAttachmentHistory>(c => c.AttachmentHistory).WithOne()
            .HasForeignKey(c => c.IncidentId);
        modelBuilder.Entity<SubmittedIncident>().HasOne<Link>(c => c.LinkState).WithOne()
            .HasForeignKey<Link>(c => c.IncidentId);

        modelBuilder.Entity<TriggerEventReview>().HasOne<ActionPlanning>(x => x.ActionPlanning).WithOne()
            .HasForeignKey<ActionPlanning>(z => z.ReviewId);

        modelBuilder.Entity<TriggerEventReview>()
            .HasMany(ter => ter.TriggerReviewFormData)
            .WithOne()
            .HasForeignKey(trfd => trfd.TriggerReviewId);

        modelBuilder.Entity<SubmittedIncident>().HasOne<ActionPlanning>(x => x.ActionPlanning).WithOne()
            .HasForeignKey<SubmittedIncident>(z => z.ReviewId);

        modelBuilder.Entity<ActionPlanning>()
            .HasMany<ActionPlanningAssociatedIntervention>(x => x.AssociatedInterventions)
            .WithOne()
            .HasForeignKey(x => x.ReviewId);

        modelBuilder.Entity<ActionPlanning>()
            .HasMany<ActionPlanningAssociatedIncidentCategorization>(x => x.AssociatedIncidentCategorizations)
            .WithOne()
            .HasForeignKey(x => x.ReviewId);

        modelBuilder.Entity<ActionPlanning>()
            .HasMany<ActionPlanningResponsibleIndividual>(x => x.ResponsibleIndividuals)
            .WithOne()
            .HasForeignKey(x => x.ReviewId);

        modelBuilder.Entity<ActionPlanning>()
            .HasMany<ActionPlanningResponsibleDepartment>(x => x.ResponsibleDepartments)
            .WithOne()
            .HasForeignKey(x => x.ReviewId);

        modelBuilder.Entity<ActionPlanningAssociatedIntervention>()
            .HasOne<ActionPlanningIntervention>(x => x.Intervention)
            .WithOne()
            .HasForeignKey<ActionPlanningAssociatedIntervention>(x => x.InterventionId);

        modelBuilder.Entity<ActionPlanningAssociatedIncidentCategorization>()
            .HasOne<ActionPlanningIncidentCategorization>(x => x.IncidentCategorization)
            .WithOne()
            .HasForeignKey<ActionPlanningAssociatedIncidentCategorization>(x => x.CategorizationId);

        modelBuilder.Entity<ActionPlanningResponsibleIndividual>()
            .HasOne<SafetyUser>(x => x.SafetyUser)
            .WithOne()
            .HasForeignKey<ActionPlanningResponsibleIndividual>(x => x.UserName);

        modelBuilder.Entity<ActionPlanningAssociatedIntervention>().HasKey(x => new { x.ReviewId, x.InterventionId });

        modelBuilder.Entity<ActionPlanningAssociatedIncidentCategorization>()
            .HasKey(x => new { x.ReviewId, x.CategorizationId });

        modelBuilder.Entity<ActionPlanningResponsibleIndividual>().HasKey(x => new {x.ReviewId, x.UserName});

        modelBuilder.Entity<ActionPlanningResponsibleDepartment>().HasKey(x => new {x.ReviewId, x.LocationName});

        modelBuilder.Entity<ActionPlanningSentDetails>().HasKey(x => new { x.ReviewId, x.UserName, x.SentAt });
        modelBuilder.Entity<ActionPlanningSentDetails>()
            .HasOne<SafetyUser>(x => x.SafetyUser)
            .WithOne()
            .HasForeignKey<ActionPlanningSentDetails>(x => x.UserName);

        modelBuilder.Entity<File>().HasOne(c => c.User).WithMany().HasForeignKey(c => c.UserName);
        modelBuilder.Entity<FileStatusLog>().Property(c => c.Action).HasConversion<int>();
        modelBuilder.Entity<FileStatusLog>().OwnsOne<DeleteReason>(p => p.DeleteReason, pa =>
        {
            pa.Property(c => c.Type).HasConversion<int>().HasColumnName("DeleteReason_Type");
            pa.Property(c => c.Description).HasColumnName("DeleteReason_Description");
        });

        modelBuilder.Entity<Link>().Property(c => c.LinkState).HasConversion(
            new ValueConverter<LinkState, bool>(
                v => v == LinkState.Primary,
                v => v ? LinkState.Primary : LinkState.Duplicate
            )
        );

        modelBuilder.Entity<IncidentLinkHistoryItem>().Property(c => c.Action).HasConversion(
            new ValueConverter<LinkAction, int>(
                v => (int)v,
                v => v == 0 ? LinkAction.Added : LinkAction.Removed
            )
        );
        modelBuilder.Entity<ReviewAttachment>().HasKey(c => new { c.ReviewId, c.FileId });
        modelBuilder.Entity<ReporterAttachment>().HasKey(c => new { c.IncidentId, c.FileId });

        modelBuilder.Entity<ReportedIncident>().HasMany<IncidentAssociatedLocation>(c => c.AssociatedLocations).WithOne()
            .HasForeignKey(c => c.IncidentId);
        modelBuilder.Entity<ReportedIncident>().Property(c => c.OccuredOn).HasConversion<string>(s => s.HasValue ? s.Value.ToString("yyyy-MM-ddTHH:mm:ss.fffZ") : null, d => DateTime.Parse(d.Replace("T", " ").Replace("Z", "")));
        modelBuilder.Entity<ReportedIncident>().HasOne<Link>(c => c.LinkState).WithOne()
            .HasForeignKey<Link>(c => c.IncidentId);

        modelBuilder
            .Entity<AuditTool>()
            .Property(a => a.NumOfDocumentsRange)
            .HasConversion(new EnumToStringConverter<DocumentRangeValidation>());

        modelBuilder
            .Entity<AuditTool>()
            .HasOne(a => a.Form)
            .WithOne()
            .HasForeignKey<AuditTool>(f => f.FormId);

        modelBuilder
            .Entity<AuditTool>()
            .HasMany(a => a.AuditToolColumns)
            .WithOne(c => c.AuditTool)
            .HasForeignKey(c => c.AuditId);

        modelBuilder
            .Entity<AuditTool>()
            .HasMany(a => a.AuditToolFilters)
            .WithOne(c => c.AuditTool)
            .HasForeignKey(c => c.AuditId);

        modelBuilder
            .Entity<AuditTool>()
            .HasMany(a => a.AuditToolExportColumns)
            .WithOne(c => c.AuditTool)
            .HasForeignKey(c => c.AuditId);

        #region String Converters

        modelBuilder.Entity<SafetyUser>().Property(c => c.DisplayName).HasConversion<TrimString>();

        #endregion

        #region ReadOnly Tables
        modelBuilder.Entity<SeverityScaleItem>().IsReadOnly();
        modelBuilder.Entity<SeverityScaleType>().IsReadOnly();
        modelBuilder.Entity<SeverityScaleItemCategory>().IsReadOnly();
        #endregion
    }

    protected override void ConfigureConventions(ModelConfigurationBuilder configBuilder)
    {
        configBuilder.Properties<UserName>()
            .HaveConversion<UserNameConverter>();
    }
}
