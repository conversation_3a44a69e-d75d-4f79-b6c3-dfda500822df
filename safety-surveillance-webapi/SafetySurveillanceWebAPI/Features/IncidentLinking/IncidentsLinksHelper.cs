#nullable enable
using SafetySurveillanceWebApi.Models.Database.Incidents;

namespace SafetySurveillanceWebApi.Features.IncidentLinking;

public static class IncidentsLinksHelper
{
    public static IncidentLinkingResult ConsolidateLinkChanges(List<Link> oldLinks, List<Link> newLinks)
    {
        var primaryChanged = oldLinks.GetPrimary()?.IncidentId != newLinks.GetPrimary()?.IncidentId;
        var oldPrimary = oldLinks.GetPrimary()?.IncidentId;
        var primary = newLinks.GetPrimary()?.IncidentId;

        List<int> added = new();
        List<int> removed = new();

        if (primaryChanged)
        {
            removed = oldLinks.Select(c => c.IncidentId).ToList();
            added = newLinks.Select(c => c.IncidentId).ToList();
        }
        else
        {
            removed = oldLinks
                .Select(c => c.IncidentId)
                .Except(newLinks.Select(c => c.IncidentId)).ToList();

            added = newLinks
                .Select(c => c.IncidentId)
                .Except(oldLinks.Select(c => c.IncidentId)).ToList();
        }

        removed.Where(c => c != oldPrimary).ToList().ForEach(i => Console.WriteLine($"{i} was unlinked from {oldPrimary}"));
        added.Where(c => c!= primary).ToList().ForEach(i => Console.WriteLine($"{i} was linked to {primary}"));


        return new IncidentLinkingResult() {
            RemoveListLog = removed,
            AddListLog = added,
            NewPrimary = primary,
            OldPrimary = oldPrimary,
            RemoveList = GetRemoveList(newLinks, removed, oldLinks),
            AddList = GetAddList(newLinks, added),
        };

    }

    private static List<int> GetAddList(List<Link> newLinks, List<int> added)
    {
        if (newLinks.Count <= 1) return new();
        return added;
    }

    private static List<int> GetRemoveList(List<Link> newLinks, List<int> removed, List<Link> oldLinks)
    {
        if (newLinks.Count != 1) return removed;
        if (oldLinks.Count == 0) return new();
        List<int> list = new List<int>(removed);
        list.Add(newLinks.First().IncidentId);
        return list;

    }

    public static Link? GetPrimary(this List<Link> links)
    {
        if(links.Count == 0) return null;
        return links.Single(c => c.IsPrimary);
    }

    public static List<int> GetDuplicates(this List<Link> links)
    {
        return links.Where(c => c.IsDuplicate).Select(c => c.IncidentId).ToList();
    }
}
