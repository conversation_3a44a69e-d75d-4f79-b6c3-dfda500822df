using System.IO;
using Microsoft.ApplicationInsights;
using Microsoft.ApplicationInsights.DataContracts;
using Microsoft.AspNetCore.Http;

namespace SafetySurveillanceWebApi.Infrastructure;

public class ResponseSizeMiddleware
{
    private readonly RequestDelegate next;
    private readonly TelemetryClient telemetryClient;

    public ResponseSizeMiddleware(RequestDelegate next, TelemetryClient telemetryClient)
    {
        this.next = next;
        this.telemetryClient = telemetryClient;
    }

    public async Task InvokeAsync(HttpContext context)
    {
        var originalBodyStream = context.Response.Body;

        using (var responseBody = new MemoryStream())
        {
            context.Response.Body = responseBody;

            await this.next(context);
            context.Response.Body.Seek(0, SeekOrigin.Begin);
            var responseSize = context.Response.Body.Length;

            if (context.Features.Get<RequestTelemetry>() is { } requestTelemetry)
            {
                requestTelemetry.Properties["ResponseSize"] = responseSize.ToString();
            }

            await responseBody.CopyToAsync(originalBodyStream);
        }
    }
}
