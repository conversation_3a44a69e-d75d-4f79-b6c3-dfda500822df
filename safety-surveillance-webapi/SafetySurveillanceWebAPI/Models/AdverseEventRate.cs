namespace SafetySurveillanceWebApi.Models;

public interface IXAxisItem<TData>
{
    string Category { get; set; }
    
    bool IsPartialDataSet { get; set; }
    
    List<TData> Data { get; set; }
}

public class CategoryChartXAxisItemAdverseEventCategoryCount
{
    public string AdverseEventCategory { get; set; }
    public int Count { get; set; }
}

public class ChartXAxisItem<T> : IXAxisItem<T>
{
    public bool IsPartialDataSet { get; set; }
    public string Category { get; set; }
    public List<T> Data { get; set; } = new List<T>();
}


public class AdverseEventRate
{
    public AdverseEventRate()
    {
        this.RateMultiplier = 1;
    }

    public string BreakOutCategory { get; set; }

    public double Numerator { get; set; }

    public double Denominator { get; set; }

    public string Category { get; set; }

    public double RateMultiplier { get; set; }

    public bool IsPartialDataSet { get; set; }

    public int SortOrder { get; set; }
    public int SeverityScaleTypeId { get; set; }

    public double Product
    {
        get
        {
            // ReSharper disable once CompareOfFloatsByEqualityOperator
            if (this.Denominator == 0)
            {
                return 0;
            }

            var val = (this.Numerator / this.Denominator) * this.RateMultiplier;

            return Math.Floor((100 * val) + .5) / 100;
        }
    }
}
