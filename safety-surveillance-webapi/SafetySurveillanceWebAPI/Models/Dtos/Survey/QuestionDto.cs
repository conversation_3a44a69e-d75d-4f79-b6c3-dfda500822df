namespace SafetySurveillanceWebApi.Models.Dtos.Survey;

using System.ComponentModel.DataAnnotations.Schema;

using SafetySurveillanceWebApi.Models.Constants;

public class QuestionDto
{
    public Guid QuestionId { get; set; }

    public Guid SectionId { get; set; }

    public string QuestionText { get; set; }

    public string Type { get; set; }

    public bool IsRequired { get; set; }

    public string HelpText { get; set; }

    public QuestionMultiSelectTypeOption MultiSelectType { get; set; }

    public List<QuestionOptionDto> Options { get; set; }

    public List<QuestionDto> SubQuestions { get; set; }

    public List<DecisionValueDto> DecisionValues { get; set; }

    public Guid? ParentQuestionId { get; set; }

    public Guid? ParentOptionId { get; set; }

    public string QuestionSubText { get; set; }
    
    public ICollection<QuestionHelpDataDto> QuestionHelpData { get; set; }
}
