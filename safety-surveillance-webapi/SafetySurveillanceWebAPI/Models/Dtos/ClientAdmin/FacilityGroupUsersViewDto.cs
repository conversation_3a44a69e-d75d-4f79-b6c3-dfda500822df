namespace SafetySurveillanceWebApi.Models.Dtos.ClientAdmin;

public class FacilityGroupUsersViewDto
{
    public string UserName { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    public List<FacilityGroupDto> FacilityGroups { get; set; }
    public List<string> Facilities { get; set; }
    public List<Guid> FacilityGroupIds { get; set; }
    public List<GroupRoleViewDto> Roles { get; set; }
    public List<Guid> RoleIds { get; set; }
    public DateTime UserCreateDate { get; set; }
    public string LastUpdatedBy { get; set; }
    public DateTime? LastUpdatedDateTime { get; set; }
    public DateTime? LastLoginDateTime { get; set; }
    public List<LocationLeadDto> Locations { get; set; }
    public List<CategoryLeadDto> Categories { get; set; }
}
