namespace SafetySurveillanceWebApi.Models.Dtos.Telemetry;

public class ClientConfigMiniDto
{
    public string Version { get; set; }

    public List<TriggerMiniDto> Triggers { get; set; }

    public List<FeatureFlagMiniDto> FeatureFlags { get; set; }

    public List<DeepDiveMiniDto> DeepDives { get; set; }

    public List<AuditToolMiniDto> AuditTools { get; set; }

    public ClientConfigMiniDto(ClientConfigDto config)
    {
        Version = "minimized";
        Triggers = config.Triggers.Select(t => new TriggerMiniDto(t)).ToList();
        FeatureFlags = config.FeatureFlags.Select(f => new FeatureFlagMiniDto(f)).ToList();
        DeepDives = config.DeepDives.Select(d => new DeepDiveMiniDto(d)).ToList();
        AuditTools = config.AuditTools.Select(a => new AuditToolMiniDto(a)).ToList();
    }

}
