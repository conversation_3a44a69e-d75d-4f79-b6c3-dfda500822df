namespace SafetySurveillanceWebApi.Models.Notification;

using System.ComponentModel.DataAnnotations.Schema;

[Table("TypePreferenceOptions", Schema = "Notification")]
public class NotificationTypePreferenceOption
{
    [Column("OptionId")]
    public int OptionId { get; set; }

    [Column("NotificationTypeID")]
    public int TypeId { get; set; }

    [Column("Order")]
    public int? Order { get; set; }

    [Column("OptionName")]
    public string OptionName { get; set; }

    [Column("DisplayText")]
    public string DisplayText { get; set; }

    [Column("SummaryText")]
    public string SummaryText { get; set; }
}