using System.Linq;
using Microsoft.VisualBasic;
using Newtonsoft.Json;
using SafetySurveillanceWebApi.Models.Authorize;
using SafetySurveillanceWebApi.Services.DataExport;

namespace SafetySurveillanceWebApi.Models;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using System.Text;
using System.Text.RegularExpressions;
using SafetySurveillanceWebApi.Extensions;
using SafetySurveillanceWebApi.Models.Constants;
using SafetySurveillanceWebApi.Models.ModelAttributes;
using SafetySurveillanceWebApi.Models.RootCauseAnalysis;
using ExportReport = SafetySurveillanceWebApi.Models.Constants.ExportReport;

[Table("TriggerEventReview", Schema = "SafetySurveillance")]
public class TriggerEventReview
{
    [CsvColumnName(
        Export = true,
        Name = "Review Id",
        Order = 1,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive})]
    [Key, Column("ID")]
    public int ReviewId { get; set; }

    [Column("Comment")] public string Comment { get; set; }

    [CsvColumnName(
        Export = true,
        Name = "Event",
        Order = 2,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive, ExportReport.AdverseEvents})]
    [Column("IsEvent")]
    public AdverseEventType IsAdverseEvent { get; set; }

    [Column("UserName")] public UserName UserName { get; set; }

    [Column("ReviewDTS")]
    [ObjectCompareIgnore]
    public DateTime? ReviewDts { get; set; }

    [CsvColumnName(
        Export = true,
        Name = "Event Date/Time",
        Order = 3,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive, ExportReport.AdverseEvents})]
    [Column("EventDTS")]
    public DateTime? AdverseEventDts { get; set; }

    [Column("TriggerDts")] public DateTime? TriggerDts { get; set; }

    [CsvColumnName(
        Export = true,
        Name = "Present On Admission",
        Order = 10,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive, ExportReport.AdverseEvents})]
    [Column("IsPresentOnAdmission")]
    public bool IsPresentOnAdmission { get; set; }

    [Column("EventCategoryId")] public int? AdverseEventCategoryId { get; set; }

    [ObjectCompareIgnore]
    [CsvColumnName(Export = true, Name = "Event Category", Order = 7, IsParent = true)]
    public AdverseEventCategoryReference AdverseEventCategoryReference { get; set; }

    [Column("EventSubCategoryId")] public int? AdverseEventSubCategoryId { get; set; }

    [ObjectCompareIgnore]
    [CsvColumnName(Export = true, Name = "Event SubCategory", Order = 8, IsParent = true,
        ExportReports = new[] {ExportReport.Positive, ExportReport.AdverseEvents})]
    [ForeignKey("AdverseEventSubCategoryId")]
    public AdverseEventSubCategoryReference AdverseEventSubCategoryReference { get; set; }

    [CsvColumnName(
        Export = true,
        Name = "Quality Improvement",
        Order = 12,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive, ExportReport.AdverseEvents})]
    [Column("IsQualityImprovement")]
    public bool IsQualityImprovement { get; set; }

    [CsvColumnName(
        Export = true,
        Name = "Regulatory Reporting",
        Order = 10,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive, ExportReport.AdverseEvents})]
    [Column("IsRegulatoryReportable")]
    public bool IsRegulatoryReportable { get; set; }

    [CsvColumnName(
        Export = true,
        Name = "Report To Risk",
        Order = 11,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive, ExportReport.AdverseEvents})]
    [Column("IsPotentialRisk")]
    public bool IsPotentialRisk { get; set; }

    [CsvColumnName(
        Export = true,
        Name = "Severity",
        Order = 9,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive, ExportReport.AdverseEvents})]
    [Column("Severity")]
    [Obsolete("Use ScaleItem.ItemName instead. This is for backward compatibility.")]
    public string Severity { get; set; }

    [ObjectCompareIgnore]
    [ForeignKey("Severity")]
    public AdverseEventSeverityReference AdverseEventSeverityReference { get; set; }

    [Column("EventLocation")] public string AdverseEventLocation { get; set; }

    [Column("OtherLocation")] public string OtherLocation { get; set; }

    [Obsolete("Unit is now a Multiselect, Table:  SafetySurveillance.AssociatedUnit")]
    [Column("EventDepartmentNM")]
    public string AdverseEventDepartment { get; set; }

    [Obsolete("ServiceLine is now a Multiselect, Table:  SafetySurveillance.AssociatedServiceLine")]
    [Column("EventServiceLine")]
    public string AdverseEventServiceLine { get; set; }

    [Column("IsDeleted")] public bool IsDeleted { get; set; }

    [CsvColumnName(
        Export = true,
        Name = "Event Type",
        Order = 14,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive, ExportReport.AdverseEvents})]
    [Column("EventType")]
    public string EventType { get; set; }

    [NotMapped]
    [ObjectCompareIgnore]
    [ForeignKey("TriggerReviewId")]
    public List<AssociatedProvider> AssociatedProviders { get; set; } = new List<AssociatedProvider>();

    [CsvColumnName(
        Export = true,
        Name = "Associated Providers",
        Order = 11,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive, ExportReport.AdverseEvents})]
    [Column("AssociatedProviders", Order = 15)]
    public string AssociatedProvidersJoinString
    {
        get
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < this.AssociatedProviders.Count; i++)
            {
                if (this.AssociatedProviders[i].Provider != null)
                {
                    sb.Append(this.AssociatedProviders[i].Provider.FullName);

                    if (i != this.AssociatedProviders.Count - 1)
                    {
                        sb.Append(";");
                    }
                }
            }

            return sb.ToString();
        }
    }

    [CsvColumnName(
        Export = true,
        Name = "Sentinel Event",
        Order = 15,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive})]
    [Column("IsSentinel")]
    public bool IsSentinel { get; set; }

    [CsvColumnName(
        Export = true,
        Name = "Send To Peer Review",
        Order = 16,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive})]
    [Column("IsPeerReviewSubmit")]
    public bool IsPeerReviewSubmit { get; set; }

    [NotMapped]
    [ObjectCompareIgnore]
    [ForeignKey("TriggerReviewId")]
    public List<ReviewToRootCauseOption> ReviewToRootCauseOptions { get; set; } = new List<ReviewToRootCauseOption>();

    [CsvColumnName(Export = true, Name = "Root Cause Analysis Options", Order = 51)]
    public string RootCauseOptionsJoinString
    {
        get
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < this.ReviewToRootCauseOptions.Count; i++)
            {
                if (this.ReviewToRootCauseOptions[i].Option != null)
                {
                    StringBuilder appender = new StringBuilder();

                    if (this.ReviewToRootCauseOptions[i].Option.Category != null)
                    {
                        appender.Append(this.ReviewToRootCauseOptions[i].Option.Category.Name);
                    }

                    appender.Append(": ");
                    if (this.ReviewToRootCauseOptions[i].Option.ParentOption?.ParentOption != null)
                    {
                        appender.Append(this.ReviewToRootCauseOptions[i].Option.ParentOption.ParentOption?.Name);
                        appender.Append(": ");
                    }

                    var rootCauseAnalysisOption = this.ReviewToRootCauseOptions[i].Option.ParentOption;
                    if (rootCauseAnalysisOption != null)
                    {
                        appender.Append(rootCauseAnalysisOption.Name);
                        appender.Append(": ");
                    }

                    appender.Append(this.ReviewToRootCauseOptions[i].Option.Name);

                    sb.Append(appender);

                    if (i != this.ReviewToRootCauseOptions.Count - 1)
                    {
                        sb.Append("; ");
                    }
                }
            }

            return sb.ToString();
        }
    }

    [CsvColumnName(
        Export = true,
        Name = "Review Description",
        Order = 4,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive, ExportReport.PhReviewed})]
    [NotMapped]
    [ObjectCompareIgnore]
    public string TextComment
    {
        get
        {
            this.Comment ??= string.Empty;
            return this.Comment.ScapeComment();
        }
    }

    [NotMapped] public List<TriggerEventInvestigationDetail> TriggerEventInvestigationDetails { get; set; }

    [NotMapped]
    [CsvColumnName(
        Export = true,
        Name = "Investigation Details",
        Order = 5,
        ExportSection = ExportSection.EventInformation)]
    public string TriggerEventInvestigationDetailsConcatenated
    {
        get
        {
            if (TriggerEventInvestigationDetails == null || TriggerEventInvestigationDetails.Count == 0)
            {
                return string.Empty;
            }

            var index = 0;
            var count = TriggerEventInvestigationDetails.Count;
            StringBuilder sb = new StringBuilder();
            sb.Append("\"");
            foreach (var triggerComment in this.TriggerEventInvestigationDetails)
            {
                sb.Append(
                    $"[{triggerComment.UserName} {triggerComment.CommentDts:MM/dd/yy HH:mm}]: {Strings.RTrim(triggerComment.Comment.ScapeComment())};");
                if (index < count - 1)
                {
                    sb.Append("\r\r");
                }

                index++;
            }

            sb.Append("\"");
            return sb.ToString();
        }
    }

    [NotMapped]
    [ObjectCompareIgnore]
    [ForeignKey("TriggerReviewId")]
    public List<AssociatedDevices> AssociatedDevices { get; set; } = new List<AssociatedDevices>();

    [CsvColumnName(
        Export = true,
        Name = "Associated Devices",
        Order = 15,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] {ExportReport.Positive, ExportReport.AdverseEvents})]
    [Column("AssociatedDevices", Order = 16)]
    public string AssociatedDevicesJoinString
    {
        get
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < this.AssociatedDevices.Count; i++)
            {
                if (this.AssociatedDevices[i].Device != null)
                {
                    sb.Append(this.AssociatedDevices[i].Device.DeviceDsc);

                    if (i != this.AssociatedDevices.Count - 1)
                    {
                        sb.Append(";");
                    }
                }
            }

            return sb.ToString();
        }
    }

    [ForeignKey("TriggerReviewId")]
    [ObjectCompareIgnore]
    public List<AssociatedUnit> AssociatedUnits { get; set; } = new List<AssociatedUnit>();

    [ForeignKey("TriggerReviewId")]
    [ObjectCompareIgnore]
    public List<AssociatedLocation> AssociatedLocations { get; set; } = new List<AssociatedLocation>();

    [CsvColumnName(Export = true, Name = "Associated Locations", Order = 15)]
    [Column("AssociatedUnits", Order = 16)]
    public string AssociatedUnitsJoinString
    {
        get
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < this.AssociatedUnits.Count; i++)
            {
                if (this.AssociatedUnits[i].Location != null)
                {
                    sb.Append(this.AssociatedUnits[i].Location);

                    if (i != this.AssociatedUnits.Count - 1)
                    {
                        sb.Append(";");
                    }
                }
            }

            if (!string.IsNullOrWhiteSpace(this.OtherLocation))
            {
                sb.Append(this.OtherLocation);
            }

            return sb.ToString();
        }
    }

    [ForeignKey("TriggerReviewId")]
    [ObjectCompareIgnore]
    public List<AssociatedServiceLine> AssociatedServiceLines { get; set; } = new List<AssociatedServiceLine>();

    [CsvColumnName(Export = true, Name = "Associated Service Lines", Order = 15)]
    [Column("AssociatedServiceLines", Order = 16)]
    public string AssociatedServiceLinesJoinString
    {
        get
        {
            StringBuilder sb = new StringBuilder();
            for (int i = 0; i < this.AssociatedServiceLines.Count; i++)
            {
                if (this.AssociatedServiceLines[i].ServiceLine != null)
                {
                    sb.Append(this.AssociatedServiceLines[i].ServiceLine);

                    if (i != this.AssociatedServiceLines.Count - 1)
                    {
                        sb.Append(";");
                    }
                }
            }

            return sb.ToString();
        }
    }


    /// <summary>
    /// This mirrors the documentation-detail form on the front end side.  This tells you that
    /// the form is not complete.  Keep them in sync
    /// </summary>
    [NotMapped]
    public bool IsComplete
    {
        get
        {
            if (this.IsAdverseEvent == AdverseEventType.EventDetected &&
                (!string.IsNullOrEmpty(this.Comment)
                 && !string.IsNullOrEmpty(this.ScaleItem?.ItemName)
                 && (!string.IsNullOrEmpty(this.OtherLocation) || this.AssociatedUnits.IsNotEmpty())
                 && this.AdverseEventCategoryId != null
                 && this.AdverseEventSubCategoryId != null
                 && this.AdverseEventDts != null))
            {
                return true;
            }

            return false;
        }
    }

    [NotMapped] public SeverityScaleItem ScaleItem { get; set; }

    [NotMapped] public string SeverityRating => this.ScaleItem?.ItemName;

    [CsvColumnName(Export = true, Name = GenerateColumnsService.SeverityScaleName, Order = 9)]
    [NotMapped]
    public string SeverityScaleType => this.ScaleItem?.ScaleType?.ScaleName;

    [NotMapped] public bool BelongsToIncident { get; set; }

    [Column("SentinelEventEmailSent")] public bool SentinelEventEmailSent { get; set; }

    [NotMapped]
    [CsvColumnName(
        Export = true,
        Name = "Action Planning Documentation",
        Order = 52,
        IsParent = true,
        ExportSection = ExportSection.ActionPlanning)]
    public ActionPlanning.ActionPlanning ActionPlanning { get; set; }

    [NotMapped]
    public virtual ICollection<Features.Formly.TriggerReviewFormData> TriggerReviewFormData { get; set; } = new List<Features.Formly.TriggerReviewFormData>();

    [CsvColumnName(
        Export = true,
        Name = "Formly Sub Form",
        Order = 53,
        ExportSection = ExportSection.EventInformation,
        ExportReports = new[] { ExportReport.Positive, ExportReport.AdverseEvents, ExportReport.IncidentReport })]
    [NotMapped]
    public string FormlySubFormData
    {
        get
        {
            if (TriggerReviewFormData?.Any() == true)
            {
                var formDataList = TriggerReviewFormData
                    .Where(trfd => !trfd.IsDeleted && trfd.FormData != null && !trfd.FormData.IsDeleted)
                    .Select(trfd => trfd.FormData.FormDataJson)
                    .Where(json => !string.IsNullOrEmpty(json))
                    .ToList();

                if (formDataList.Any())
                {
                    return string.Join("; ", formDataList);
                }
            }
            return string.Empty;
        }
    }
}
