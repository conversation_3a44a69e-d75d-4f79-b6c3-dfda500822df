namespace SafetySurveillanceWebApi.Models;

using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

[Table("DecisionValue", Schema = "Survey")]
public class DecisionValue
{
    [Key, Column("DecisionValueID")]
    public Guid DecisionValueId { get; set; }

    [Column("SourceQuestionID")]
    public Guid SourceQuestionId { get; set; }

    [Column("EnableQuestionID")]
    public Guid EnableQuestionId { get; set; }

    [Column("DecisionResponse")]
    public string DecisionResponse { get; set; }

    [Column("DecisionResponseID")]
    public Guid? DecisionResponseId { get; set; }

    [Column("AllOptionsEnableFlg")]
    public bool AllOptionsEnabled { get; set; }

    [Column("InvertEnable")]
    public bool InvertEnable { get; set; }

    [ForeignKey("SourceQuestionId")]
    public Question SourceQuestion { get; set; }

    [ForeignKey("EnableQuestionId")]
    public Question EnableQuestion { get; set; }
}