using System.ComponentModel.DataAnnotations.Schema;
using Newtonsoft.Json;

namespace SafetySurveillanceWebApi.Models;

[Table("SummarySignificantClinicalDataSurgery", Schema = "PatientSafetyTriggers")]
public class PatientSurgeryProcedures
{
    [Column("LogID")]
    public string LogId { get; set; }

    [Column("PatientEncounterID")]
    public string PatientEncounterId { get; set; }

    [Column("PatientEncounterSourceDSC")]
    public string PatientEncounterSourceDsc { get; set; }

    [Column("PrimaryProcedureNM")]
    public string PrimaryProcedureNm { get; set; }

    [Column("SurgeryStartDTS")]
    [JsonConverter(typeof(CustomDateTimeConverter))]
    public DateTime? SurgeryStartDts { get; set; }

    [Column("SurgeryEndDTS")]
    [JsonConverter(typeof(CustomDateTimeConverter))]
    public DateTime? SurgeryEndDts { get; set; }

    [Column("SurgeryMinuteDurationNBR")]
    public string SurgeryMinuteDurationNbr { get; set; }
}