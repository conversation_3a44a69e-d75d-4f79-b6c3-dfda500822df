namespace SafetySurveillanceWebApi.Models.Constants;

using SafetySurveillanceWebApi.Repositories;

public static class AuditConstants
{
    public static string[] ClabsiDataElements = new string[]
    {
        "Who is performing observation",
        "Who is being observed",
        "Location",
        "Line Type",
        "Laterality",
        "Current State of Dressing (eyes on patient)",
        "Dressing Change Required",
        "Dressing Change",
        "Line Securement (eyes on patient)",
        "Part of CLABSI Maintenance Bundle observed",
        "Daily Discussion About Lines (commonly performed during Huddle)",
        "Line Entry-Clean Gloves required for chemo, blood, products, or labs",
        "Needleless Connector Change Procedure",
        "Tubing Change Procedure - Non-Sterile Procedure (Tubing connected to needleless connector)",
        "Tubing Change Procedure - Sterile Procedure (Tubing connected directly to CVL hub - Tubing without needleless connector) (ex: art lines, CVP)",
        "ASK: During hand-off, shift report, clinic visit, encounter or team rounds, what topics should be discussed about central venous lines daily?",
        "ASK: Under what conditions should a dressing be changed?",
        "ASK: What are the steps you take prior to line entry?",
        "ASK: What PPE is required when performing a needleless connector change?"
    };

    public static string[] PressureInjuryDataElements = new string[]
    {
        "Skin assessment doc (once/shift)?",
        "Actual Braden/Braden Q",
        "Patient positioning",
        "Turn Q2?",
        "Turn per patient report?",
        "HOB<30 degrees non vented patients?",
        "Appropriate bed surface?",
        "Layer of linen",
        "Appropriate prophylactic dressing in place?",
        "Moisture management for diapered patients?",
        "End of Life?",
        "Stage?",
        "Location of Pressure Injury",
        "Device Related?",
        "Device Assessed/Rotated?",
        "Presence of Dressing?",
        "Date on Dressing?",
        "Condition of Dressing",
        "Devices of Concern",
        "Devices Associated to this Pressure Injury"
    };

    public static string[] CautiAuditElements = new string[]
    {
        "Who is performing observation",
        "Location",
        "Shift",
        "Order for Indwelling Urinary Catheter",
        "Insertion Documented",
        "Drain Type Documented",
        "Drain Size Documented",
        "Proper Securement",
        "Securement Method Used",
        "Assess Ongoing Need for Catheter - (Maintenance Day)",
        "Closed Drainage System - (Maintenance Day)",
        "Routine Periurethral and Perineal Cleansing - (Maintenance Day)",
        "Replacement of catheter and collection system if breaks in aseptic technique, disconnection, or leakage occur - (Maintenance Day)",
        "UA with culture ordered only when meeting facility diagnostic guidelines (to distinguish CAUTI from asymptomatic bacteriuria) - (Maintenance Day)",
        "Assess Ongoing Need for Catheter - (Maintenance Day)",
        "Closed Drainage System - (Maintenance Night)",
        "Routine Periurethral and Perineal Cleansing - (Maintenance Night)",
        "Replacement of catheter and collection system if breaks in aseptic technique, disconnection, or leakage occur - (Maintenance Night)",
        "UA with culture ordered only when meeting facility diagnostic guidelines (to distinguish CAUTI from asymptomatic bacteriuria) - (Maintenance Night)"
    };

    public static string[] IncidentElements = new string[]
    {
        "Where did this incident occur? (location of incident)",
        "Incident Type:",
        "Describe the incident in detail:"
    };
}