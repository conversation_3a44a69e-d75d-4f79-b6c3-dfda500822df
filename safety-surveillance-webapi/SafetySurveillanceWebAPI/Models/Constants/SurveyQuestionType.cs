namespace SafetySurveillanceWebApi.Models.Constants;

public class SurveyQuestionType
{
    public const string Dichotomous = "Dichotomous";
    public const string DichotomousWhy = "Dichotomous-Why";
    public const string DichotomousNA = "Dichotomous-NA";
    public const string DichotomousText = "Dichotomous-Text";
    public const string DichotomousNAText = "Dichotomous-NA-Text";

    public const string MultipleChoice = "Multiple-Choice";
    public const string MultipleChoiceText = "Multiple-Choice-Text";
    public const string MultipleChoiceNoneAbove = "Multiple-Choice-None-Above";

    public const string CheckboxList = "Check-box-list";
    public const string CheckboxListNoneAbove = "Check-box-list-none-above";
    public const string CheckboxListOther = "Check-box-list-other";

    public const string SingleValuePreSelect = "Single-Value-Pre-Select";
    public const string SingleValuePreRadio = "Single-Value-Pre-Radio";

    public const string Text = "Text";
    public const string MultiSelect = "Multi-Select";
    public const string MultiSelectText = "Multi-Select-Text";

    public const string DependantMultipleChoice = "Dependant-Multiple-Choice";
    public const string ComplexSubMultipleChoice = "Complex-Sub-Multiple-Choice";

    public const string SelectRadio = "Select-Radio";
    public const string SelectRadioWhy = "Select-Radio-Why";
    public const string Date = "Date";
    public const string CheckBox = "CheckBox";
    public const string TextArea = "Text-Area";

    public const string Wysiwyg = "WYSIWYG-Text";
    public const string MultipleDate = "Multiple-Date";
    public const string MulipleDateRange = "Multiple-Date-Range";
    public const string DateTime = "Date-Time";

    public const string MulipleChoiceTextDynamic = "Multiple-Choice-Text-Dynamic";
    public const string Person = "Patient";

}