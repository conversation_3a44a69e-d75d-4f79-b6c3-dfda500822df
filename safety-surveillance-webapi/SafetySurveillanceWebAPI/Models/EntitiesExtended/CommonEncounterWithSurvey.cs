using SafetySurveillanceWebApi.Models.Database.PSMSurvey;

namespace SafetySurveillanceWebApi.Models.EntitiesExtended;

using SafetySurveillanceWebApi.Models.Audit.Example;
using SafetySurveillanceWebApi.Models.Audit.HeparinAudit;
using SafetySurveillanceWebApi.Models.Audit.Mortality;
using SafetySurveillanceWebApi.Models.Audit.Restraints;
using SafetySurveillanceWebApi.Models.Common;
using SafetySurveillanceWebApi.Models.FebrileNeutropenia;

public class CommonEncounterWithSurvey
{
    public CommonEncounter Encounter { get; set; }

    public CommonAdt LatestAdts { get; set; }

    public CommonBradenScore Braden { get; set; }

    public List<AuditClabsi> Devices { get; set; }
        
    public List<AuditCauti> CautiDevices { get; set; }

    public List<SkinIntegrityEncounter> SkinIntegrityEncounters { get; set; }

    public MortalityAudit MortalityAudit { get; set; }

    public RestraintsAudit RestraintAudit { get; set; }

    public HeparinAudit HeparinAudit { get; set; }

    public ExampleAudit ExampleAudit { get; set; }
    public ReadmissionAudit ReadmissionAudit { get; set; }

    public AuditPressureInjury PressureInjuryAudit { get; set; }
}
