using SafetySurveillanceWebApi.Services.Filters.Filterables;

namespace SafetySurveillanceWebApi.Models;

using System.ComponentModel.DataAnnotations.Schema;

public class DeviceAssociatedHacPatientList : HacPatientList
{
    [Column("LineDrainAirwayID")]
    public string LineDrainAirwayId { get; set; }

    [Column("DeviceTypeID")]
    public string DeviceTypeId { get; set; }

    [Column("LatestDeviceDayRowNBR")]
    public int LatestDeviceDayRowNbr { get; set; }

    [Column("POALineFLG")]
    public int POALineFlg { get; set; }

    [Column("LengthOfStayHoursNBR")]
    public decimal? LengthOfStayHoursNbr { get; set; }

    [Column("PlacementDTS")]
    public DateTime? PlacementDts { get; set; }

    [Column("PlacementAdmitDTS")]
    public DateTime PlacementAdmitDts { get; set; }

    [Column("PlacementEncounterFLG")]
    public int PlacementEncounterFlg { get; set; }

    [Column("RemovalDTS")]
    public DateTime? RemovalDts { get; set; }

    [Column("RemovalDischargeDTS")]
    public DateTime? RemovalDischargeDts { get; set; }
}