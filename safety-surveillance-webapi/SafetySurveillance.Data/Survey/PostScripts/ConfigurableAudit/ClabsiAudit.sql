GO 
DECLARE @AuditId UNIQUEIDENTIFIER = '348233c9-cc60-49d4-b72c-f0e335f1d928'
DECLARE @ClabsiFormId uniqueidentifier = 'DD438C2D-AA1B-479C-9E1F-B4577421B591'
declare @CreatedBy varchar(100) = 'Health Catalyst'

IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditTool] where Id = @AuditId))
BEGIN
    insert into [Survey].[AuditTool] (
        Id,
        Name,
        GroupedColumnName,
        NumOfDocuments,
        NumOfDocumentsRange,
        FormId,
        CountByGroup,
        CountLabelName,
        CreatedDTS,
        CreatedByTXT,
        IsActiveFLG
    ) values (
            @AuditId,
            'Clabsi Audit',
            'Line',
            0, --infinite
            null,
            @ClabsiFormId,
            0,
            'Line Count',
            sysdatetime(),
            @CreatedBy,
            1
    );
END
/*
    Patient Details Columns
*/
-- Patient Column
declare @patientColumnId uniqueidentifier = 'd0966656-b0fb-49b6-9292-896ecd4c82f8'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @patientColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @patientColumnId, @AuditId, 'PatientFullName', 'Patient', 1, 'text', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'CONCAT(PatientLastNM, '', '', PatientFirstNM)', null, sysdatetime(),
            @CreatedBy, 1, 1, 1
        )
    END

-- MRN
declare @mrnColumnId uniqueidentifier = '1412188b-c4d7-47f5-a041-2fbb09ff2828'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @mrnColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @mrnColumnId, @AuditId, 'Mrn', 'MRN', 1, 'text', 0,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'MRN', null, sysdatetime(),
            @CreatedBy, 0, 0, 0
        )
    END

-- Visit Number
declare @visitNumberColumnId uniqueidentifier = 'd95793d8-5b09-4231-8e60-94150ff1a011'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @visitNumberColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @visitNumberColumnId, @AuditId, 'VisitNumber', 'Visit#', 1, 'text', 0,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'FacilityAccountID', null, sysdatetime(),
            @CreatedBy, 0, 0, 0
        )
    END

-- Unit
declare @unitColumnId uniqueidentifier = 'ea376084-a17f-416c-a61a-b98dc287364b'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @unitColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @unitColumnId, @AuditId, 'Unit', 'Unit', 2, 'text', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'DepartmentNM', null, sysdatetime(),
            @CreatedBy, 1, 1, 1
        )
    END

-- Room
declare @roomColumnId uniqueidentifier = 'bd9fead5-6c4d-457f-bd38-ae8eb07ae6d2'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @roomColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @roomColumnId, @AuditId, 'Room', 'Room', 3, 'text', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'IsNull(RoomNM, ''Unknown'')', null, sysdatetime(),
            @CreatedBy, 1, 1, 1
        )
    END

-- Age
declare @ageColumnId uniqueidentifier = 'edc903f2-3c4c-41bf-ab56-2f14a83d5955'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @ageColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @ageColumnId, @AuditId, 'AgeInMonths', 'Age', 4, 'Age', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'DATEDIFF(month, BirthDTS, AdmitDTS)', 'yrs', sysdatetime(),
            @CreatedBy, 1, 1, 1
        )
    END

-- LOS
declare @LosColumnId uniqueidentifier = '409e9577-1349-46d9-bc37-386e89f44525'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @LosColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @LosColumnId, @AuditId, 'LengthOfStay', 'LOS', 5, 'text', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'DATEDIFF(day, AdmitDTS, ISNULL(DischargeDTS, sysdatetime()))', 'days', sysdatetime(),
            @CreatedBy, 1, 1, 1
        )
    END

-- Line
declare @CatheterTypeColumnId uniqueidentifier = 'b48d6f15-737d-4c8c-82b6-562e81b67f87'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @CatheterTypeColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @CatheterTypeColumnId, @AuditId, 'Line', 'Line', 6, 'text', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'CONCAT(DeviceTypeDSC, '' '', LateralityDSC, '' '', SiteCD, '' '', DeviceID)', null, sysdatetime(),
            @CreatedBy, 0, 1, 1
        )
    END

-- Audit
declare @auditColumnId uniqueidentifier = '230e954c-438f-49fe-80d2-d37ffd43ba02'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @auditColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @auditColumnId, @AuditId, 'Action', 'Audit', 7, 'Action', 10,
            'asc', NULL, NULL, NULL, 'Document >', sysdatetime(),
            @CreatedBy, 0, 0, 1
        )
    END

-- Status
declare @statusColumnId uniqueidentifier = '7ab69e7a-2094-4ae0-bfe1-a6c9bf1d41e0'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @statusColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @statusColumnId, @AuditId, 'Status', 'Status', 8, 'Text', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'StatusDSC', NULL, sysdatetime(),
            @CreatedBy, 0, 0, 1
        )
    END

-- Assignee
declare @AssigneeColumnId uniqueidentifier = '9bce53e8-fd8d-40a6-b36c-7abb6ddb2d53'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @AssigneeColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @AssigneeColumnId, @AuditId, 'Assignee', 'Assignee', 9, 'Text', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'AssignedToUser', NULL, sysdatetime(),
            @CreatedBy, 0, 0, 1
        )
    END
    
-- Assignee UserName
declare @AssigneeUserNameColumnId uniqueidentifier = 'f7e65abc-613d-4d47-93fa-76a9ad31b7c3'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @AssigneeUserNameColumnId))
BEGIN
insert into [Survey].[AuditToolColumn] (
  Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
  SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
  CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
) values (
  @AssigneeUserNameColumnId, @AuditId, 'AssigneeUserName', 'Assignee UserNAme', 11, 'Text', 10,
  'asc', 'Survey', 'ClabsiAuditWithEncounter', 'AssignedToUserName', NULL, sysdatetime(),
  @CreatedBy, 0, 0, 0
  )
END

-- Updated
declare @UpdatedColumnId uniqueidentifier = 'f72af6f7-6fb0-4c77-aba8-ad0c60899908'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @UpdatedColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @UpdatedColumnId, @AuditId, 'Updated', 'Updated', 10, 'Date', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'Updated', NULL, sysdatetime(),
            @CreatedBy, 0, 0, 1
        )
    END

-- Facility
declare @FacilityColumnId uniqueidentifier = '0882e437-2f1a-4f94-9471-55a4dfb9489b'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @FacilityColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @FacilityColumnId, @AuditId, 'Facility', 'Facility', 11, 'Text', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'Facility', NULL, sysdatetime(),
            @CreatedBy, 0, 0, 0
        )
    END

-- Service Line
declare @ServiceLineColumnId uniqueidentifier = '46042d64-0d6d-4fa3-acbb-850cf4b37fa0'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @ServiceLineColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @ServiceLineColumnId, @AuditId, 'ServiceLine', 'Service Line', 12, 'Text', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'PrimaryServiceLineNM', NULL, sysdatetime(),
            @CreatedBy, 0, 0, 0
        )
    END

-- SurveyEventId
declare @SurveyEventIdColumnId uniqueidentifier = '811f65a9-3142-4702-b414-2533853fbc3d'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @SurveyEventIdColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @SurveyEventIdColumnId, @AuditId, 'SurveyEventId', 'SurveyEventId', 13, 'Text', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'SurveyEventId', NULL, sysdatetime(),
            @CreatedBy, 0, 0, 0
        )
    END

-- EncounterId
declare @EncounterIdColumnId uniqueidentifier = '10f85abf-3213-4162-a183-964ccce5959d'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @EncounterIdColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @EncounterIdColumnId, @AuditId, 'EncounterId', 'EncounterId', 14, 'Text', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'EncounterID', NULL, sysdatetime(),
            @CreatedBy, 0, 0, 0
        )
    END

-- EncounterSource
declare @EncounterSrcColumnId uniqueidentifier = '10e0da00-afc9-471d-9a7e-0e84bcee5aee'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolColumn] where Id = @EncounterSrcColumnId))
    BEGIN
        insert into [Survey].[AuditToolColumn] (
            Id, AuditId, ColumnName, DisplayName, DisplayOrder, DataType, WidthPercentage,
            SortDirection, SchemaName, TableName, SelectColumnName, Suffix, CreatedDTS,
            CreatedByTXT, IsSortable, IsSearchable, IsActiveFLG
        ) values (
            @EncounterSrcColumnId, @AuditId, 'EncounterSrc', 'EncounterSrc', 15, 'Text', 10,
            'asc', 'Survey', 'ClabsiAuditWithEncounter', 'EncounterSourceDSC', NULL, sysdatetime(),
            @CreatedBy, 0, 0, 0
        )
    END

/*
    Data Export Columns ==================================================================================================
*/
-- Patient Column
declare @patientNameExportColumnId uniqueidentifier = '33c6a07b-ecc6-4832-a589-604ce4594da6'
  IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @patientNameExportColumnId))
  BEGIN
  insert into [Survey].[AuditToolExport] (
    Id, AuditId, ColumnName, DisplayName, DataType, Section,
    DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
  ) values (
    @patientNameExportColumnId, @AuditId, 'PatientFullName', 'Patient Name', 'Text', 'Demographics Information',
    1, 'Survey', 'CautiAuditWithEncounter', 'CONCAT(PatientLastNM, '', '', PatientFirstNM)', sysdatetime(), @CreatedBy
    )
  END

-- MRN Column
declare @MrnExportColumnId uniqueidentifier = 'a43c8219-4fbd-4bae-9d8f-3e8f4d062582'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @MrnExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @MrnExportColumnId, @AuditId, 'Mrn', 'MRN', 'Text', 'Demographics Information',
            2, 'Survey', 'ClabsiAuditWithEncounter', 'MRN', sysdatetime(), @CreatedBy
        )
    END

-- Gender Column
declare @GenderExportColumnId uniqueidentifier = 'd44779ba-64ad-4027-a634-56a8aae7a95c'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @GenderExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @GenderExportColumnId, @AuditId, 'Gender', 'Gender', 'Text', 'Demographics Information',
            3, 'Survey', 'ClabsiAuditWithEncounter', 'Gender', sysdatetime(), @CreatedBy
        )
    END

-- Date of Birth Column
declare @DateOfBirthExportColumnId uniqueidentifier = '3017b0f0-5e39-4ca1-afaa-4b343c3dfa08'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @DateOfBirthExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @DateOfBirthExportColumnId, @AuditId, 'BirthDTS', 'Date Of Birth', 'Date', 'Demographics Information',
            4, 'Survey', 'ClabsiAuditWithEncounter', 'BirthDTS', sysdatetime(), @CreatedBy
        )
    END

-- Patient Class Column
declare @PatientClassExportColumnId uniqueidentifier = 'a1f499e5-fa60-4792-be2b-c58128ea4321'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @PatientClassExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @PatientClassExportColumnId, @AuditId, 'PatientClassDsc', 'Patient Class', 'Text', 'Demographics Information',
            5, 'Survey', 'ClabsiAuditWithEncounter', 'PatientClassDsc', sysdatetime(), @CreatedBy
        )
    END

-- Visit Number Column
declare @VisitNumberExportColumnId uniqueidentifier = '45c876d6-7167-453a-8e65-7eb852161de3'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @VisitNumberExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @VisitNumberExportColumnId, @AuditId, 'FacilityAccountID', 'Visit Number', 'Text', 'Encounter Information',
            1, 'Survey', 'ClabsiAuditWithEncounter', 'FacilityAccountID', sysdatetime(), @CreatedBy
        )
    END

-- Admit Date/Time Column
declare @AdmitDateExportColumnId uniqueidentifier = '7c71ce20-bf51-4800-8b93-df30cfc5c3cc'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @AdmitDateExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @AdmitDateExportColumnId, @AuditId, 'InpatientAdmitDTS', 'Admit Date/Time', 'Date', 'Encounter Information',
            2, 'Survey', 'ClabsiAuditWithEncounter', 'InpatientAdmitDTS', sysdatetime(), @CreatedBy
        )
    END

-- Discharge Date/Time Column
declare @DischargeDateExportColumnId uniqueidentifier = '4cbd293e-650d-4328-b529-7cc03e30bf4c'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @DischargeDateExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @DischargeDateExportColumnId, @AuditId, 'DischargeDTS', 'Discharge Date/Time', 'Date', 'Encounter Information',
            3, 'Survey', 'ClabsiAuditWithEncounter', 'DischargeDTS', sysdatetime(), @CreatedBy
        )
    END

-- Inpatient Date/Time Column
declare @InpatientDateExportColumnId uniqueidentifier = '398eaa70-2eb3-4781-bf50-d9bb084bf4f6'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @InpatientDateExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @InpatientDateExportColumnId, @AuditId, 'InpatientAdmitDTS', 'Inpatient Date/Time', 'Date', 'Encounter Information',
            4, 'Survey', 'ClabsiAuditWithEncounter', 'InpatientAdmitDTS', sysdatetime(), @CreatedBy
        )
    END

-- Admit Source Column
declare @AdmitSourceExportColumnId uniqueidentifier = '7b43f0d5-f496-463b-838f-dc48f5a9588c'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @AdmitSourceExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @AdmitSourceExportColumnId, @AuditId, 'AdmitSourceDSC', 'Admit Source', 'Text', 'Encounter Information',
            5, 'Survey', 'ClabsiAuditWithEncounter', 'AdmitSourceDSC', sysdatetime(), @CreatedBy
        )
    END

-- Admit Reason Column
declare @AdmitReasonExportColumnId uniqueidentifier = '8a62318c-51b8-452a-9713-98a1f5417389'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @AdmitReasonExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @AdmitReasonExportColumnId, @AuditId, 'ReasonForBedDSC', 'Admit Reason', 'Text', 'Encounter Information',
            6, 'Survey', 'ClabsiAuditWithEncounter', 'ReasonForBedDSC', sysdatetime(), @CreatedBy
        )
    END

-- Primary Diagnosis Code Column
declare @PrimaryDiagnosisCodeExportColumnId uniqueidentifier = 'ce7252bd-a102-41ef-888e-73b6a6cccb23'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @PrimaryDiagnosisCodeExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @PrimaryDiagnosisCodeExportColumnId, @AuditId, 'PrimaryDiagnosisCD', 'Primary Diagnosis Code', 'Text', 'Encounter Information',
            7, 'Survey', 'ClabsiAuditWithEncounter', 'PrimaryDiagnosisCD', sysdatetime(), @CreatedBy
        )
    END

-- Primary Diagnosis Description Column
declare @PrimaryDiagnosisDscExportColumnId uniqueidentifier = 'ad5c4dbc-2cc4-4811-a6a5-c38067667770'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @PrimaryDiagnosisDscExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @PrimaryDiagnosisDscExportColumnId, @AuditId, 'PrimaryDiagnosisDSC', 'Primary Diagnosis Description', 'Text', 'Encounter Information',
            8, 'Survey', 'ClabsiAuditWithEncounter', 'PrimaryDiagnosisDSC', sysdatetime(), @CreatedBy
        )
    END

-- Service Column
declare @ServiceExportColumnId uniqueidentifier = '85c69ca9-be6b-447a-ae26-1831474953fd'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @ServiceExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @ServiceExportColumnId, @AuditId, 'PrimaryServiceLineNM', 'Service', 'Text', 'Encounter Information',
            9, 'Survey', 'ClabsiAuditWithEncounter', 'PrimaryServiceLineNM', sysdatetime(), @CreatedBy
        )
    END

-- Attending Provider Column
declare @AttendingProviderExportColumnId uniqueidentifier = '5e369dce-19ba-4eb9-9a74-6f4af70714ec'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @AttendingProviderExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @AttendingProviderExportColumnId, @AuditId, 'AttendingProviderFullNM', 'Attending Provider', 'Text', 'Encounter Information',
            10, 'Survey', 'ClabsiAuditWithEncounter', 'AttendingProviderFullNM', sysdatetime(), @CreatedBy
        )
    END

-- Discharge Disposition Description Column
declare @DischargeDispositionExportColumnId uniqueidentifier = '2ec50a93-8757-4a52-b77c-dfc8c5edc987'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @DischargeDispositionExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @DischargeDispositionExportColumnId, @AuditId, 'DischargeDispositionDSC', 'Discharge Disposition Description', 'Text', 'Encounter Information',
            11, 'Survey', 'ClabsiAuditWithEncounter', 'DischargeDispositionDSC', sysdatetime(), @CreatedBy
        )
    END

-- Facility Name Column
declare @FacilityExportColumnId uniqueidentifier = 'f09b9519-84e0-4b32-a027-cf8e9ce9512a'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @FacilityExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @FacilityExportColumnId, @AuditId, 'DischargeLocationNM', 'Facility Name', 'Text', 'Encounter Information',
            12, 'Survey', 'ClabsiAuditWithEncounter', 'Facility', sysdatetime(), @CreatedBy
        )
    END

-- Length of Stay Column
declare @LOSExportColumnId uniqueidentifier = '71dd9ac2-562a-4f23-a6e6-e413c85804ed'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolExport] where Id = @LOSExportColumnId))
    BEGIN
        insert into [Survey].[AuditToolExport] (
            Id, AuditId, ColumnName, DisplayName, DataType, Section,
            DisplayOrder, SchemaName, TableName, SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @LOSExportColumnId, @AuditId, 'LengthOfStay',
            'Length of Stay', 'Text', 'Encounter Information',
            13, 'Survey', 'ClabsiAuditWithEncounter', 'DATEDIFF(day, AdmitDTS, ISNULL(DischargeDTS, sysdatetime()))', sysdatetime(), @CreatedBy
        )
    END


-- Create Filters
declare @FacilityFilterId uniqueidentifier = '8061dd42-f632-431b-8f0c-0a8dd59f8fc3'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolFilter] where Id = @FacilityFilterId))
    BEGIN
        insert into [Survey].[AuditToolFilter] (
            Id, AuditId, Name, FilterClass,
            FilterCriteria, FilterCriteriaPropertyName,
            CreatedDTS, CreatedByTXT
        ) values (
            @FacilityFilterId, @AuditId, 'Facility', 'Facility',
            'CriteriaFacility', 'Facility',
            sysdatetime(), @CreatedBy
        )
    END

declare @UnitFilterId uniqueidentifier = '33de6f66-8a78-42b7-956a-464c691e6acc'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolFilter] where Id = @UnitFilterId))
    BEGIN
        insert into [Survey].[AuditToolFilter] (
            Id, AuditId, Name, FilterClass,
            FilterCriteria, FilterCriteriaPropertyName,
            CreatedDTS, CreatedByTXT
        ) values (
            @UnitFilterId, @AuditId, 'Unit', 'Unit',
            'CriteriaUnit', 'Unit',
            sysdatetime(), @CreatedBy
        )
    END

declare @ServiceLineFilterId uniqueidentifier = 'cf81d467-8557-4915-b31d-fa96aa0f8339'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolFilter] where Id = @ServiceLineFilterId))
    BEGIN
        insert into [Survey].[AuditToolFilter] (
            Id, AuditId, Name, FilterClass,
            FilterCriteria, FilterCriteriaPropertyName,
            CreatedDTS, CreatedByTXT
        ) values (
            @ServiceLineFilterId, @AuditId, 'Service Line', 'ServiceLine',
            'CriteriaServiceLine', 'ServiceLine',
            sysdatetime(), @CreatedBy
        )
    END

declare @AssigneeFilterId uniqueidentifier = '5efe8b56-bf18-441d-b765-440ad996f8bf'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolFilter] where Id = @AssigneeFilterId))
    BEGIN
        insert into [Survey].[AuditToolFilter] (
            Id, AuditId, Name, FilterClass,
            FilterCriteria, FilterCriteriaPropertyName,
            CreatedDTS, CreatedByTXT
        ) values (
            @AssigneeFilterId, @AuditId, 'Assignee', 'Assignee',
            'CriteriaAssignee', 'AssigneeUserName',
            sysdatetime(), @CreatedBy
        )
    END

declare @StatusFilterId uniqueidentifier = 'ce95495c-f288-4428-92dd-5e7b45617df1'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolFilter] where Id = @StatusFilterId))
    BEGIN
        insert into [Survey].[AuditToolFilter] (
            Id, AuditId, Name, FilterClass,
            FilterCriteria, FilterCriteriaPropertyName,
            CreatedDTS, CreatedByTXT
        ) values (
            @StatusFilterId, @AuditId, 'Status', 'Status',
            'CriteriaStatus', 'Status',
            sysdatetime(), @CreatedBy
        )
    END

/*
    Patient Details Columns ==================================================================================================
*/
declare @PatientDetailPatientColumnId uniqueidentifier = 'd2cc8850-e0ac-4aab-bdb0-c44f2344544d'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @PatientDetailPatientColumnId))
    BEGIN
        insert into [Survey].[AuditToolPatientDetail] (
            Id, AuditId, ColumnName, DisplayName,
            DisplayOrder, DataType, SchemaName, TableName,
            SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @PatientDetailPatientColumnId, @AuditId, 'PatientFullName', 'Patient',
            1, 'Text', 'Survey', 'ClabsiAuditWithEncounter',
            'CONCAT(PatientLastNM, '', '', PatientFirstNM)', SYSDATETIME(), @CreatedBy
        )
    END

declare @VisitNumberPatientDetailColumnId uniqueidentifier = '7c43880e-ebcf-4c41-bba2-eb4f35014aab'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @VisitNumberPatientDetailColumnId))
    BEGIN
        insert into [Survey].[AuditToolPatientDetail] (
            Id, AuditId, ColumnName, DisplayName,
            DisplayOrder, DataType, SchemaName, TableName,
            SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @VisitNumberPatientDetailColumnId, @AuditId, 'VisitNumber', 'Visit #',
            2, 'Text', 'Survey', 'ClabsiAuditWithEncounter',
            'FacilityAccountID', SYSDATETIME(), @CreatedBy
        )
    END

declare @MrnPatientDetailColumnId uniqueidentifier = '36a1e917-c463-4903-95b0-70d78a3e5856'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @MrnPatientDetailColumnId))
    BEGIN
        insert into [Survey].[AuditToolPatientDetail] (
            Id, AuditId, ColumnName, DisplayName,
            DisplayOrder, DataType, SchemaName, TableName,
            SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @MrnPatientDetailColumnId, @AuditId, 'Mrn', 'Mrn',
            3, 'Text', 'Survey', 'ClabsiAuditWithEncounter',
            'MRN', SYSDATETIME(), @CreatedBy
        )
    END

declare @AgePatientDetailColumnId uniqueidentifier = '0e1b1718-dc5d-4e63-b23f-94f1b4b277fe'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @AgePatientDetailColumnId))
    BEGIN
        insert into [Survey].[AuditToolPatientDetail] (
            Id, AuditId, ColumnName, DisplayName,
            DisplayOrder, DataType, SchemaName, TableName,
            SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @AgePatientDetailColumnId, @AuditId, 'AgeInMonths', 'Age',
            4, 'Age', 'Survey', 'ClabsiAuditWithEncounter',
            'DATEDIFF(month, BirthDTS, AdmitDTS)', SYSDATETIME(), @CreatedBy
        )
    END

declare @GenderPatientDetailColumnId uniqueidentifier = 'c6e02b8e-5e5e-4df0-96a7-289f9c62364a'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @GenderPatientDetailColumnId))
    BEGIN
        insert into [Survey].[AuditToolPatientDetail] (
            Id, AuditId, ColumnName, DisplayName,
            DisplayOrder, DataType, SchemaName, TableName,
            SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @GenderPatientDetailColumnId, @AuditId, 'Gender', 'Gender',
            5, 'Text', 'Survey', 'ClabsiAuditWithEncounter',
            'Gender', SYSDATETIME(), @CreatedBy
        )
    END

declare @AdmitDatePatientDetailColumnId uniqueidentifier = 'cad6d599-a352-4f24-9af6-2f00c643a06a'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @AdmitDatePatientDetailColumnId))
    BEGIN
        insert into [Survey].[AuditToolPatientDetail] (
            Id, AuditId, ColumnName, DisplayName,
            DisplayOrder, DataType, SchemaName, TableName,
            SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @AdmitDatePatientDetailColumnId, @AuditId, 'AdmitDate', 'Hospital Admit Date',
            6, 'Date', 'Survey', 'ClabsiAuditWithEncounter',
            'AdmitDTS', SYSDATETIME(), @CreatedBy
        )
    END

declare @InpatientAdmitDatePatientDetailColumnId uniqueidentifier = '9fd0f669-49ce-4b09-8df1-797de3bef0c7'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @InpatientAdmitDatePatientDetailColumnId))
    BEGIN
        insert into [Survey].[AuditToolPatientDetail] (
            Id, AuditId, ColumnName, DisplayName,
            DisplayOrder, DataType, SchemaName, TableName,
            SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @InpatientAdmitDatePatientDetailColumnId, @AuditId, 'InpatientAdmitDate', 'Inpatient Admit Date',
            7, 'Date', 'Survey', 'ClabsiAuditWithEncounter',
            'InpatientAdmitDTS', SYSDATETIME(), @CreatedBy
        )
    END

declare @UnitPatientDetailColumnId uniqueidentifier = '0501f876-4663-43e4-a000-a879f746c604'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @UnitPatientDetailColumnId))
    BEGIN
        insert into [Survey].[AuditToolPatientDetail] (
            Id, AuditId, ColumnName, DisplayName,
            DisplayOrder, DataType, SchemaName, TableName,
            SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @UnitPatientDetailColumnId, @AuditId, 'Unit', 'Unit',
            8, 'Text', 'Survey', 'ClabsiAuditWithEncounter',
            'DepartmentNM', SYSDATETIME(), @CreatedBy
        )
    END

declare @RoomPatientDetailColumnId uniqueidentifier = 'b00dde21-bdb1-4118-915e-4133a5f9a733'
IF (NOT EXISTS(SELECT Id FROM [Survey].[AuditToolPatientDetail] where Id = @RoomPatientDetailColumnId))
    BEGIN
        insert into [Survey].[AuditToolPatientDetail] (
            Id, AuditId, ColumnName, DisplayName,
            DisplayOrder, DataType, SchemaName, TableName,
            SelectColumnName, CreatedDTS, CreatedByTXT
        ) values (
            @RoomPatientDetailColumnId, @AuditId, 'Room', 'Room',
            9, 'Text', 'Survey', 'ClabsiAuditWithEncounter',
            'IsNull(RoomNM, ''Unknown'')', SYSDATETIME(), @CreatedBy
        )
    END
GO
