IF (EXISTS(SELECT * FROM [Survey].[Question]
WHERE FormID = '{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}' 
AND  QuestionID = '{BD253240-7AA7-4CA0-8CF9-6B949D53AC30}' 
AND  [QuestionTXT] = 'Would you like to report this incident anonymously'))
BEGIN
    UPDATE [Survey].[Question]
    SET  [QuestionTXT] = 'Would you like to report this incident anonymously?'
    WHERE QuestionId = '{BD253240-7AA7-4CA0-8CF9-6B949D53AC30}'
END

IF (EXISTS(SELECT * FROM [Survey].[Question]
WHERE FormID = '{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}'
AND  QuestionID = '{247957FB-4BD0-4AD9-9CAB-E579A01F216C}' 
AND  [QuestionTXT] in ('Patient Information', 'Patient Information:')))
BEGIN
    UPDATE [Survey].[Question]
    SET  [QuestionTXT] = 'Which individual would you like to associate this incident to?'
    WHERE QuestionId = '{247957FB-4BD0-4AD9-9CAB-E579A01F216C}'
END

IF (EXISTS(SELECT * FROM [Survey].[Question]
WHERE FormID = '{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}'
AND  QuestionID = '{D0E6393E-C305-4C35-A5B0-535AA2A71962}' 
AND  [QuestionTXT] = 'When did this incident occur'))
BEGIN
    UPDATE [Survey].[Question]
    SET  [QuestionTXT] = 'When did this incident occur?'
    WHERE QuestionId = '{D0E6393E-C305-4C35-A5B0-535AA2A71962}'
END

IF (EXISTS(SELECT * FROM [Survey].[Question] 
WHERE FormID = '{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}'
AND  QuestionID = '{5AEB1CD5-FBB8-4F54-B090-BDB69B3072BE}' 
AND  [QuestionTXT] = 'Where did this incident occur (location of incident)'))
BEGIN
    UPDATE [Survey].[Question]
    SET  [QuestionTXT] = 'Where did this incident occur? (location of incident)'
    WHERE QuestionId = '{5AEB1CD5-FBB8-4F54-B090-BDB69B3072BE}'
END

IF (EXISTS(SELECT * FROM [Survey].[Question]
WHERE FormID = '{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}'
AND  QuestionID = '{C585A755-C3AC-4DDC-A888-88DBCE2BBC32}' 
AND  [QuestionTXT] = 'Incident Type'))
BEGIN
    UPDATE [Survey].[Question]
    SET  [QuestionTXT] = 'Incident Type:'
    WHERE QuestionId = '{C585A755-C3AC-4DDC-A888-88DBCE2BBC32}'
END

IF (EXISTS(SELECT * FROM [Survey].[Question]
WHERE FormID = '{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}'
AND  QuestionID = '{61A92ADF-F122-4E42-8D23-5F305E5FF699}' 
AND  [QuestionTXT] = 'Describe the incident in detail'))
BEGIN
    UPDATE [Survey].[Question]
    SET  [QuestionTXT] = 'Describe the incident in detail:'
    WHERE QuestionID = '{61A92ADF-F122-4E42-8D23-5F305E5FF699}'
END

IF (EXISTS(SELECT * FROM [Survey].[Section]
WHERE FormID = '{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}'
    AND SectionID ='{8C7EACE7-0148-49F8-A820-09E3963CB6C7}'
    AND SectionNM = 'Patient Information'))
BEGIN
    UPDATE [Survey].[Section]
    SET SectionNM = 'Affected Individual'
    WHERE SectionID ='{8C7EACE7-0148-49F8-A820-09E3963CB6C7}'
END


-- WHLA data fix
IF (EXISTS(SELECT * FROM [Survey].[Question]
WHERE FormID = '{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}'
    AND QuestionID ='{D280562C-25F3-471A-8BDD-601533F71B47}'
    AND IsActiveFLG = 1
))
BEGIN
    UPDATE Survey.Question set IsActiveFLG = 0 WHERE 
    QuestionId = '{D280562C-25F3-471A-8BDD-601533F71B47}'
END


IF (NOT EXISTS(SELECT * FROM [Survey].[Question]
WHERE FormID = '{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}'
    AND QuestionID ='{61A92ADF-F122-4E42-8D23-5F305E5FF699}'
))
BEGIN
    Declare @IncidentDetailsQuestionID uniqueidentifier = '{61A92ADF-F122-4E42-8D23-5F305E5FF699}'
    Declare @IncidentDetailsOrder int = 6
    Declare @IncidentDetailsSectionId uniqueidentifier = '{4A0BF9B6-0DAE-48E8-A787-1DCB4C7271AD}'
    Declare @IncidentDetailsText varchar(125) = 'Describe the incident in detail'
    Declare @SubmitIncidentCreatedBy varchar(50) = 'Health Catalyst'
    Declare @IncidentDetailsQuestionType int = (Select TypeId from Survey.QuestionType where TypeNM = 'WYSIWYG-Text')
    Declare @IncidentDetailsHelpText Varchar(255) = 'Examples of an Incident: Patient Fell on his way to the bathroom; Blood sugar dropped upon administration of medication'
    Declare @IncidentDetailsQuestionSubText Varchar(255) = 'Describe the incident in as much detail as possible, including information about the cause of the event, witnesses, and any follow-up actions.'
    INSERT INTO [Survey].[Question]
           ([QuestionID]
           ,[FormID]
           ,[SectionID]
           ,[DisplayOrderNBR]
           ,[QuestionTXT]
		   ,[QuestionTypeID]
		   ,[CreatedByTXT]
		   ,[CreatedDTS]
           ,[HelpText]
           ,[QuestionSubTXT])
     VALUES
           (@IncidentDetailsQuestionID
           ,'{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}'
           ,@IncidentDetailsSectionId
           ,@IncidentDetailsOrder
           ,@IncidentDetailsText
		   ,@IncidentDetailsQuestionType
		   ,@SubmitIncidentCreatedBy
		   ,GETDATE()
           ,@IncidentDetailsHelpText
           ,@IncidentDetailsQuestionSubText)
END

IF (EXISTS(SELECT * FROM [Survey].[Question]
WHERE FormID = '{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}'
    AND QuestionID ='{5AEB1CD5-FBB8-4F54-B090-BDB69B3072BE}'
    AND QuestionTypeID <> (Select TypeID from Survey.QuestionType where TypeNM = 'Multiple-Choice-Text-Dynamic')
))
BEGIN
    UPDATE [Survey].[Question]
    SET QuestionTypeId = (Select TypeID from Survey.QuestionType where TypeNM = 'Multiple-Choice-Text-Dynamic')
    
    WHERE FormID = '{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}'
    AND QuestionID ='{5AEB1CD5-FBB8-4F54-B090-BDB69B3072BE}'
END

IF (EXISTS(SELECT * FROM [Survey].[Question]
WHERE FormID = '{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}'
    AND QuestionID ='{247957FB-4BD0-4AD9-9CAB-E579A01F216C}'
    AND QuestionTXT <> 'Which individual would you like to associate this incident to?'  
))
BEGIN
    UPDATE [Survey].[Question]
    SET QuestionTxt = 'Which individual would you like to associate this incident to?'  
    WHERE FormID = '{D7FA0685-8B5B-4380-943D-F0C3C33D5D6C}'
    AND QuestionID ='{247957FB-4BD0-4AD9-9CAB-E579A01F216C}'
END

IF (NOT EXISTS(select * from Survey.QuestionOption where QuestionId = '{C585A755-C3AC-4DDC-A888-88DBCE2BBC32}'))

BEGIN
   --------- Incident TYpe
    Declare @IncidentTypeQuestionID uniqueidentifier = '{C585A755-C3AC-4DDC-A888-88DBCE2BBC32}'

    INSERT INTO [Survey].[QuestionOption]
           ([QuestionOptionID]
           ,[QuestionID]
           ,[OptionOrderNBR]
           ,[OptionTXT])
             VALUES
           (NEWID(), @IncidentTypeQuestionID, 1, 'Events Related to Medication/IV Fluids'),
           (NEWID(), @IncidentTypeQuestionID, 2, 'Events Related to Patient Care'),
           (NEWID(), @IncidentTypeQuestionID, 3, 'Events Related to Perinatal Care'),
           (NEWID(), @IncidentTypeQuestionID, 4, 'Events Related to Surgery or Other Procedures'),
           (NEWID(), @IncidentTypeQuestionID, 5, 'Events Related to Healthcare Associated Infections'),
           (NEWID(), @IncidentTypeQuestionID, 6, 'Unknown')
END

GO

IF (NOT EXISTS(select * from Survey.QuestionOption where QuestionOptionID = 'a7a4b573-09ec-4cb6-a6fb-9dd441a2ac52'))
BEGIN
    DECLARE @OptionOrderNumber INT

    select @OptionOrderNumber = MAX(OptionOrderNBR)
    from Survey.QuestionOption
    where questionid = 'c585a755-c3ac-4ddc-a888-88dbce2bbc32'
    and IsActiveFLG = 1

    INSERT INTO Survey.QuestionOption
        (
        QuestionOptionID
        ,QuestionID
        ,OptionOrderNBR
        ,OptionTXT
        ,IsActiveFLG
        ,IsDefaultOption
        )
    VALUES (
        'a7a4b573-09ec-4cb6-a6fb-9dd441a2ac52'
        , 'c585a755-c3ac-4ddc-a888-88dbce2bbc32'
        , @OptionOrderNumber + 1
        , 'Other'
        , 1
        , 0
    )
END

GO

-- END whla data fix


