DECLARE @RemovedQuestionOptionIdOne UniqueIdentifier = '4BFDC27C-285C-475B-B106-E81C9DFC051A'
DECLARE @RemovedQuestionOptionIdTwo UniqueIdentifier = 'C075FB77-C37C-4E38-B5C4-EB876E506948'
DECLARE @UpdateOrderQuestionOptionId UniqueIdentifier = '2B55C905-E8F6-484D-8211-BEF32468B402'

IF (EXISTS(SELECT * FROM Survey.QuestionOption where QuestionOptionId = @RemovedQuestionOptionIdOne))
BEGIN
    DELETE FROM [Survey].[QuestionOption]
    WHERE QuestionOptionID in (@RemovedQuestionOptionIdOne, @RemovedQuestionOptionIdTwo)

    UPDATE [Survey].[QuestionOption]
    SET OptionOrderNBR = 6
    WHERE QuestionOptionID = @UpdateOrderQuestionOptionId
END


