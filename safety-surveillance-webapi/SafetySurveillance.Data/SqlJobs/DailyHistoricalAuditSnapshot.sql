IF NOT EXISTS(SELECT 1 FROM msdb.dbo.sysjobs WHERE name = N'PatientSafety-Daily_HistoricalAudit_Snapshot_Job')
BEGIN /** ===== Start of Create SQL Job Block ==== **/

/****** Object:  Job [PatientSafety-Daily_HistoricalAudit_Snapshot_Job]  ******/
BEGIN TRANSACTION
DECLARE @ReturnCode INT
SELECT @ReturnCode = 0
/****** Object:  JobCategory [[Uncategorized (Local)]]  ******/
IF NOT EXISTS (SELECT name FROM msdb.dbo.syscategories WHERE name=N'[Uncategorized (Local)]' AND category_class=1)
BEGIN
EXEC @ReturnCode = msdb.dbo.sp_add_category @class=N'JOB', @type=N'LOCAL', @name=N'[Uncategorized (Local)]'
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback

END

DECLARE @jobId BINARY(16)
EXEC @ReturnCode =  msdb.dbo.sp_add_job @job_name=N'PatientSafety-Daily_HistoricalAudit_Snapshot_Job', 
		@enabled=1, 
		@notify_level_eventlog=0, 
		@notify_level_email=0, 
		@notify_level_netsend=0, 
		@notify_level_page=0, 
		@delete_level=0, 
		@description=N'This job for the Patient Safety database will take a daily snapshot of Audit tables to support configurable audits', 
		@category_name=N'[Uncategorized (Local)]',
        @owner_login_name=N'$(ServiceAccountName)', @job_id = @jobId OUTPUT
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
/****** Object:  Step [Create_HistoricalAudit_Tables]   ******/
EXEC @ReturnCode = msdb.dbo.sp_add_jobstep @job_id=@jobId, @step_name=N'Create_HistoricalAudit_Tables', 
		@step_id=1, 
		@cmdexec_success_code=0, 
		@on_success_action=3, 
		@on_success_step_id=0, 
		@on_fail_action=2, 
		@on_fail_step_id=0, 
		@retry_attempts=0, 
		@retry_interval=0, 
		@os_run_priority=0, @subsystem=N'TSQL', 
		@command=N'-- SQL Agent Job - Step 1 - determines if backup tables need to be created and does so

DECLARE @BackupSchema AS VARCHAR(50)=''Historical'' -- this is where new backup tables will be created
DECLARE @SchemaToBackup AS VARCHAR(50)=''PSMSurvey'' -- existing SAM schema for source Audit tables
DECLARE @TablePrefixToBackup AS VARCHAR(50)=''Audit%'' -- existing SAM Audit table prefixes to match

DECLARE @TableNameToCreate AS NVARCHAR(256)
DECLARE @CreateBackupTableSql AS VARCHAR(MAX)

DECLARE Audit_Cursor CURSOR FOR
SELECT [t1].[TABLE_NAME]
FROM INFORMATION_SCHEMA.TABLES AS [t1]
LEFT OUTER JOIN INFORMATION_SCHEMA.TABLES AS [t2]
  ON CONCAT([t1].[TABLE_SCHEMA],''_'',[t1].[TABLE_NAME]) = [t2].[TABLE_NAME]
  AND [t2].[TABLE_SCHEMA] = @BackupSchema
WHERE [t1].[TABLE_SCHEMA] = @SchemaToBackup
  AND [t1].[TABLE_NAME] LIKE @TablePrefixToBackup
  AND [t1].[TABLE_TYPE] = ''VIEW'' -- using VIEW here because that is how SAM Designer creates the entities
  AND [t2].[TABLE_NAME] IS NULL;
OPEN Audit_Cursor;
FETCH NEXT FROM Audit_Cursor INTO @TableNameToCreate;
WHILE @@FETCH_STATUS = 0
BEGIN
		PRINT '' '' + @TableNameToCreate
		-- tables being created will have this type of naming: **[Historical].[PSMSurvey_AuditPressureInjury]**
		SET @CreateBackupTableSql = ''SELECT TOP 0 * INTO ['' + @BackupSchema + ''].['' + @SchemaToBackup + ''_'' + @TableNameToCreate + ''] FROM ['' + @SchemaToBackup + ''].['' + @TableNameToCreate + '']''
		PRINT '' '' + @CreateBackupTableSql -- log what SQL command is being executed
		EXEC(@CreateBackupTableSql)
		FETCH NEXT FROM Audit_Cursor INTO @TableNameToCreate;
END;
CLOSE Audit_Cursor;
DEALLOCATE Audit_Cursor;
GO
', 
		@database_name='$(DatabaseName)', 
		@flags=4
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
/****** Object:  Step [Insert_Update_HistoricalAuditBackups]  ******/
EXEC @ReturnCode = msdb.dbo.sp_add_jobstep @job_id=@jobId, @step_name=N'Insert_Update_HistoricalAuditBackups', 
		@step_id=2, 
		@cmdexec_success_code=0, 
		@on_success_action=1, 
		@on_success_step_id=0, 
		@on_fail_action=2, 
		@on_fail_step_id=0, 
		@retry_attempts=0, 
		@retry_interval=0, 
		@os_run_priority=0, @subsystem=N'TSQL', 
		@command=N'DECLARE @BackupSchema AS VARCHAR(50)=''Historical'' -- this is where new backup tables will be created
DECLARE @SchemaToBackup AS VARCHAR(50)=''PSMSurvey'' -- existing SAM schema for source Audit tables
DECLARE @TablePrefixToBackup AS VARCHAR(50)=''Audit%'' -- existing SAM Audit table prefixes to match

DECLARE @TableNameToCreate AS NVARCHAR(256)
DECLARE @CreateBackupTableSql AS VARCHAR(MAX)

DECLARE Audit_Cursor CURSOR FOR

	SELECT [t1].[TABLE_NAME]
	FROM INFORMATION_SCHEMA.TABLES AS [t1]
	INNER JOIN [Survey].[AuditTool] AS [at]
	  ON [t1].TABLE_NAME = [at].[HistoricalBackupSource]
	WHERE [t1].[TABLE_SCHEMA] = @SchemaToBackup
	  AND [t1].[TABLE_NAME] LIKE @TablePrefixToBackup
	  AND [t1].[TABLE_TYPE] = ''VIEW'';

OPEN Audit_Cursor;
FETCH NEXT FROM Audit_Cursor INTO @TableNameToCreate;

WHILE @@FETCH_STATUS = 0 
	BEGIN -- ==== **AUDIT** CURSOR LOOP START =====
		PRINT(''=== Starting processing for: **['' + @TableNameToCreate + '']**'')
		DECLARE @UpdateSql AS VARCHAR(MAX)
		DECLARE @InsertSql AS VARCHAR(MAX)
		DECLARE @UpdateSelectWhereSql AS VARCHAR(MAX)
		DECLARE @InsertSelectWhereSql AS VARCHAR(MAX)
		DECLARE @TargetTableName AS NVARCHAR(256)
		DECLARE @SourceTableName AS NVARCHAR(256)
		DECLARE @ColumnNameToUpdate AS NVARCHAR(256)
		DECLARE @ColumnIsKey AS BIT

		SET @UpdateSql = NULL
		SET @InsertSql = NULL
		SET @UpdateSelectWhereSql = NULL
		SET @InsertSelectWhereSql = NULL

		SET @TargetTableName = ''['' + @BackupSchema + ''].['' + @SchemaToBackup + ''_'' + @TableNameToCreate + '']''
		SET @SourceTableName = ''['' + @SchemaToBackup + ''].['' + @TableNameToCreate + '']''
		SET @InsertSql = ''INSERT INTO '' + @TargetTableName + CHAR(13) + ''SELECT DISTINCT [a].* FROM [Survey].[AuditTool] AS [at] INNER JOIN [Survey].[SurveyEvent] AS [se] ON [at].[HistoricalBackupSource]='''''' 
					+ @TableNameToCreate + '''''' AND [at].[FormId]=[se].[FormId] INNER JOIN '' + @SourceTableName + '' AS [a] ON [se].[EncounterID]=[a].[EncounterID] AND [se].[EncounterSourceDSC]=[a].[EncounterSourceDSC]''
					+ '' WHERE NOT EXISTS (SELECT * FROM '' + @TargetTableName + '' AS [h] WHERE ''

		DECLARE Column_Cursor CURSOR FOR

			SELECT [c].[COLUMN_NAME], [ac].[IsHistoricalBackupKey]
			FROM INFORMATION_SCHEMA.COLUMNS AS [c]
			  INNER JOIN INFORMATION_SCHEMA.TABLES AS [t]
			ON [c].[TABLE_CATALOG] = [t].[TABLE_CATALOG]
			  AND [c].[TABLE_SCHEMA] = [t].[TABLE_SCHEMA]
			  AND [c].[TABLE_NAME] = [t].[TABLE_NAME]
			INNER JOIN [Survey].[AuditTool] AS [at]
				ON [c].[TABLE_NAME] = [at].[HistoricalBackupSource]
			LEFT OUTER JOIN [HCPatientSafety].[Survey].[AuditToolColumn] AS [ac]
				ON [at].[Id] = [ac].[AuditId]
				AND ([c].[COLUMN_NAME] = [ac].[ColumnName] OR [c].[COLUMN_NAME] = [ac].[SelectColumnName])
			WHERE [c].[TABLE_SCHEMA] = @SchemaToBackup
			  AND [c].[TABLE_NAME] = @TableNameToCreate
			  AND [t].[TABLE_TYPE] = ''VIEW'';

		OPEN Column_Cursor;

		FETCH NEXT FROM Column_Cursor INTO @ColumnNameToUpdate, @ColumnIsKey;

		WHILE @@FETCH_STATUS = 0
			BEGIN -- ==== **COLUMN** CURSOR LOOP START =====
				IF @UpdateSql IS NULL
					BEGIN
					SET @UpdateSql = ''UPDATE [h] SET [h].['' + @ColumnNameToUpdate + '']=[a].['' + @ColumnNameToUpdate + '']''
					END
				ELSE
					BEGIN
					SET @UpdateSql = @UpdateSql + '', [h].['' + @ColumnNameToUpdate + '']=[a].['' + @ColumnNameToUpdate + '']''
					END

				IF @ColumnIsKey = 1 AND @UpdateSelectWhereSql IS NULL
					BEGIN
					SET @UpdateSelectWhereSql = ''[a].['' + @ColumnNameToUpdate + ''] = [h].['' + @ColumnNameToUpdate + '']''
					END
				ELSE IF @ColumnIsKey = 1 AND @UpdateSelectWhereSql IS NOT NULL
					BEGIN
					SET @UpdateSelectWhereSql = @UpdateSelectWhereSql +  '' AND [a].['' + @ColumnNameToUpdate + ''] = [h].['' + @ColumnNameToUpdate + '']''
					END

				IF @ColumnIsKey = 1 AND @InsertSelectWhereSql IS NULL
					BEGIN
					SET @InsertSelectWhereSql = ''[a].['' + @ColumnNameToUpdate + ''] = [h].['' + @ColumnNameToUpdate + '']''
					END
				ELSE IF @ColumnIsKey = 1 AND @InsertSelectWhereSql IS NOT NULL
					BEGIN
					SET @InsertSelectWhereSql = @InsertSelectWhereSql +  '' AND [a].['' + @ColumnNameToUpdate + ''] = [h].['' + @ColumnNameToUpdate + '']''
					END

				FETCH NEXT FROM Column_Cursor INTO @ColumnNameToUpdate, @ColumnIsKey;
			END; -- ==== **COLUMN** CURSOR LOOP FINISH =====

		CLOSE Column_Cursor;
		DEALLOCATE Column_Cursor;

		--PRINT(@UpdateSelectWhereSql)
		SET @InsertSql = @InsertSql + @InsertSelectWhereSql + '');''
		SET @UpdateSql = @UpdateSql + '' FROM '' + @TargetTableName + '' h, '' + @SourceTableName + '' a WHERE '' + @UpdateSelectWhereSql

		PRINT(@UpdateSql)
		PRINT(@InsertSql)
		
		EXEC(@UpdateSql)
		EXEC(@InsertSql)

		FETCH NEXT FROM Audit_Cursor INTO @TableNameToCreate;

	END; -- ==== **AUDIT** CURSOR LOOP FINISH =====

CLOSE Audit_Cursor;
DEALLOCATE Audit_Cursor;
GO
', 
		@database_name='$(DatabaseName)', 
		@flags=4
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
EXEC @ReturnCode = msdb.dbo.sp_update_job @job_id = @jobId, @start_step_id = 1
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
EXEC @ReturnCode = msdb.dbo.sp_add_jobschedule @job_id=@jobId, @name=N'7:15 AM Daily', 
		@enabled=1, 
		@freq_type=4, 
		@freq_interval=1, 
		@freq_subday_type=1, 
		@freq_subday_interval=0, 
		@freq_relative_interval=0, 
		@freq_recurrence_factor=0, 
		@active_start_date=20230801, 
		@active_end_date=99991231, 
		@active_start_time=71500, 
		@active_end_time=235959, 
		@schedule_uid=N'dd3a4552-bf4c-42ad-8ce3-33a3f1a52c7b'
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
EXEC @ReturnCode = msdb.dbo.sp_add_jobserver @job_id = @jobId, @server_name = N'(local)'
IF (@@ERROR <> 0 OR @ReturnCode <> 0) GOTO QuitWithRollback
COMMIT TRANSACTION
GOTO EndSave
QuitWithRollback:
    IF (@@TRANCOUNT > 0) ROLLBACK TRANSACTION
EndSave:
  
END /** ===== End of Create SQL Job Block ==== **/

GO


