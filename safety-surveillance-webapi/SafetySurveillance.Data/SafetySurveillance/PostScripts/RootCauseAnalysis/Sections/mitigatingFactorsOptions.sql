DECLARE @MitigatingFactorsCategoryId int;
select @MitigatingFactorsCategoryId = id from [SafetySurveillance].[RootCauseAnalysisCategory] where Name = 'Mitigating Factors'


--Mitigating Factors
  


--Directed To Patient
 declare @directedToPatientOptionId UniqueIdentifier = '51BCDB92-546B-482D-9696-7FE8C6F2C650'
    INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
              (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable)
              values (@directedToPatientOptionId, 'Directed To Patient', @MitigatingFactorsCategoryId, 1, 0);

                -- Help Called For
              DECLARE @helpCalledForOptionId uniqueidentifier = '1FE52D61-E7BD-46DA-8C6E-7C2C407B8B0D'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@helpCalledForOptionId, 'Help Called For',@MitigatingFactorsCategoryId, 1, 1, @directedToPatientOptionId);

                 -- Management/Treatment/Care Undertaken
              DECLARE @ManagementOptionId uniqueidentifier = '31A9B0B4-845E-47B2-9F6A-094198514846'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@ManagementOptionId, 'Management/Treatment/Care Undertaken',@MitigatingFactorsCategoryId, 2, 1, @directedToPatientOptionId);

                    -- Patient Referred
              DECLARE @patientReferredOptionId uniqueidentifier = '1D5ED09A-DE4C-4757-A0EC-F1548F21FD7A'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@patientReferredOptionId, 'Patient Referred',@MitigatingFactorsCategoryId, 3, 1, @directedToPatientOptionId);


                      -- Patient Education/Explanation
              DECLARE @patientEducationOptionId uniqueidentifier = '25164218-9401-41BC-A598-F14298226609'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@patientEducationOptionId, 'Patient Education/Explanation',@MitigatingFactorsCategoryId, 4, 1, @directedToPatientOptionId);

                       -- Apology
              DECLARE @apologyOptionId uniqueidentifier = '80ECEA51-1779-4CC4-A60E-5CFFB14B0D47'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@apologyOptionId, 'Apology',@MitigatingFactorsCategoryId, 5, 1, @directedToPatientOptionId);



--Directed To Staff
 declare @directedToStaffOptionId UniqueIdentifier = '55E0FB9C-7354-4CB8-832C-AF1C8C972334'
    INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
              (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable)
              values (@directedToStaffOptionId, 'Directed To Staff', @MitigatingFactorsCategoryId, 2, 0);

               -- Good Supervision/Leadership
              DECLARE @goodSupervisionOptionId uniqueidentifier = '37935BD8-9352-4DF2-8647-669BF6A02AB5'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@goodSupervisionOptionId, 'Good Supervision/Leadership',@MitigatingFactorsCategoryId, 1, 1, @directedToStaffOptionId);

                 -- Good Team Work
              DECLARE @goodTeamWorkOptionId uniqueidentifier = 'BEA5EB26-01B4-4D8F-A473-ACC366B34D91'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@goodTeamWorkOptionId, 'Good Team Work',@MitigatingFactorsCategoryId, 2, 1, @directedToStaffOptionId);

                  -- Effective Communication
              DECLARE @effectiveCommunicationOptionId uniqueidentifier = '3A34F97F-B80E-49EC-B195-943EBCF99C96'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@effectiveCommunicationOptionId, 'Effective Communication',@MitigatingFactorsCategoryId, 3, 1, @directedToStaffOptionId);

                 -- Relevant Person(s) Attended
              DECLARE @personsAttendedOptionId uniqueidentifier = '38BE4C0A-14D9-46F3-B104-79D447691980'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@personsAttendedOptionId, 'Relevant Person(s) Attended',@MitigatingFactorsCategoryId, 4, 1, @directedToStaffOptionId);

             
                -- Relevant Person(s) Educated
              DECLARE @personsEducatedOptionId uniqueidentifier = '49F46E19-2AB1-485A-A899-510B2FD21DFA'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@personsEducatedOptionId, 'Relevant Person(s) Educated',@MitigatingFactorsCategoryId, 5, 1, @directedToStaffOptionId);

              -- Good Luck/Chance
              DECLARE @goodLuckOptionId uniqueidentifier = 'E119C0A5-C12F-4DD8-B928-DC03E52E3206'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@goodLuckOptionId, 'Good Luck/Chance',@MitigatingFactorsCategoryId, 6, 1, @directedToStaffOptionId);


--DIRECTED TO ORGANIZATION
 declare @directedToOrganizationOptionId UniqueIdentifier = '6DFDA4FF-831B-4A26-B64C-DBAF10494BCA'
    INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
              (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable)
              values (@directedToOrganizationOptionId, 'Directed To Organization', @MitigatingFactorsCategoryId, 3, 0);


                  -- Effective Protocol Available
              DECLARE @effectiveProtocolOptionId uniqueidentifier = '45A82B65-09D4-47EC-A544-62CA92D61059'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@effectiveProtocolOptionId, 'Effective Protocol Available',@MitigatingFactorsCategoryId, 1, 1, @directedToOrganizationOptionId);

                    -- Product/Equipment/Device Management & Availability/Accessibility
              DECLARE @equipmentOptionId uniqueidentifier = '78EBA6BF-4622-4832-963F-FB9A8D672C4B'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@equipmentOptionId, 'Product/Equipment/Device Management & Availability/Accessibility',@MitigatingFactorsCategoryId, 2, 1, @directedToOrganizationOptionId);

                    -- Documentation Error Corrected
              DECLARE @DocumentationErrorOptionId uniqueidentifier = '693753A6-D81C-4165-A4FF-D1E4976D4A31'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@DocumentationErrorOptionId, 'Documentation Error Corrected',@MitigatingFactorsCategoryId, 3, 1, @directedToOrganizationOptionId);




--DIRECTED TO AN AGENT
 declare @directedToAgentOptionId UniqueIdentifier = '263DA967-B724-43E0-8A25-F389341D0226'
    INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
              (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable)
              values (@directedToAgentOptionId, 'Directed To An Agent', @MitigatingFactorsCategoryId, 4, 0);


                  -- Security/Physical Environment Measure
              DECLARE @securityOptionId uniqueidentifier = 'F45807AE-B105-4EFC-B721-60CF5CD9E468'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@securityOptionId, 'Security/Physical Environment Measure',@MitigatingFactorsCategoryId, 1, 1, @directedToAgentOptionId);

                    -- Infection Control Strategies Managed/Implemented
              DECLARE @infectionControlOptionId uniqueidentifier = '09765800-062B-4D96-A750-2E3CD42EFF3C'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@infectionControlOptionId, 'Infection Control Strategies Managed/Implemented',@MitigatingFactorsCategoryId, 2, 1, @directedToAgentOptionId);

                  -- Therapeutic Agent Error Corrected
              DECLARE @therapeuticErrorOptionId uniqueidentifier = '6287E3AB-1200-4903-AAA7-5F1DAA3BF857'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@therapeuticErrorOptionId, 'Therapeutic Agent Error Corrected',@MitigatingFactorsCategoryId, 3, 1, @directedToAgentOptionId);

                  -- Equipment Usage Error Corrected
              DECLARE @equipmentErrorOptionId uniqueidentifier = '0535C49C-2847-46E2-90FA-F4DFDE541E76'
              INSERT INTO [SafetySurveillance].[RootCauseAnalysisOption]
               (Id, Name, RootCauseAnalysisCategoryId, [Order], IsSelectable, ParentOptionId)
               values (@equipmentErrorOptionId, 'Equipment Usage Error Corrected',@MitigatingFactorsCategoryId, 4, 1, @directedToAgentOptionId);