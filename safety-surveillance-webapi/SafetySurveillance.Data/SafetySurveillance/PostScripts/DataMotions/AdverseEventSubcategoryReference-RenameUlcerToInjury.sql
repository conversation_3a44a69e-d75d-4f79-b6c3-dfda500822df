IF EXISTS (SELECT AdverseEventSubCategoryDSC FROM [SafetySurveillance].[AdverseEventSubcategoryReference] WHERE AdverseEventSubCategoryDSC like '%Ulcer%')
BEGIN
  
  UPDATE [SafetySurveillance].[AdverseEventSubcategoryReference]
  SET AdverseEventSubCategoryDSC = 'Stage III or IV Pressure Injury'
  WHERE Id = 50

  UPDATE [SafetySurveillance].[AdverseEventSubcategoryReference]
  SET AdverseEventSubCategoryDSC = 'Stage I or II Pressure Injury'
  WHERE Id = 49


  UPDATE [SafetySurveillance].[AdverseEventSubcategoryReference]
  SET AdverseEventSubCategoryDSC = 'Pressure Injury'
  WHERE Id = 45

END