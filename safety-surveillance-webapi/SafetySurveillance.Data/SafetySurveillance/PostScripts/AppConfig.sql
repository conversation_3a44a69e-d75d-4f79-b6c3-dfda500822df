DECLARE @selectedTriggerIdName varchar(50);
DECLARE @riskPredictionFlg varchar(50);
DECLARE @enabledAeDeepDives varchar(50);
DECLARE @enablePublicHealthFlg varchar(50);
DECLARE @harmDetectionFlg varchar(50);
DECLARE @enabledAuditTools varchar(50);
DECLARE @psmReleaseDate varchar(50);
DECLARE @psmReleaseCodeName varchar(50);
DECLARE @psmWebAppBuildNumber varchar(50);
DECLARE @psmWebApiBuildNumber varchar(50);
DECLARE @psmNotifierBuildNumber varchar(50);
SET @selectedTriggerIdName = 'selectedTriggerIds'
SET @riskPredictionFlg = 'riskPredictionFlg'
SET @enabledAeDeepDives = 'enabledAeDeepDives'
SET @enablePublicHealthFlg = 'enablePublicHealthFlg'
SET @harmDetectionFlg = 'harmDetectionFlg'
SET @enabledAuditTools = 'enabledAuditTools'
SET @psmReleaseDate = 'psmReleaseDate'
SET @psmReleaseCodeName = 'psmReleaseCodeName'
SET @psmWebAppBuildNumber = 'psmWebAppBuildNumber'
SET @psmWebApiBuildNumber = 'psmWebApiBuildNumber'
SET @psmNotifierBuildNumber = 'psmNotifierBuildNumber'

IF NOT Exists(select f.Name from SafetySurveillance.AppConfig f where f.Name = @selectedTriggerIdName)
  BEGIN
    INSERT into SafetySurveillance.AppConfig(Name, Value) VALUES(@selectedTriggerIdName, '105,102');
  END

IF NOT Exists(SELECT f.Name FROM SafetySurveillance.AppConfig f WHERE f.Name = @riskPredictionFlg)
    BEGIN
        INSERT INTO SafetySurveillance.AppConfig(Name, Value) VALUES (@riskPredictionFlg, 'false');
    END

IF NOT Exists(SELECT f.Name FROM SafetySurveillance.AppConfig f WHERE f.Name = @enabledAeDeepDives)
    BEGIN
        INSERT INTO SafetySurveillance.AppConfig(Name, Value) VALUES (@enabledAeDeepDives, '');
    END;

IF NOT Exists(SELECT f.Name FROM SafetySurveillance.AppConfig f WHERE f.Name = @enablePublicHealthFlg)
    BEGIN
        INSERT INTO SafetySurveillance.AppConfig(Name, Value) VALUES (@enablePublicHealthFlg, 'false');
    END;


IF NOT Exists(SELECT f.Name FROM SafetySurveillance.AppConfig f WHERE f.Name = @harmDetectionFlg)
    BEGIN
        INSERT INTO SafetySurveillance.AppConfig(Name, Value) VALUES (@harmDetectionFlg, 'true');
    END;

IF NOT Exists(SELECT f.Name FROM SafetySurveillance.AppConfig f WHERE f.Name = @enabledAuditTools)
    BEGIN
        INSERT INTO SafetySurveillance.AppConfig(Name, Value) VALUES (@enabledAuditTools, '');
    END;

IF NOT Exists(SELECT f.Name FROM SafetySurveillance.AppConfig f WHERE f.Name = @psmReleaseDate)
    BEGIN
        INSERT INTO SafetySurveillance.AppConfig(Name, Value) VALUES (@psmReleaseDate, '');
    END;

IF NOT Exists(SELECT f.Name FROM SafetySurveillance.AppConfig f WHERE f.Name = @psmReleaseCodeName)
    BEGIN
        INSERT INTO SafetySurveillance.AppConfig(Name, Value) VALUES (@psmReleaseCodeName, '');
    END;

IF NOT Exists(SELECT f.Name FROM SafetySurveillance.AppConfig f WHERE f.Name = @psmWebAppBuildNumber)
    BEGIN
        INSERT INTO SafetySurveillance.AppConfig(Name, Value) VALUES (@psmWebAppBuildNumber, '');
    END;

IF NOT Exists(SELECT f.Name FROM SafetySurveillance.AppConfig f WHERE f.Name = @psmWebApiBuildNumber)
    BEGIN
        INSERT INTO SafetySurveillance.AppConfig(Name, Value) VALUES (@psmWebApiBuildNumber, '');
    END;

IF NOT Exists(SELECT f.Name FROM SafetySurveillance.AppConfig f WHERE f.Name = @psmNotifierBuildNumber)
    BEGIN
        INSERT INTO SafetySurveillance.AppConfig(Name, Value) VALUES (@psmNotifierBuildNumber, '');
    END;