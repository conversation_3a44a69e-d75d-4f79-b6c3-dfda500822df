CREATE TABLE [Authorize].[IncidentLocationLeads]
(
	[Id] INT NOT NULL IDENTITY CONSTRAINT [PK_IncidentLocationLeads] PRIMARY KEY CLUSTERED ([Id] ASC),
    [UserName] VARCHAR(100) NOT NULL,
    [Location] VARCHAR(255) NOT NULL,
    [Facility] VARCHAR(255) NOT NULL,
    CONSTRAINT [FK_IncidentLocationLeads_ToUsers] FOREIGN KEY ([UserName]) REFERENCES [Authorize].[Users]([UserName]),
    CONSTRAINT [UQ_Location_User] UNIQUE(Facility, Location, UserName)
)
