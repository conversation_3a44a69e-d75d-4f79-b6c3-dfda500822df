using SafetySurveillance.Domain.Common;

namespace SafetySurveillance.Application.Contracts.Filter;

public class CriteriaServiceLine<T> : ICriteria<T> where T : class
{
    private readonly List<FilterModel>? lines;
    private readonly string propertyName;

    public CriteriaServiceLine(List<FilterModel>? lines, string propertyName)
    {
        this.lines = lines;
        this.propertyName = propertyName;
    }
    
    public List<T> MeetCriteria(List<T> records)
    {
        if (lines is not { Count: > 0 })
            return records;
        
        var returnData = new List<T>();
        foreach (var record in records)
        {
            if (record is IDictionary<string, object> fields 
                && fields.TryGetValue(propertyName, out object? value) && lines.Any(u => u.Value == value.ToString()))
            {
                returnData.Add(record);
            }
        }

        return returnData;
    }
}
