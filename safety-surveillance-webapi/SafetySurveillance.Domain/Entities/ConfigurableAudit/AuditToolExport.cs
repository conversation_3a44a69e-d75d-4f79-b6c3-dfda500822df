using SafetySurveillance.Domain.Common;

namespace SafetySurveillance.Domain.Entities.ConfigurableAudit;

public class AuditToolExport : AuditableEntity
{
    public Guid Id { get; init; }
    public Guid AuditId { get; init; }
    public string ColumnName { get; init; } = string.Empty;
    public string DisplayName { get; init; } = string.Empty;
    public string DataType { get; init; } = string.Empty;
    public string Section { get; init; } = string.Empty;
    public string SchemaName { get; init; } = string.Empty;
    public string TableName { get; init; } = string.Empty;
    public string SelectColumnName { get; init; } = string.Empty;
    public int DisplayOrder { get; set; }
    
    public AuditTool AuditTool { get; init; }
}
