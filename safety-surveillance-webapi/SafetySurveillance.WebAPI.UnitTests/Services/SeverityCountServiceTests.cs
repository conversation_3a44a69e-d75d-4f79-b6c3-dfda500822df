namespace SafetySurveillance.WebAPI.UnitTests.Services
{
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using SafetySurveillanceWebApi.Models;
    using SafetySurveillanceWebApi.Services.Severity;
    using System.Collections.Generic;


    [TestClass]
    public class SeverityCountServiceTests
    {

        List<SeverityScaleType> severityScaleType = new List<SeverityScaleType>()
        {
            new SeverityScaleType()
            {
                ScaleTypeId = 1, Items = new List<SeverityScaleItem>()
                {
                    new SeverityScaleItem() { ItemName = "A", ScaleItemId = 1 },
                    new SeverityScaleItem() { ItemName = "B", ScaleItemId = 2 },
                    new SeverityScaleItem() { ItemName = "C", ScaleItemId = 3 },
                    new SeverityScaleItem() { ItemName = "D", ScaleItemId = 4 },
                    new SeverityScaleItem() { ItemName = "E", ScaleItemId = 5 },
                }
            },
            new SeverityScaleType()
            {
                ScaleTypeId = 2, Items = new List<SeverityScaleItem>()
                {
                    new SeverityScaleItem { ItemName = "Minor", ScaleItemId = 11 },
                    new SeverityScaleItem { ItemName = "Major", ScaleItemId = 12 },
                    new SeverityScaleItem { ItemName = "Moderate", ScaleItemId = 13 },
                    new SeverityScaleItem { ItemName = "None", ScaleItemId = 14 },
                    new SeverityScaleItem { ItemName = "Catastrophic", ScaleItemId = 15 },
                }
            },

        };

        [TestMethod]
        public void TestSeverityCountCalculate()
        {
            var b = new ScaleTypeCountCollection(severityScaleType);
            var c = b.Calculate(new List<SeverityScaleItem>()
            {
                new SeverityScaleItem() { ItemName = "A", ScaleItemId = 1, ScaleTypeId = 1 },
                new SeverityScaleItem() { ItemName = "B", ScaleItemId = 2, ScaleTypeId = 1 },
                new SeverityScaleItem() { ItemName = "C", ScaleItemId = 3, ScaleTypeId = 1 },
                new SeverityScaleItem() { ItemName = "A", ScaleItemId = 1, ScaleTypeId = 1 },
                new SeverityScaleItem() { ItemName = "Minor", ScaleItemId = 11, ScaleTypeId = 2 },
                new SeverityScaleItem() { ItemName = "Major", ScaleItemId = 12, ScaleTypeId = 2 },
                new SeverityScaleItem() { ItemName = "Minor", ScaleItemId = 11, ScaleTypeId = 2 },
            });

            Assert.AreEqual(c.Total, 7);
        }
    }
}