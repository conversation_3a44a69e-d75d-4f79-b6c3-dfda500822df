using MassTransit;
using SafetySurveillanceWebApi.Services.Authorization;

namespace SafetySurveillance.WebAPI.UnitTests.Services.SurveyEvent
{
    using System;
    using System.Collections.Generic;
    using System.Threading.Tasks;

    using Microsoft.VisualStudio.TestTools.UnitTesting;

    using Moq;

    using SafetySurveillanceWebApi.Models;
    using SafetySurveillanceWebApi.Models.Authorize;
    using SafetySurveillanceWebApi.Repositories;
    using SafetySurveillanceWebApi.Repositories.Incident;
    using SafetySurveillanceWebApi.Services;
    using SafetySurveillanceWebApi.Services.Survey;

    [TestClass]
    public class SurveyEventServiceTests
    {
        private SurveyEventService surveyEventService;
        private Mock<IAuditRepository> mockRoundRepository;
        private Mock<ISurveyEventRepository> mockSurveyEventRepository;
        private Mock<ISurveyEventMapper> mockSurveyEventMapper;
        private Mock<ISurveyEventResponseMultiSelectService> mockSurveyEventMultiSelectService;
        private Mock<IAuditLogService> mockAuditLogService;
        private Mock<IIncidentsRepository> incidentsRepository;
        private Mock<ITriggerEventCommentRepository> triggerEventCommentRepository;
        protected Mock<IUserService> userService;
        private Mock<IApplicationConfigurationRepository> applicationConfigurationRepo;
        private Mock<IPublishEndpoint> publishEndpoint;

        [TestClass]
        public class SaveStatus : SurveyEventServiceTests
        {

            [TestInitialize]
            public void Init()
            {
                this.mockRoundRepository = new Mock<IAuditRepository>();
                this.mockSurveyEventRepository = new Mock<ISurveyEventRepository>();
                this.mockSurveyEventMapper = new Mock<ISurveyEventMapper>();
                this.mockSurveyEventMultiSelectService = new Mock<ISurveyEventResponseMultiSelectService>();
                this.mockAuditLogService = new Mock<IAuditLogService>();
                this.incidentsRepository = new Mock<IIncidentsRepository>();
                this.triggerEventCommentRepository = new Mock<ITriggerEventCommentRepository>();
                this.userService = new Mock<IUserService>();
                this.applicationConfigurationRepo = new Mock<IApplicationConfigurationRepository>();
                this.publishEndpoint = new Mock<IPublishEndpoint>();
                this.surveyEventService = new SurveyEventService(
                    this.mockSurveyEventRepository.Object,
                    this.mockRoundRepository.Object,
                    this.mockSurveyEventMapper.Object,
                    this.mockSurveyEventMultiSelectService.Object,
                    this.mockAuditLogService.Object,
                    this.incidentsRepository.Object,
                    this.triggerEventCommentRepository.Object,
                    this.userService.Object,
                    this.publishEndpoint.Object,
                    this.applicationConfigurationRepo.Object);
            }

            [TestMethod]
            public async Task ValidIdCallsToSave()
            {
                // arrange
                var surveyId = Guid.NewGuid();
                var surveyEvent = new SurveyEvent
                                      {
                                          Assignments = new List<SurveyEventAssignment> { new SurveyEventAssignment() }
                                      };
                var statusId = -2;
                var status = new SurveyStatusReference() { StatusId = statusId };
                var updatedBy = "EarlSinclair";

                this.mockSurveyEventRepository.Setup(p => p.GetStatusById(statusId))
                    .Returns(Task.FromResult(status));
                this.mockSurveyEventRepository.Setup(s => s.GetSurveyEventWithAssignmentsById(surveyId))
                    .Returns(Task.FromResult(surveyEvent));

                // act
                var actualResult = await this.surveyEventService.SaveStatus(surveyId, statusId, updatedBy);

                // assert
                Assert.AreEqual(status, actualResult);
                this.mockSurveyEventRepository.Verify(p => p.GetStatusById(statusId), Times.Once);
                this.mockSurveyEventRepository.Verify(p => p.SaveSurvey(surveyEvent), Times.Once);
            }
        }

        [TestClass]
        public class GetSurveyEventsByTypeForPatientEncounterAsync : SurveyEventServiceTests
        {
            [TestInitialize]
            public void Init()
            {
                this.mockRoundRepository = new Mock<IAuditRepository>();
                this.mockSurveyEventRepository = new Mock<ISurveyEventRepository>();
                this.mockSurveyEventRepository
                    .Setup(
                        repo => repo.GetSurveyEventsByTypeForPatientEncounterAsync(
                            It.IsAny<Guid>(),
                            It.IsAny<string>(),
                            It.IsAny<string>())).Returns(Task.FromResult(new List<SurveyEvent>()));
                this.mockSurveyEventMapper = new Mock<ISurveyEventMapper>();
                this.mockSurveyEventMultiSelectService = new Mock<ISurveyEventResponseMultiSelectService>();
                this.mockAuditLogService = new Mock<IAuditLogService>();
                this.incidentsRepository = new Mock<IIncidentsRepository>();
                this.triggerEventCommentRepository = new Mock<ITriggerEventCommentRepository>();
                this.userService = new Mock<IUserService>();
                this.publishEndpoint = new Mock<IPublishEndpoint>();
                this.applicationConfigurationRepo = new Mock<IApplicationConfigurationRepository>();

                this.surveyEventService = new SurveyEventService(
                    this.mockSurveyEventRepository.Object,
                    this.mockRoundRepository.Object,
                    this.mockSurveyEventMapper.Object,
                    this.mockSurveyEventMultiSelectService.Object,
                    this.mockAuditLogService.Object,
                    this.incidentsRepository.Object,
                    this.triggerEventCommentRepository.Object,
                    this.userService.Object,
                    this.publishEndpoint.Object,
                    this.applicationConfigurationRepo.Object);
            }

            [TestMethod]
            public async Task ReturnsListOfSurveyEvents()
            {
                // arrange

                // act
                var actualResult =
                    await this.surveyEventService.GetSurveyEventsByTypeForPatientEncounterAsync(
                        Guid.NewGuid(),
                        "123",
                        "test");

                // assert
                Assert.IsNotNull(actualResult);
            }
        }

        [TestClass]
        public class GetFilterUnits : SurveyEventServiceTests
        {
            [TestInitialize]
            public void Init()
            {
                this.mockRoundRepository = new Mock<IAuditRepository>();
                this.mockSurveyEventRepository = new Mock<ISurveyEventRepository>();
                this.mockSurveyEventRepository
                    .Setup(
                        repo => repo.GetFilterUnits()).Returns(Task.FromResult(new List<string>()));
                this.mockSurveyEventMapper = new Mock<ISurveyEventMapper>();
                this.mockSurveyEventMultiSelectService = new Mock<ISurveyEventResponseMultiSelectService>();
                this.mockSurveyEventMultiSelectService = new Mock<ISurveyEventResponseMultiSelectService>();
                this.mockAuditLogService = new Mock<IAuditLogService>();
                this.incidentsRepository = new Mock<IIncidentsRepository>();
                this.triggerEventCommentRepository = new Mock<ITriggerEventCommentRepository>();
                this.userService = new Mock<IUserService>();
                this.publishEndpoint = new Mock<IPublishEndpoint>();
                this.applicationConfigurationRepo = new Mock<IApplicationConfigurationRepository>();

                this.surveyEventService = new SurveyEventService(
                    this.mockSurveyEventRepository.Object,
                    this.mockRoundRepository.Object,
                    this.mockSurveyEventMapper.Object,
                    this.mockSurveyEventMultiSelectService.Object,
                    this.mockAuditLogService.Object,
                    this.incidentsRepository.Object,
                    this.triggerEventCommentRepository.Object,
                    this.userService.Object,
                    this.publishEndpoint.Object,
                    this.applicationConfigurationRepo.Object);
            }

            [TestMethod]
            public async Task ReturnsListOfUnits()
            {
                // arrange

                // act
                var actualResult =
                    await this.surveyEventService.GetFilterUnits();

                // assert
                Assert.IsNotNull(actualResult);
            }
        }

        [TestClass]
        public class GetAuditAssignees : SurveyEventServiceTests
        {
            [TestInitialize]
            public void Init()
            {
                this.mockRoundRepository = new Mock<IAuditRepository>();
                this.mockRoundRepository.Setup(
                    repo => repo.GetAuditAssignees()).Returns(Task.FromResult(new List<SafetyUser>()));
                this.mockSurveyEventMapper = new Mock<ISurveyEventMapper>();
                this.mockSurveyEventRepository = new Mock<ISurveyEventRepository>();
                this.mockSurveyEventMultiSelectService = new Mock<ISurveyEventResponseMultiSelectService>();
                this.mockSurveyEventMultiSelectService = new Mock<ISurveyEventResponseMultiSelectService>();
                this.mockAuditLogService = new Mock<IAuditLogService>();
                this.incidentsRepository = new Mock<IIncidentsRepository>();
                this.triggerEventCommentRepository = new Mock<ITriggerEventCommentRepository>();
                this.userService = new Mock<IUserService>();
                this.publishEndpoint = new Mock<IPublishEndpoint>();
                this.applicationConfigurationRepo = new Mock<IApplicationConfigurationRepository>();

                this.surveyEventService = new SurveyEventService(
                    this.mockSurveyEventRepository.Object,
                    this.mockRoundRepository.Object,
                    this.mockSurveyEventMapper.Object,
                    this.mockSurveyEventMultiSelectService.Object,
                    this.mockAuditLogService.Object,
                    this.incidentsRepository.Object,
                    this.triggerEventCommentRepository.Object,
                    this.userService.Object,
                    this.publishEndpoint.Object,
                    this.applicationConfigurationRepo.Object);
            }

            [TestMethod]
            public async Task ReturnsListOfUnits()
            {
                // arrange

                // act
                var actualResult =
                    await this.surveyEventService.GetRoundAssignee();

                // assert
                Assert.IsNotNull(actualResult);
            }
        }
    }
}
