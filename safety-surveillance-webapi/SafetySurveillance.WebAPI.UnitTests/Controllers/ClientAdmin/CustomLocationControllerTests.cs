namespace SafetySurveillance.WebAPI.UnitTests.Controllers
{
    using System;
    using System.Collections.Generic;
    using System.ComponentModel.DataAnnotations;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.VisualStudio.TestTools.UnitTesting;

    using Moq;

    using SafetySurveillanceWebApi.Controllers;
    using SafetySurveillanceWebApi.Models.ClientAdmin.CustomLocations;
    using SafetySurveillanceWebApi.Repositories.ClientAdmin;

    [TestClass]
    public class CustomLocationControllerTests : BaseControllerTests
    {
        [TestClass]
        public class AddCustomLocationsForFacility : CustomLocationControllerTests
        {
            [TestMethod]
            public void AddCustomLocationsShouldValidateParameters()
            {
                // Arrange
                var parameters = typeof(CustomLocationController).GetMethod(nameof(CustomLocationController.AddCustomLocations))
                    .GetParameters().ToDictionary(p => p.Name, p => p);
                var attributes = parameters["input"].CustomAttributes;

                // Assert
                Assert.IsTrue(attributes.Any(a => a.AttributeType == typeof(RequiredAttribute)),
                    "input is not required");
            }

            [TestMethod]
            public async Task CallCustomLocationRepositoryAddCustomLocationWhenInputDTOIsProvided()
            {
                // Arrange
                var mockRepository = new Mock<ICustomLocationRepository>();
                var subject = new CustomLocationController(mockRepository.Object,
                    this.HttpContextAccessorMock.Object);

                // Act
                // ReSharper disable once UnusedVariable
                var result = await subject.AddCustomLocations(new AddCustomLocationsInputDTO()
                {
                    Facility = "test",
                    LocationNames = new List<string>() { "test1" }
                });

                // Assert
                mockRepository.Verify(mock => mock.AddCustomLocationsAsync(It.IsAny<AddCustomLocationsInputDTO>(), It.IsAny<string>()));
            }

            [TestMethod]
            public void AddCustomLocationsShouldFailIfLocationNamesIsNull()
            {
                // Arrange
                var repositoryMock = new Mock<ICustomLocationRepository>();
                var subject = new CustomLocationController(repositoryMock.Object,
                    this.HttpContextAccessorMock.Object);
                AddCustomLocationsInputDTO input = new AddCustomLocationsInputDTO()
                {
                    Facility = "facility",
                    LocationNames = null
                };

                // Act
                var validationResultList = new List<ValidationResult>();
                Validator.TryValidateObject(input, new ValidationContext(input), validationResultList);

                // Assert
                var message = validationResultList[0].ErrorMessage;
                Assert.AreEqual("The LocationNames field is required.", message);
            }

            [TestMethod]
            public void AddCustomLocationsShouldFailIfFacilityIsNull()
            {
                // Arrange
                var repositoryMock = new Mock<ICustomLocationRepository>();
                var subject = new CustomLocationController(repositoryMock.Object,
                    this.HttpContextAccessorMock.Object);
                AddCustomLocationsInputDTO input = new AddCustomLocationsInputDTO()
                {
                    Facility = null,
                    LocationNames = new List<string>() { "test1" }
                };

                // Act
                var validationResultList = new List<ValidationResult>();
                Validator.TryValidateObject(input, new ValidationContext(input), validationResultList);

                // Assert
                var message = validationResultList[0].ErrorMessage;
                Assert.AreEqual("The Facility field is required.", message);
            }
        }

        [TestClass]
        public class GetCustomLocationsByFacility : CustomLocationControllerTests
        {
            [TestMethod]
            public void GetCustomLocationsByFacilityShouldValidateParameters()
            {
                // Arrange
                var parameters = typeof(CustomLocationController).GetMethod(nameof(CustomLocationController.GetCustomLocationsByFacility))
                    .GetParameters().ToDictionary(p => p.Name, p => p);
                var attributes = parameters["facility"].CustomAttributes;

                // Assert
                Assert.IsTrue(attributes.Any(a => a.AttributeType == typeof(RequiredAttribute)),
                    "facility is not required");
            }

            [TestMethod]
            public async Task CallCustomLocationRepositoryGetCustomLocationsByFacilityWhenFacilityIsProvided()
            {
                // Arrange
                var mockRepository = new Mock<ICustomLocationRepository>();
                var subject = new CustomLocationController(mockRepository.Object,
                    this.HttpContextAccessorMock.Object);

                // Act
                // ReSharper disable once UnusedVariable
                var result = await subject.GetCustomLocationsByFacility("facility");

                // Assert
                mockRepository.Verify(mock => mock.GetCustomLocationsByFacilityAsync(It.IsAny<string>()));
            }
        }

        [TestClass]
        public class UpdateCustomLocationName : CustomLocationControllerTests
        {
            [TestMethod]
            public void ShouldValidateParameters()
            {
                // Arrange
                var parameters = typeof(CustomLocationController).GetMethod(nameof(CustomLocationController.AddCustomLocations))
                    .GetParameters().ToDictionary(p => p.Name, p => p);
                var attributes = parameters["input"].CustomAttributes;

                // Assert
                Assert.IsTrue(attributes.Any(a => a.AttributeType == typeof(RequiredAttribute)),
                    "input is not required");
            }

            [TestMethod]
            public async Task CallRepositoryWhenInputIsProvided()
            {
                // Arrange
                var mockRepository = new Mock<ICustomLocationRepository>();
                var subject = new CustomLocationController(mockRepository.Object,
                    this.HttpContextAccessorMock.Object);

                // Act
                // ReSharper disable once UnusedVariable
                var result = await subject.UpdateCustomLocationName(new UpdateCustomLocationNameInputDTO()
                {
                    Id = Guid.NewGuid(),
                    Facility = "test",
                    Name = "test"
                });

                // Assert
                mockRepository.Verify(mock => mock.UpdateCustomLocationNameAsync(It.IsAny<UpdateCustomLocationNameInputDTO>()));
            }
        }

        [TestClass]
        public class DeleteCustomLocation : CustomLocationControllerTests
        {
            [TestMethod]
            public void ShouldValidateParameters()
            {
                // Arrange
                var parameters = typeof(CustomLocationController).GetMethod(nameof(CustomLocationController.DeleteCustomLocation))
                    .GetParameters().ToDictionary(p => p.Name, p => p);
                var id = parameters["id"].CustomAttributes;
                var facility = parameters["facility"].CustomAttributes;

                // Assert
                Assert.IsTrue(id.Any(a => a.AttributeType == typeof(RequiredAttribute)),
                    "id is not required");
                Assert.IsTrue(facility.Any(a => a.AttributeType == typeof(RequiredAttribute)),
                    "facility is not required");
            }

            [TestMethod]
            public async Task CallRepositoryWhenInputIsProvided()
            {
                // Arrange
                var mockRepository = new Mock<ICustomLocationRepository>();
                var subject = new CustomLocationController(mockRepository.Object,
                    this.HttpContextAccessorMock.Object);

                var id = Guid.NewGuid();
                var facility = "facility";

                // Act
                // ReSharper disable once UnusedVariable
                var result = await subject.DeleteCustomLocation(id, facility);

                // Assert
                mockRepository.Verify(mock => mock.DeleteCustomLocationAsync(id, facility));
            }
        }
    }
}
