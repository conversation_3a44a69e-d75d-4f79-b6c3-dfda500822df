namespace SafetySurveillance.WebAPI.UnitTests.Repositories.Authorization
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;

    using Microsoft.EntityFrameworkCore;
    using Microsoft.VisualStudio.TestTools.UnitTesting;

    using MockQueryable.Moq;

    using Moq;

    using SafetySurveillanceWebApi.Data;
    using SafetySurveillanceWebApi.Models.Authorize;
    using SafetySurveillanceWebApi.Repositories.Authorization;
    using SafetySurveillanceWebApi.Services.Authorization;

    [TestClass]
    class AuthorizationRepositoryTests
    {
        private AuthorizationRepository subject;
        private Mock<DbSet<SafetyUser>> users;
        private Mock<DbSet<Group>> groups;
        private Mock<DbSet<UserPermission>> userPermissions;
        private Mock<DbSet<Permission>> permissions;
        private Mock<DbSet<GroupUser>> groupUsers;
        private Mock<DbSet<GroupPermissions>> groupPermissions;
        private string tarzanUser = "<PERSON>@TheJungle";
        private string janeUser = "<PERSON>@IntoTheJungle";
        private string peterUser = "<PERSON>@Neverland";

        private Guid permissions1Id = Guid.NewGuid();
        private Guid permissions2Id = Guid.NewGuid();
        private Guid permissions3Id = Guid.NewGuid();

        //private string permission1Id = ;
        //private string permission2Id

        [TestInitialize]
        public void PermissionServiceTestsInitialize()
        {
            var group1Guid = Guid.NewGuid();
            var group2Guid = Guid.NewGuid();
            var group3Guid = Guid.NewGuid();

            this.users = new List<SafetyUser>()
                             {
                                 new SafetyUser()
                                     {
                                         UserName = this.tarzanUser,
                                         CreatedBy = "LostParents@NotTheJungle",
                                         CreatedDateTime = new DateTime(),
                                         DisplayName = "TarzanTheGreat",
                                         Email = "<EMAIL>",
                                         IsDeleted = false
                                     },
                                 new SafetyUser()
                                     {
                                         UserName = this.janeUser,
                                         CreatedBy = "JungleExpediction@NotTheJungle",
                                         CreatedDateTime = new DateTime(),
                                         DisplayName = "JaneTheGreater",
                                         Email = "<EMAIL>",
                                         IsDeleted = false
                                     },
                                 new SafetyUser()
                                     {
                                         UserName = this.peterUser,
                                         CreatedBy = "Cap'n@Hook",
                                         CreatedDateTime = new DateTime(),
                                         DisplayName = "FlyingPeter",
                                         Email = "<EMAIL>",
                                         IsDeleted = true
                                     }
                             }.AsQueryable().BuildMockDbSet();

            this.groups = new List<Group>()
                              {
                                  new Group()
                                      {
                                          GroupId = group1Guid, GroupName = "The Great Apes", IsDeleted = false,
                                      },
                                  new Group()
                                      {
                                          GroupId = group2Guid, GroupName = "The Lost Boys", IsDeleted = true,
                                      },
                                  new Group()
                                      {
                                          GroupId = group3Guid, GroupName = "Welcome to the Jungle", IsDeleted = false,
                                      }
                              }.AsQueryable().BuildMockDbSet();

            this.permissions = new List<Permission>()
                                   {
                                       new Permission()
                                           {
                                               IsDeleted = false, Name = "Swing From Trees", PermissionId = this.permissions1Id
                                           },
                                       new Permission()
                                           {
                                               IsDeleted = false, Name = "Jungle Excursions", PermissionId = this.permissions2Id
                                           },
                                       new Permission()
                                           {
                                               IsDeleted = true, Name = "You Can Fly", PermissionId = this.permissions3Id
                                           }
                                   }.AsQueryable().BuildMockDbSet();
            this.userPermissions = new List<UserPermission>()
                                       {
                                           new UserPermission()
                                               {
                                                   Id = 1,
                                                   PermissionId = this.permissions2Id,
                                                   UserName = this.tarzanUser,
                                                   Permissions = this.permissions.Object.FirstOrDefault(p => p.PermissionId == this.permissions2Id)
                                               },
                                           new UserPermission()
                                               {
                                                   Id = 2,
                                                   PermissionId = this.permissions2Id,
                                                   UserName = this.tarzanUser,
                                                   Permissions = this.permissions.Object.FirstOrDefault(p => p.PermissionId == this.permissions2Id)
                                               },
                                           new UserPermission()
                                               {
                                                   Id = 3,
                                                   PermissionId = this.permissions3Id,
                                                   UserName = this.tarzanUser,
                                                   Permissions = this.permissions.Object.FirstOrDefault(p => p.PermissionId == this.permissions3Id)
                                               },
                                           new UserPermission()
                                               {
                                                   Id = 4,
                                                   PermissionId = this.permissions1Id,
                                                   UserName = this.janeUser,
                                                   Permissions = this.permissions.Object.FirstOrDefault(p => p.PermissionId == this.permissions1Id)
                                               },
                                           new UserPermission()
                                               {
                                                   Id = 5,
                                                   PermissionId = this.permissions1Id,
                                                   UserName = this.tarzanUser,
                                                   Permissions = this.permissions.Object.FirstOrDefault(p => p.PermissionId == this.permissions1Id)
                                               },
                                       }.AsQueryable().BuildMockDbSet();
            this.groupUsers = new List<GroupUser>()
                                  {
                                      new GroupUser() { Id = 1, GroupId = group1Guid, UserName = this.tarzanUser },
                                      new GroupUser() { Id = 2, GroupId = group2Guid, UserName = this.tarzanUser },
                                      new GroupUser() { Id = 3, GroupId = group3Guid, UserName = this.tarzanUser },
                                      new GroupUser() { Id = 4, GroupId = group3Guid, UserName = this.janeUser },
                                      new GroupUser() { Id = 5, GroupId = group2Guid, UserName = this.peterUser }
                                  }.AsQueryable().BuildMockDbSet();
            this.groupPermissions = new List<GroupPermissions>()
                                        {
                                            new GroupPermissions()
                                                {
                                                    Id = 1,
                                                    GroupId = group2Guid,
                                                    PermissionId = this.permissions3Id,
                                                    Permission = this.permissions.Object.FirstOrDefault(p => p.PermissionId == this.permissions3Id)
                                                },
                                            new GroupPermissions()
                                                {
                                                    Id = 2,
                                                    GroupId = group3Guid,
                                                    PermissionId = this.permissions2Id,
                                                    Permission = this.permissions.Object.FirstOrDefault(p => p.PermissionId == this.permissions2Id)
                                                },
                                        }.AsQueryable().BuildMockDbSet();
        }

        [TestClass]
        public class GetUserByUserIdAsync : AuthorizationRepositoryTests
        {
            [TestMethod]
            public void ReturnsUserWithUserId()
            {
                // arrange
                var testId = this.tarzanUser;
                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(p => p.AuthorizeUsers).Returns(this.users.Object);
                var expectedResult = this.users.Object.FirstOrDefault(p => p.UserName == testId);

                this.subject = new AuthorizationRepository(mockContext.Object);

                // act
                var result = this.subject.GetUserByUserIdAsync(testId);

                // assert
                Assert.AreEqual(expectedResult, result.Result);
            }
        }

        [TestClass]
        public class GetUserPermissionsAsync : AuthorizationRepositoryTests
        {
            [TestMethod]
            public void ReturnsWithUserPermissions()
            {
                // arrange
                var testId = this.tarzanUser;
                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(p => p.AuthorizeUserPermissions).Returns(this.userPermissions.Object);
                var expectedResult = this.userPermissions.Object.Where(p => p.UserName == testId).ToList();

                this.subject = new AuthorizationRepository(mockContext.Object);

                // act
                var result = this.subject.GetUserPermissionsAsync(testId);

                // assert
                Assert.AreEqual(expectedResult.Count(), result.Result.Count());
            }
        }

        [Ignore]
        [TestClass]
        public class GetGroupPermissionsByUserIdAsync : AuthorizationRepositoryTests
        {
            [TestMethod]
            public void ShouldReturnGroupPermissionsWhenGivenUserId()
            {
                // arrange
                var testId = this.tarzanUser;
                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(p => p.AuthorizeGroupUsers).Returns(this.groupUsers.Object);
                mockContext.SetupGet(p => p.AuthorizeGroupPermissions).Returns(this.groupPermissions.Object);
                var userGroups = this.groupUsers.Object.Where(gu => gu.UserName == testId).Select(g => g.GroupId).ToList();
                var expectedResult = this.groupPermissions.Object.Where(p => userGroups.Contains(p.GroupId)).ToList();

                this.subject = new AuthorizationRepository(mockContext.Object);

                // act
                var result = this.subject.GetGroupPermissionsByUserIdAsync(testId);

                // assert
                Assert.AreEqual(expectedResult.Count(), result.Result.Count());
            }
        }
    }
}
