using AutoMapper;
using PSM.Common.Service;
using SafetySurveillanceWebApi.Configuration;
using SafetySurveillanceWebApi.Infrastructure;
using SafetySurveillanceWebApi.Models.Survey;
using SafetySurveillanceWebApi.Services;

namespace SafetySurveillance.WebAPI.UnitTests.Repositories.Incidents
{
    using System;
    using System.Collections.Generic;
    using System.Linq;
    using System.Threading.Tasks;
    using AutoFixture;
    using Microsoft.EntityFrameworkCore;
    using Microsoft.VisualStudio.TestTools.UnitTesting;
    using MockQueryable.Moq;
    using Moq;
    using SafetySurveillanceWebApi.Data;
    using SafetySurveillanceWebApi.Models;
    using SafetySurveillanceWebApi.Models.Authorize;
    using SafetySurveillanceWebApi.Repositories;
    using SafetySurveillanceWebApi.Repositories.Incident;

    [TestClass]
    public class IncidentsRepositoryTests
    {
        private IncidentsRepository incidentsRepository;

        private Fixture fixture;

        [TestInitialize]
        public void SetUp()
        {
            CacheExtensions.UseMock();
            this.fixture = new Fixture();
            fixture.Behaviors.OfType<ThrowingRecursionBehavior>().ToList()
                .ForEach(b => fixture.Behaviors.Remove(b));
            fixture.Behaviors.Add(new OmitOnRecursionBehavior());
        }

        [TestCleanup]
        public void Cleanup()
        {
            CacheExtensions.UseDefault();
        }

        [TestClass]
        public class GetAllIncidents : IncidentsRepositoryTests
        {
            [TestMethod]
            public async Task ShouldReturnValidIncidentsForAFacility()
            {
                var facility = "LSNFN";

                // Arrange
                var response = fixture.Build<Response>()
                    .With(x => x.AssociatedPersonResponse,
                        fixture.Build<AssociatedPerson>().With(y => y.Location, facility).Create())
                    .Create();

                var records = this.fixture.Build<SurveyEvent>()
                    .With(x => x.Responses, new List<Response>() { response })
                    .With(x => x.EncounterSource, "IncidentSubmit")
                    .With(x => x.IsDeleted, false)
                    .CreateMany(3)
                    .ToList();

                var responses = new List<Response>() {response}.AsQueryable().BuildMockDbSet();

                var surveyEvents = records
                    .AsQueryable()
                    .BuildMockDbSet();

                var mockContext = new Mock<ISafetySurveillanceApplicationContext>();
                mockContext.SetupGet(c => c.SurveyEvents).Returns(surveyEvents.Object);
                mockContext.SetupGet(c => c.Responses).Returns(responses.Object);
                var mockTriggerMatchService = new Mock<ITriggerMatchService>();
                var mockTriggerReviewRepository = new Mock<ITriggerReviewRepository>();
                var mockEncryptionService = new Mock<IEncryptionService>();
                var mockAppConfiguration = new Mock<IAppConfiguration>();
                var mockAutomapper = new Mock<IMapper>();
                this.incidentsRepository = new IncidentsRepository(mockContext.Object, mockTriggerMatchService.Object, mockTriggerReviewRepository.Object, mockEncryptionService.Object,  mockAutomapper.Object, mockAppConfiguration.Object);

                // Act
                var result = await this.incidentsRepository.GetAllIncidents(facility);

                // Assert
                Assert.IsNotNull(result);
                Assert.AreEqual(result.Count, records.Count);
            }
        }
    }
}
