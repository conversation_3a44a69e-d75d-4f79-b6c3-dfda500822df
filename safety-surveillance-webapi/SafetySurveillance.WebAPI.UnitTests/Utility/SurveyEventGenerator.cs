namespace SafetySurveillance.WebAPI.UnitTests.Utility
{
    using SafetySurveillanceWebApi.Models;
    using SafetySurveillanceWebApi.Models.Constants;
    using System;
    using System.Collections.Generic;
    using System.Linq;

    public static class SurveyEventGenerator
    {
        public static SurveyEvent AddQuestionsToSurveyForm(List<string> questionText)
        {
            // these are special columns that are not questions.
            var questions = questionText.Where(p => p != "Comments")
                                        .Where(p => p != "SurveyDate")
                                        .ToList();

            var result = new SurveyEvent()
            {
                //FormId = Guid.NewGuid(),
                Form = new Form()
                {
                    Sections = new List<Section>()
                        {
                            new Section()
                            {
                                 Questions = new List<Question>()
                            },
                            new Section()
                            {
                                 Questions = new List<Question>()
                            }
                        }
                },
                Responses = new List<Response>(),
                Comments = new List<SurveyEventComment>(),
                IncidentReviewerComments = new List<TriggerEventComment>(),
            };

            for (var i = 0; i < questions.Count; i++)
            {
                // half the questions are on form 1 and half on form 2.
                // this tests that it shouldnt matter
                result.Form.Sections[i % 2].Questions.Add(
                        new Question()
                        {
                            Order = i,
                            QuestionText = questionText[i],
                            IsActive = true
                        });
            }

            return result;
        }

        public static SurveyEvent CreateResponseForAllQuestionsBasedOnType(SurveyEvent surveyEvent, string questionType, int responseType = 0)
        {
            foreach (var section in surveyEvent.Form.Sections)
            {
                foreach (var question in section.Questions)
                {
                    Guid id = new Guid();
                    question.QuestionId = id;
                    question.QuestionType = new QuestionType()
                    {
                        Type = questionType
                    };
                    var response = SurveyEventGenerator.MakeRandomResponse(question, questionType, responseType);
                    if(surveyEvent.Responses == null)
                    {
                        surveyEvent.Responses = new List<Response>();
                    }
                    surveyEvent.Responses.Add(response);
                }
            }

            return surveyEvent;
        }

        public static Response MakeRandomResponse(Question question, string questionType, int responseType)
        {
            switch (questionType)
            {
                case SurveyQuestionType.DichotomousWhy:
                    return new Response()
                    {
                        ResponseText = RandomExtension.RandomString(10),
                        AdditionalText = responseType == 1 ? RandomExtension.RandomString(10) : null
                    };
                case SurveyQuestionType.Date:
                    return new Response()
                    {
                        ResponseText = RandomExtension.RandomDate(DateTime.Now.AddDays(-5), DateTime.Now).ToString()
                    };

                case SurveyQuestionType.CheckBox:
                case SurveyQuestionType.TextArea:
                case SurveyQuestionType.Dichotomous:
                case SurveyQuestionType.DichotomousNA:
                case SurveyQuestionType.Text:
                case SurveyQuestionType.SingleValuePreSelect:
                case SurveyQuestionType.SingleValuePreRadio:
                    return new Response()
                    {
                        ResponseText = RandomExtension.RandomString(10)
                    };

                case SurveyQuestionType.SelectRadioWhy:
                case SurveyQuestionType.SelectRadio:
                case SurveyQuestionType.CheckboxListOther:
                    return SurveyEventGenerator.GetCheckboxListOther(question, responseType);
                case SurveyQuestionType.CheckboxListNoneAbove:
                    return SurveyEventGenerator.GetCheckBoxListNoneAbove(question, responseType);
                case SurveyQuestionType.CheckboxList:
                    return SurveyEventGenerator.GetCheckBoxListNoneAbove(question, 1);
                case SurveyQuestionType.MultipleChoice:
                    return new Response()
                    {
                        ResponseOptionId = null,
                        ResponseOption = new QuestionOption()
                        {
                            OptionText = RandomExtension.RandomString(10)
                        }
                    };
                case SurveyQuestionType.MultiSelect:
                case SurveyQuestionType.MultiSelectText:
                    return SurveyEventGenerator.GetMultiSelectText(question, responseType);
                case SurveyQuestionType.MultipleChoiceText:
                    return SurveyEventGenerator.GetMultipleChoiceText(question, responseType);
                case SurveyQuestionType.DependantMultipleChoice:
                    return SurveyEventGenerator.GetDependantMultipleChoice(question, responseType);
                case SurveyQuestionType.ComplexSubMultipleChoice:
                    return SurveyEventGenerator.GetComplexSubMultipleChoice(question, responseType);
                default:
                    throw new Exception("Question Type response doesnt exist");
            }
        }

        private static Response GetMultiSelectText(Question question, int responseType)
        {
            switch (responseType)
            {
                case 0:
                    return new Response()
                    {
                        DeviceResponses = new List<DeviceMultiSelectResponse>(),
                        ResponseText = RandomExtension.RandomString(10)
                    };
                case 1:
                    return new Response()
                    {
                        DeviceResponses = new List<DeviceMultiSelectResponse>()
                        {
                            new DeviceMultiSelectResponse()
                            {
                                 Device = new Device()
                                 {
                                      DeviceDsc = RandomExtension.RandomString(10),
                                      DeviceId = RandomExtension.RandomInt()
                                 }
                            },
                            new DeviceMultiSelectResponse()
                            {
                                 Device = new Device()
                                 {
                                      DeviceDsc = RandomExtension.RandomString(10),
                                      DeviceId = RandomExtension.RandomInt()
                                 }
                            }
                        },
                    };
                case 2:
                    return new Response()
                    {
                        DeviceResponses = new List<DeviceMultiSelectResponse>()
                        {
                            new DeviceMultiSelectResponse()
                            {
                                 Device = new Device()
                                 {
                                      DeviceDsc = RandomExtension.RandomString(10),
                                      DeviceId = RandomExtension.RandomInt()
                                 }
                            },
                            new DeviceMultiSelectResponse()
                            {
                                 Device = new Device()
                                 {
                                      DeviceDsc = RandomExtension.RandomString(10),
                                      DeviceId = RandomExtension.RandomInt()
                                 }
                            }
                        },
                        ResponseText = RandomExtension.RandomString(10)
                    };
                default:
                    throw new Exception("Response type doesnt exist");
            }
        }

        private static Response GetCheckboxListOther(Question question, int responseType)
        {
            switch (responseType)
            {
                case 0:
                    return new Response()
                    {
                        AdditionalText = RandomExtension.RandomString(10)
                    };
                case 1:
                    var result = new Response()
                    {
                        AdditionalText = null,
                        ResponseOptionId = null,
                        Question = question
                    };
                    result.Question.Options = new List<QuestionOption>()
                        {
                            new QuestionOption()
                            {
                                QuestionOptionId = Guid.NewGuid(),
                                OptionText = RandomExtension.RandomString(10),
                            },
                            new QuestionOption()
                            {
                                QuestionOptionId = Guid.NewGuid(),
                                OptionText = RandomExtension.RandomString(10),
                            }
                        };
                    result.ResponseText = string.Join(",", result.Question.Options.Select(p => p.QuestionOptionId)) + ",";
                    return result;
                case 2:
                    var result2 = new Response()
                    {
                        AdditionalText = RandomExtension.RandomString(10),
                        ResponseOptionId = null,
                        Question = question
                    };
                    result2.Question.Options = new List<QuestionOption>()
                        {
                            new QuestionOption()
                            {
                                QuestionOptionId = Guid.NewGuid(),
                                OptionText = RandomExtension.RandomString(10),
                            },
                            new QuestionOption()
                            {
                                QuestionOptionId = Guid.NewGuid(),
                                OptionText = RandomExtension.RandomString(10),
                            }
                        };
                    result2.ResponseText = string.Join(",", result2.Question.Options.Select(p => p.QuestionOptionId)) + ",";
                    return result2;
                default:
                    throw new Exception("Response type doesnt exist");
            }
        }

        public static Response GetDependantMultipleChoice(Question question, int responseType)
        {
            switch (responseType)
            {
                case 0:
                    return new Response()
                    {
                        ResponseOption = new QuestionOption()
                        {
                            OptionText = RandomExtension.RandomString(10)
                        }
                    };
                case 1:
                    return new Response()
                    {
                        ResponseOption = new QuestionOption()
                        {
                            OptionText = RandomExtension.RandomString(10),
                            DependentQuestionOption = new QuestionOption()
                            {
                                OptionText = RandomExtension.RandomString(10)
                            }
                        }
                    };
                default:
                    throw new Exception("Response type doesnt exist");
            }
        }

        public static Response GetComplexSubMultipleChoice(Question question, int responseType)
        {
            switch (responseType)
            {
                case 0:
                    return new Response()
                    {
                        ResponseOption = null,
                        ResponseText = RandomExtension.RandomString(10)
                    };
                case 1:
                    return new Response()
                    {
                        ResponseText = null,
                        ResponseOption = new QuestionOption()
                        {
                            OptionText = RandomExtension.RandomString(10)
                        }
                    };
                default:
                    throw new Exception("Response type doesnt exist");
            }
        }

        public static Response GetMultipleChoiceText(Question question, int responseType)
        {
            switch (responseType)
            {
                case 0:
                    return new Response()
                    {
                        ResponseOption = null,
                        ResponseText = RandomExtension.RandomString(10)
                    };
                case 1:
                    return new Response()
                    {
                        ResponseOption = new QuestionOption()
                        {
                            OptionText = RandomExtension.RandomString(10)
                        }
                    };
                default:
                    throw new Exception("Response type doesnt exist");
            }
        }

        public static Response GetCheckBoxListNoneAbove(Question question, int responseType)
        {
            switch (responseType)
            {
                case 0:
                    return new Response()
                    {
                        ResponseOptionId = new Guid(),
                        ResponseText = RandomExtension.RandomString(10)
                    };
                case 1:
                    var result = new Response()
                    {
                        ResponseOptionId = null,
                        Question = question
                    };
                    result.Question.Options = new List<QuestionOption>()
                        {
                            new QuestionOption()
                            {
                                QuestionOptionId = Guid.NewGuid(),
                                OptionText = RandomExtension.RandomString(10),
                            },
                            new QuestionOption()
                            {
                                QuestionOptionId = Guid.NewGuid(),
                                OptionText = RandomExtension.RandomString(10),
                            }
                        };
                    result.ResponseText = string.Join(",", result.Question.Options.Select(p => p.QuestionOptionId)) + ",";
                    return result;
                default:
                    throw new Exception("Response type doesnt exist");
            }
        }

        public static SurveyEvent CreateResponseForSubQuestions(SurveyEvent surveyEventObject, List<string> subQuestions, string questionType, int numOfChildForms)
        {
            surveyEventObject = SurveyEventGenerator.CreateResponseForAllQuestionsBasedOnType(surveyEventObject, questionType, 0);
            surveyEventObject.ChildSurveyEvents = new List<SurveyEvent>();
            surveyEventObject.Form.ChildForms = new List<Form>();
            for (int j = 0; j < numOfChildForms; j++)
            {
                var surveyEvent = SurveyEventGenerator.AddQuestionsToSurveyForm(subQuestions);
                surveyEvent = SurveyEventGenerator.CreateResponseForAllQuestionsBasedOnType(surveyEvent, questionType);
                surveyEvent.ParentSurveyEventId = surveyEventObject.SurveyEventId;
                surveyEvent.ParentSurveyEvent = surveyEventObject;
                surveyEventObject.ChildSurveyEvents.Add(surveyEvent);

                if (!surveyEventObject.Form.ChildForms.Any(p => p.FormId == surveyEvent.FormId))
                {
                    surveyEventObject.Form.ChildForms.Add(surveyEvent.Form);
                }
            }

            return surveyEventObject;
        }
    }
}
