using PSM.Common.Service.Constants;

namespace PSM.Notifier.Services.NotificationTypes
{
    using Microsoft.EntityFrameworkCore;

    using PSM.Notifier.Constants;
    using PSM.Notifier.Data;
    using PSM.Notifier.Models.Database.Notification;
    using PSM.Notifier.Services.NotificationMethods;

    public class ReleaseEventService : INotifier, IEmailNotifier
    {
        private NotificationType notificationType;
        private DateTime latestReleaseDateTime;

        public ReleaseEventService()
        {
            this.notificationType = null;
        }

        public IPatientSafetyContext PatientSafetyContext { get; set; }

        public async Task<bool> HasEventsToNotify()
        {
            this.notificationType = await this.PatientSafetyContext.NotificationTypes
                                        .FirstAsync(p => p.Name == NotificationTypeName.Release);

            var latestReleaseDateString = await this.PatientSafetyContext.AppConfigs.Where(c => c.Name == NotificationConstants.ReleaseDateAppConfigKey)
                                                    .FirstOrDefaultAsync();

            if (!string.IsNullOrEmpty(latestReleaseDateString.Value))
            {
                this.latestReleaseDateTime = DateTime.Parse(latestReleaseDateString.Value);
            }
            
            // check if PSM release date is more recent than last run of this notifier check
            bool hasEventsToNotify = this.latestReleaseDateTime > this.notificationType.LastUpdatedDts;
            this.notificationType.LastUpdatedDts = DateTime.Now;

            if (!hasEventsToNotify)
            {
                // save last updated timestamp if no other work needs to be done
                await this.PatientSafetyContext.SaveChangesAsync();
            }

            return hasEventsToNotify;
        }

        public async Task NotifyViaEmail()
        {
            await Task.CompletedTask;
            // throw new System.NotImplementedException();
        }

        public async Task NotifyViaNotification()
        {
            await this.ClearPreviousReleaseEvents();
            await this.NotifyReleaseEvents();

            await this.PatientSafetyContext.SaveChangesAsync();
        }

        private async Task ClearPreviousReleaseEvents()
        {
            // get all unread release notifications and set to read
            var previousUnreadReleaseNotifications = await this.PatientSafetyContext.Notifications
                                                        .Where(n => n.NotificationTypeId == this.notificationType.TypeId && n.IsNew)
                                                        .ToListAsync();

            foreach (var previousNotification in previousUnreadReleaseNotifications)
            {
                previousNotification.IsNew = false;
            }
        }

        private async Task NotifyReleaseEvents()
        {
            var allUsers = await this.PatientSafetyContext.AuthorizeUsers.Where(u => !u.IsDeleted).ToListAsync();

            foreach (var user in allUsers)
            {
                var newNotification = new Notification()
                {
                    IsNew = true,
                    CreatedDateTime = DateTime.Now,
                    NotificationMethodText = NotificationConstants.NotificationMethod,
                    NotificationTypeId = this.notificationType.TypeId,
                    UserId = user.UserName,
                    NotificationText = string.Empty
                };
                this.PatientSafetyContext.Notifications.Add(newNotification);
            }            
        }
    }
}
